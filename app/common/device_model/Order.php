<?php
/** @noinspection PhpUnused */

namespace app\common\device_model;


use app\common\cache\redis\entity\AdminLoginUser;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\db\Query;
use think\facade\Db;
use think\Model;

class Order extends Model
{
    protected $connection = 'device';
    protected $table = 'order';

    public function getMonitorData($centralized_id){
        $data = Db::connect('device')->query('SELECT
	( `omd`.`output_voltage` / 10 ) * ( `omd`.`output_current` / 10 ) AS power,
	`oapl`.`power` AS planned_power,
	o.`piles_id` ,
	`o`.`shots_number`,
	CONCAT(o.`piles_id`, 0, o.`shots_number`) AS shots_id
FROM
	`order_monitoring_data` AS `omd`
LEFT JOIN `order_allocate_power_logs` `oapl` ON `omd`.`allocate_power_log_id` = `oapl`.`id` 
LEFT JOIN `order` `o` ON `o`.`id` = `omd`.`order_id`
WHERE
	`o`.`centralized_controller_id` = ? AND `o`.status = 1 AND `omd`.`id` = (SELECT max(id) FROM `order_monitoring_data` WHERE order_id = `o`.id)
ORDER BY
	`o`.`create_time` desc',[$centralized_id]);
        return $data;
    }

}