<?php

namespace app\common\logic\common\coupon_activity;

use app\common\log\LogCollector;
use app\common\logic\common\coupon_activity\entity\UseCouponRequest;
use app\common\model\CouponCollection;
use app\common\model\CouponCollectionOrder;

class UseCoupon
{
    protected UseCouponRequest $request;

    public function __construct(UseCouponRequest $request)
    {
        $this->request = $request;
    }

    /**
     * @return int 打完折扣后的服务费金额(单位:分)，不管有没有打折扣，当没有打扣时返回的就是原先传入服务费用。
     */
    public function run(): array
    {
        LogCollector::collectorRunLog(sprintf("请求: %d", json_encode_cn($this->request->toArray())));

        // 给用户的优惠券加上排他锁(防止同一用户两笔或多笔订单同时使用同一张优惠券)。
        // 当用户查询自己优惠券耗时表示长时，就需要考虑是不是这个锁造成的了。
        // 注意：需要结合事务才会有效果，如果没有开启事务，那么在加锁成功之后，立马就又会解锁了。
        $lockRow = $this->lockUserCoupon($this->request->user_id);
        LogCollector::collectorRunLog(sprintf("锁住的行数: %d", $lockRow));

        // 查询出所有符合指定场站使用的优惠券
        $coupons = $this->getStationEffectiveCoupon($this->request->user_id, $this->request->station_id);
        LogCollector::collectorRunLog(sprintf("查询出所有符合指定场站使用的优惠券: %s", json_encode_cn($coupons)));

        // 对优惠券进行排序: 面值大的靠前 并且 快要过期的靠前
        $coupons = $this->sortByParValueDESCAndExpireTimeASC($coupons);
        LogCollector::collectorRunLog(sprintf("对优惠券进行排序: %s", json_encode_cn($coupons)));

        // 在这些优惠券里找出能使用的最大优惠的优惠券
        $use_coupon = $this->findCanUseMaxDiscountCoupon($coupons, $this->request->service_cost);
        LogCollector::collectorRunLog(sprintf("在这些优惠券里找出能使用的最大优惠的优惠券: %s", json_encode_cn($use_coupon)));

        if (!empty($use_coupon)) {
            // 在原来充电费用的基础上，免除优惠券减免的金额
            $new_service_cost = $this->discount($this->request->service_cost, $use_coupon);
            LogCollector::collectorRunLog(sprintf("免除优惠券减免的金额: %s", $new_service_cost));

            // 将指定优惠券标记为已使用
            $this->couponStatusUpdateUsed($use_coupon['id']);

            // 将指定优惠券与当前订单建立关系
            $this->recordUseRelation($this->request->order_id, $use_coupon['id']);

            // 返回最新充电费用
            return [
                'service_cost' => $new_service_cost,
                'use_coupon' => $use_coupon,
            ];
        }

        return [
            'service_cost' => $this->request->service_cost,
            'use_coupon' => null
        ];
    }

    protected function lockUserCoupon(int $user_id): int
    {
        return app(CouponCollection::class)->lockUserCoupon($user_id);
    }

    protected function recordUseRelation(string $order_id, int $coupon_id): int
    {
        return app(CouponCollectionOrder::class)->recordUseRelation($order_id, $coupon_id);
    }

    /**
     * 将指定优惠券标记为已使用
     *
     * @param int $coupon_id 优惠券ID
     * @return bool
     */
    protected function couponStatusUpdateUsed(int $coupon_id): bool
    {
        return app(CouponCollection::class)->useCoupon($coupon_id);
    }

    /**
     * 给服务费打折扣
     *
     * @param int $service_cost
     * @param array $use_coupon
     * @return int 打折扣后的服务费用
     */
    protected function discount(int $service_cost, array $use_coupon): int
    {
        // 备注：因为单位不同，优惠券面值的单位是角，服务费的单位是分。
        $new_service_cost = $service_cost - ($use_coupon['par_value'] * 10);
        // 打完折扣的金额不能低于0
        return max($new_service_cost, 0);
    }

    protected function sortByParValueDESCAndExpireTimeASC(array $coupons): array
    {
        usort($coupons, function ($coupon1, $coupon2) {
            // 如果返回值小于 0，则第一个参数会被排序到第二个参数之前。
            // 如果返回值等于 0，则认为两个参数相等，它们的顺序不变。
            // 如果返回值大于 0，则第一个参数会被排序到第二个参数之后。
            if ($coupon1['par_value'] === $coupon2['par_value']) {
                return strtotime($coupon1['expire_time']) - strtotime($coupon2['expire_time']);
            } else {
                if ($coupon1['par_value'] > $coupon2['par_value']) {
                    return -1;
                } else {
                    return 1;
                }
            }
        });

        return $coupons;
    }

    /**
     * @param array $coupons 优惠券集合
     * @param int $service_cost 本次充电的服务费用(单位:分)
     * @return array|null 为null时表示没有合适的优惠券可以使用
     */
    protected function findCanUseMaxDiscountCoupon(array $coupons, int $service_cost): ?array
    {
        foreach ($coupons as $coupon) {
            // 当 服务费用 高于等于 满减金额
            // 备注：usage_amount 的单位是角，而 service_cost 的单位是分，所以需要乘以10。
            if ($service_cost >= $coupon['usage_amount'] * 10) {
                return $coupon;
            }
        }

        return null;
    }


    protected function getStationEffectiveCoupon(int $user_id, int $station_id): array
    {
        return app(CouponCollection::class)->getStationEffectiveCoupon($user_id, $station_id);
    }
}