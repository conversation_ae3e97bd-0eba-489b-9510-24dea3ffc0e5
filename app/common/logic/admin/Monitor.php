<?php

namespace app\common\logic\admin;

use app\common\cache\redis\entity\AdminLoginUser;
use app\common\device_model\Order;
use think\facade\Db;
use think\Request;

class Monitor
{
    protected AdminLoginUser $loginUser;
    protected Request $request;

    public function __construct(AdminLoginUser $loginUser, Request $request)
    {
        $this->loginUser = $loginUser;
        $this->request = $request;
    }

    public function run()
    {
        $centralized_id = $this->request->post('centralized_id');
        $order = new Order();
        $data = $order->getMonitorData($centralized_id);
        $result['piles'] = $data;
        // 遍历数据并计算总和
        foreach ($data as &$item) {
            $item['total_power'] = (float)$item['planned_power'] + (float)$item['power'];
        }
        $config = Db::connect('device')->table('centralized_controller_config')->where('id', $centralized_id)->find();
        $result['all'] = [
            'all_power' => $config['transformer_capacity'],
            'planned_power' => array_sum(array_column($data, 'planned_power')),
            'power' => array_sum(array_column($data, 'power')),
        ];
        return $result;
    }

}