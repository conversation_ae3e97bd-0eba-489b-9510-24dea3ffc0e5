<?php
/** @noinspection PhpDynamicAsStaticMethodCallInspection */

namespace app\common\logic\admin\refund_order;

use app\common\cache\redis\entity\AdminLoginUser;
use app\common\lib\exception\RuntimeException;
use app\common\lib\SendApplet;
use app\common\lib\VerifyData;
use app\common\model\File;
use app\common\model\PayOrder as PayOrderModel;
use app\common\model\RefundOrder as RefundOrderModel;
use app\ms\Api;
use Respect\Validation\Validator as v;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Log;
use think\Request;

class RefundSuccess
{
    protected Request $request;
    protected AdminLoginUser $loginUser;

    public function __construct(AdminLoginUser $loginUser, Request $request)
    {
        $this->request = $request;
        $this->loginUser = $loginUser;
    }

    /**
     * @throws ModelNotFoundException
     * @throws DataNotFoundException
     * @throws DbException
     */
    public function run(): array
    {
        // 验证操作权限
        if ($this->loginUser->corp_id > 0) {
            throw new RuntimeException('没有权限', [], RuntimeException::CodeBusinessException);
        }
        // 验证参数
        $params = $this->verifyParams($this->request->post());
        // 验证退款截图
        $refund_screenshot = $this->verifyRefundScreenshot($params['refund_screenshot_id'], $this->loginUser->id);
        $params['refund_image'] = $refund_screenshot['url'];
        $res = Api::send('/order/pay/refund/success', 'POST', $params);
        if ($res['code'] !== 200) {
            throw new RuntimeException($res['msg'], [], RuntimeException::CodeBusinessException);
        }
        // 发送通知给小程序
        $this->sendNoticeToApplet($res['data']['user_id']);

        return [];
    }

    protected function verifyRefundScreenshot(int $refund_screenshot_id, int $user_id): array
    {
        // 查询退款截图是否存在
        $model = app(File::class);
        // 获取退款截图完整信息
        $refund_screenshot = $model->where('id', $refund_screenshot_id)->find();
        if (empty($refund_screenshot)) {
            throw new RuntimeException('无效退款截图ID', [], RuntimeException::CodeBusinessException);
        }
        return $refund_screenshot->toArray();
    }

    protected function verifyParams(array $params): array
    {
        return v::input($params, VerifyData::pay_order([
            'refund_id',
            'refund_screenshot_id'
        ]));
    }


    protected function sendNoticeToApplet(int $user_id): bool
    {
        try {
            return SendApplet::send_uid($user_id, [
                'type' => 'refund_notice',
                'result' => 'success',
                'msg' => '退款成功',
                'refund_status' => 'SUCCESS',
            ]);
        }catch (\Exception $e) {
            Log::error('发送退款通知失败：' . $e->getMessage());
            return false;
        }
    }

}