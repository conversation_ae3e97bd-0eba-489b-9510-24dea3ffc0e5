<?php

namespace app\common\logic\admin\coupon_collection;

use app\common\cache\redis\entity\AdminLoginUser;
use app\common\logic\admin\coupon_collection\entity\CouponCollectionListRequest;
use app\common\model\CouponCollection;

class CouponCollectionList
{
    protected CouponCollectionListRequest $request;
    protected AdminLoginUser $loginUser;

    public const SortFieldsID = 1; // 优惠券ID
    public const SortFieldsCreateTime = 2; // 领取时间
    public const SortFieldsOptions = [
        ['value' => self::SortFieldsID, 'label' => '优惠券ID'],
        ['value' => self::SortFieldsCreateTime, 'label' => '领取时间'],
    ];
    public const SortFieldsMap = [
        self::SortFieldsID => 'cc.id',
        self::SortFieldsCreateTime => 'cc.create_time',
    ];

    // 排序方式
    public const SortTypeAsc = 2; // 升序
    public const SortTypeDesc = 1; // 降序
    public const SortTypeMap = [
        self::SortTypeAsc => 'ASC',
        self::SortTypeDesc => 'DESC'
    ];
    public const SortTypeOptions = [
        ['value' => self::SortTypeAsc, 'label' => '升序'],
        ['value' => self::SortTypeDesc, 'label' => '降序']
    ];

    public function __construct(AdminLoginUser $loginUser, CouponCollectionListRequest $request)
    {
        $this->loginUser = $loginUser;
        $this->request = $request;
    }

    protected function getSortType(int $sort_type_option_value): string
    {
        return self::SortTypeMap[$sort_type_option_value] ?? self::SortTypeMap[self::SortTypeDesc];
    }

    protected function getSortField($option_value): string
    {
        return self::SortFieldsMap[$option_value] ?? self::SortFieldsMap[self::SortFieldsID];
    }

    public function sort_list_info(): array
    {
        return [
            'order_name' => self::SortFieldsOptions[0]['value'],
            'order_type' => self::SortTypeDesc,
            'sort_list' => self::SortFieldsOptions,
        ];
    }

    protected function transformSortParams(array $params): array
    {
        $params['sort_field'] = $this->getSortField($params['sort_field']);
        $params['sort_type'] = $this->getSortType($params['sort_type']);

        return $params;
    }

    /**
     * 增加数据权限过滤条件
     *
     * @param AdminLoginUser $loginUser
     * @param array $params
     * @return array
     */
    protected function addDataPermissionsFilterConditions(AdminLoginUser $loginUser, array $params): array
    {
        // 如果是运营商账号，那么就只能查看该运营商的活动
        if ($loginUser->corp_id > 0) {
            $params['filter_corp_id'] = $loginUser->corp_id;
        }

        return $params;
    }

    /**
     * 整理过滤条件
     *
     * @param array $filters
     * @return array[]
     */
    protected function arrangeFilters(array $filters): array
    {
        $new_filters = [];

        if (!empty($filters['filter_user_phone'])) {
            $new_filters[] = [
                'u.phone', 'like', '%' . $filters['filter_user_phone'] . '%'
            ];
        }

        if (!empty($filters['filter_activity_id'])) {
            $new_filters[] = [
                'cc.activity_id', '=', $filters['filter_activity_id']
            ];
        }

        if (!empty($filters['filter_use_status'])) {
            $new_filters[] = [
                'cc.status', '=', $filters['filter_use_status']
            ];
        }

        if (!empty($filters['filter_corp_id'])) {
            $new_filters[] = [
                'ca.corp_id', '=', $filters['filter_corp_id']
            ];
        }

        return $new_filters;
    }

    public function get_list_data(): array
    {
        // 转换排序参数
        $params = $this->transformSortParams($this->request->toArray());

        // 增加数据权限的过滤条件
        $params = $this->addDataPermissionsFilterConditions($this->loginUser, $params);

        // 整理过滤条件
        $filters = $this->arrangeFilters($params);

        return app(CouponCollection::class)->getAdminListData(
            $filters, $this->request->page, $this->request->limit,
            $params['sort_field'], $params['sort_type']
        );
    }


}