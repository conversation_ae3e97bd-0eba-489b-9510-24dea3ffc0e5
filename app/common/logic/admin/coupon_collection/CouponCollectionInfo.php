<?php

namespace app\common\logic\admin\coupon_collection;

use app\common\cache\redis\entity\AdminLoginUser;
use app\common\lib\exception\RuntimeException;
use app\common\logic\admin\coupon_collection\entity\CouponCollectionInfoRequest;
use app\common\model\CouponActivityStation;
use app\common\model\CouponCollection;

class CouponCollectionInfo
{
    protected CouponCollectionInfoRequest $request;
    protected AdminLoginUser $loginUser;

    public function __construct(AdminLoginUser $loginUser, CouponCollectionInfoRequest $request)
    {
        $this->loginUser = $loginUser;
        $this->request = $request;
    }

    public function run(): array
    {
        // 增加数据权限的过滤条件
        $params = $this->addDataPermissionsFilterConditions($this->loginUser, $this->request->toArray());

        // 整理过滤条件
        $filters = $this->arrangeFilters($params);

        // 查询数据
        $data = $this->getInfo($filters);

        // 补充关联的场站数据
        return $this->fillRelationStations($data);
    }

    protected function fillRelationStations(array $data): array
    {
        $data['stations'] = app(CouponActivityStation::class)->getActivityRelationStationData($data['activity_id'], ['s.id', 's.name']);

        return $data;
    }

    protected function getInfo(array $filters): array
    {
        $activity_data = app(CouponCollection::class)->getInfoData($filters);
        if (empty($activity_data)) {
            throw new RuntimeException(
                '无效优惠券ID',
                [],
                RuntimeException::CodeBusinessException
            );
        }
        return $activity_data;
    }

    protected function arrangeFilters(array $params): array
    {
        $filters = [];

        if (!empty($params['filter_corp_id'])) {
            $filters[] = ['ca.corp_id', '=', $params['filter_corp_id']];
        }
        if (!empty($params['id'])) {
            $filters[] = ['cc.id', '=', $params['id']];
        }

        return $filters;
    }

    protected function addDataPermissionsFilterConditions(AdminLoginUser $loginUser, array $params): array
    {
        if ($loginUser->corp_id > 0) {
            $params['filter_corp_id'] = $loginUser->corp_id;
        }

        return $params;
    }
}