<?php

namespace app\common\logic\admin\coupon_activity;

use app\common\cache\redis\entity\AdminLoginUser;
use app\common\lib\exception\RuntimeException;
use app\common\logic\admin\entity\DeleteActivityRequest;
use app\common\model\CouponActivity;
use app\common\model\CouponOplog;
use think\Request;

class DeleteActivity
{
    protected DeleteActivityRequest $request;
    protected AdminLoginUser $loginUser;
    protected Request $tp_request;

    public function __construct(
        AdminLoginUser        $loginUser,
        DeleteActivityRequest $request,
        Request               $tp_request
    )
    {
        $this->loginUser = $loginUser;
        $this->request = $request;
        $this->tp_request = $tp_request;
    }

    public function run(): array
    {
        // 验证操作权限
        $this->verifyOperationPermission($this->loginUser);

        // 加载活动数据
        $activityData = app(CouponActivity::class)->getActivity($this->request->activity_id, ['status', 'user_id', 'is_del']);

        // 验证数据权限
        $this->verifyDataPermissions($this->loginUser, $activityData);

        // 检查活动是否允许被删除
        $this->verifyIsCanOperation($activityData);

        // 执行操作
        app(CouponActivity::class)->deleteActivity($this->request->activity_id);

        // 记录操作日志
        $this->addOperationLog($this->loginUser->corp_id, $this->loginUser->id, $this->tp_request->baseUrl(), $this->tp_request->post());

        return [];
    }

    protected function addOperationLog(int $corp_id, int $user_id, string $op_interface, array $op_content): void
    {
        app(CouponOplog::class)->addLog(
            $corp_id,
            $user_id,
            CouponOplog::UserTypeAdmin,
            $op_interface,
            json_encode($op_content)
        );
    }

    protected function verifyIsCanOperation(array $activityData): void
    {
        if (!in_array($activityData['status'], [
            CouponActivity::StatusCreating,
            CouponActivity::StatusStop
        ])) {
            throw new RuntimeException(
                '只有活动状态处于"创建中"或"已停用"时，才允许删除。',
                [],
                RuntimeException::CodeBusinessException
            );
        }
    }


    protected function verifyDataPermissions(AdminLoginUser $loginUser, ?array $activityData): void
    {
        if (
            is_null($activityData) ||
            $activityData['is_del'] === CouponActivity::IsDelYes ||
            $activityData['user_id'] !== $loginUser->id
        ) {
            throw new RuntimeException('无效的活动ID', [], RuntimeException::CodeBusinessException);
        }
    }

    protected function verifyOperationPermission(AdminLoginUser $loginUser): void
    {
        if ($loginUser->corp_id === 0 || $loginUser->pid > 0) {
            throw new RuntimeException('只允许运营商主账号操作', [], RuntimeException::CodeBusinessException);
        }
    }
}