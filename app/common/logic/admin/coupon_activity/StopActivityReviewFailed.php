<?php

namespace app\common\logic\admin\coupon_activity;

use app\common\cache\redis\entity\AdminLoginUser;
use app\common\lib\exception\RuntimeException;
use app\common\logic\admin\entity\StopActivityReviewFailedRequest;
use app\common\model\CouponActivity;
use app\common\model\CouponOplog;
use think\Request;

class StopActivityReviewFailed
{
    protected StopActivityReviewFailedRequest $request;
    protected AdminLoginUser $loginUser;
    protected Request $tp_request;

    public function __construct(
        AdminLoginUser                  $loginUser,
        StopActivityReviewFailedRequest $request,
        Request                         $tp_request
    )
    {
        $this->loginUser = $loginUser;
        $this->request = $request;
        $this->tp_request = $tp_request;
    }

    public function run(): array
    {
        // 验证操作权限
        $this->verifyOperationPermissions($this->loginUser);
        // 加载活动数据
        $activityData = app(CouponActivity::class)->getActivity(
            $this->request->id,
            ['corp_id', 'status', 'user_id', 'is_del']
        );
        // 验证数据权限
        $this->verifyDataPermissions($activityData);
        // 验证活动是否能够审核
        $this->verifyIsCanOperation($activityData);
        // 更新活动状态
        app(CouponActivity::class)->updateActivityStatus($this->request->id, CouponActivity::StatusGrant);

        // 记录操作日志
        $this->addOperationLog(
            $activityData['corp_id'], $this->loginUser->id,
            $this->tp_request->baseUrl(), $this->tp_request->post()
        );

        return [];
    }

    protected function addOperationLog(int $corp_id, int $user_id, string $op_interface, array $op_content): void
    {
        app(CouponOplog::class)->addLog(
            $corp_id,
            $user_id,
            CouponOplog::UserTypeAdmin,
            $op_interface,
            json_encode($op_content)
        );
    }


    protected function verifyIsCanOperation(array $activityData): void
    {
        if ($activityData['status'] !== CouponActivity::StatusReviewStop) {
            throw new RuntimeException(
                '只有活动状态处于"停用审核中"才允许审核！',
                [],
                RuntimeException::CodeBusinessException
            );
        }
    }

    protected function verifyDataPermissions(?array $activityData): void
    {
        if (
            empty($activityData) ||
            $activityData['is_del'] === CouponActivity::IsDelYes
        ) {
            throw new RuntimeException('无效优惠券活动ID', [], RuntimeException::CodeBusinessException);
        }
    }

    protected function verifyOperationPermissions(AdminLoginUser $loginUser): void
    {
        if ($loginUser->pid > 0 || $loginUser->corp_id > 0) {
            throw new RuntimeException('只有超级管理员才允许审核优惠券活动。');
        }
    }
}