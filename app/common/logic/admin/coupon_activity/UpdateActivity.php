<?php

namespace app\common\logic\admin\coupon_activity;

use app\common\cache\redis\entity\AdminLoginUser;
use app\common\lib\exception\RuntimeException;
use app\common\log\LogCollector;
use app\common\logic\admin\entity\UpdateActivityRequest;
use app\common\model\CouponActivity;
use app\common\model\CouponActivity as CouponActivityModel;
use app\common\model\CouponActivityStation;
use app\common\model\CouponOplog;
use app\common\model\Stations;
use think\Request;

class UpdateActivity
{
    protected UpdateActivityRequest $request;
    protected AdminLoginUser $loginUser;
    protected Request $tp_request;

    public function __construct(AdminLoginUser $loginUser, UpdateActivityRequest $request, Request $tp_request)
    {
        $this->loginUser = $loginUser;
        $this->request = $request;
        $this->tp_request = $tp_request;
    }

    public function run(): array
    {
        // 加载活动数据
        $activityData = $this->loadActivityData($this->request->id);

        // 验证是否具备操作活动的权限
        $this->verifyOperationPermission($this->loginUser->id, $activityData['user_id']);

        // 验证活动是否具备更新的条件
        $this->verifyActivityIsCanUpdate($activityData['status']);

        // 验证活动时间
        $this->verifyActivityTime($this->request->start_time, $this->request->end_time);

        // 验证场站ID集合是否合法
        $this->verifyStationIdsLegitimacy($this->loginUser->corp_id, $this->request->station_ids);


        // 更新活动
        $this->updateActivity(
            $this->request->id, $this->request->name, $this->request->grant_max_count,
            $this->request->effective_days, $this->request->start_time, $this->request->end_time
        );

        // 加载活动之前关联的场站ID集合
        $old_station_ids = $this->loadCurrentRelationStationIds($this->request->id);


        // 获取新添加的场站ID集合
        $new_add_station_ids = $this->getNewAddStationIds($old_station_ids, $this->request->station_ids);
        // 添加新关联的场站
        $this->batchAddRelationStationId($this->request->id, $new_add_station_ids);


        // 获取不在关联的场站ID集合
        $new_delete_station_ids = $this->getRemoveStationIds($old_station_ids, $this->request->station_ids);
        // 删除不在关联的场站
        $this->batchRemoveRelationStationId($this->request->id, $new_delete_station_ids);

        // 记录操作日志
        $this->addOperationLog(
            $this->loginUser->corp_id, $this->loginUser->id,
            $this->tp_request->baseUrl(), $this->tp_request->post()
        );

        return [];
    }

    protected function addOperationLog(int $corp_id, int $user_id, string $op_interface, array $op_content): void
    {
        app(CouponOplog::class)->addLog(
            $corp_id,
            $user_id,
            CouponOplog::UserTypeAdmin,
            $op_interface,
            json_encode($op_content)
        );
    }

    protected function batchRemoveRelationStationId(int $activity_id, array $remove_station_ids): int
    {
        if ($remove_station_ids) {
            $row = (new CouponActivityStation())->batchDelete($activity_id, $remove_station_ids);
        } else {
            $row = 0;
        }
        LogCollector::collectorRunLog(sprintf('活动关联的场站新删除数量为：%d', $row));
        return $row;
    }

    protected function batchAddRelationStationId(int $activity_id, array $new_add_station_ids): int
    {
        if ($new_add_station_ids) {
            $row = (new CouponActivityStation())->batchAdd($activity_id, $new_add_station_ids);
        } else {
            $row = 0;
        }
        LogCollector::collectorRunLog(sprintf('活动关联的场站新添加数量为：%d', $row));

        return $row;
    }

    protected function loadCurrentRelationStationIds(int $activity_id): array
    {
        return app(CouponActivityStation::class)->getActivityStationIds($activity_id);
    }

    protected function updateActivity(
        int $activity_id, string $name, int $grant_max_count,
        int $effective_days, string $start_time, string $end_time
    ): int
    {
        $updateResult = (new CouponActivityModel())->updateActivity(
            $activity_id, [
                'name' => $name,
                'grant_max_count' => $grant_max_count,
                'effective_days' => $effective_days,
                'start_time' => $start_time,
                'end_time' => $end_time,
                'update_time' => date('Y-m-d H:i:s')
            ]
        );
        LogCollector::collectorRunLog(sprintf('更新活动的结果为：%s', $updateResult));
        return $updateResult;
    }


    protected function verifyActivityIsCanUpdate(int $status): void
    {
        // 只有当活动状态处于创建中时，才允许更新。
        if ($status !== CouponActivity::StatusCreating) {
            throw new RuntimeException(
                '只有当活动状态处于创建中才允许更新。',
                [],
                RuntimeException::CodeBusinessException
            );
        }
    }

    protected function getNewAddStationIds(array $oldStationIds, array $newStationIds): array
    {
        return array_values(array_diff($newStationIds, $oldStationIds));
    }

    protected function getRemoveStationIds(array $oldStationIds, array $newStationIds): array
    {
        return array_values(array_diff($oldStationIds, $newStationIds));
    }

    protected function loadActivityData(int $activity_id): array
    {
        $activityData = app(CouponActivity::class)->getActivity($activity_id, ['status', 'user_id', 'is_del']);
        if (empty($activityData) || $activityData['is_del'] === CouponActivity::IsDelYes) {
            throw new RuntimeException('无效优惠券活动ID', [], RuntimeException::CodeBusinessException);
        }
        return $activityData;
    }


    protected function verifyOperationPermission(int $op_user_id, int $create_activity_user_id): void
    {
        if ($op_user_id !== $create_activity_user_id) {
            throw new RuntimeException('只有创建活动的用户才允许操作', [], RuntimeException::CodeBusinessException);
        }
    }

    /**
     * 验证场站ID集合的合法性
     *
     * @param int $op_user_corp_id
     * @param array $selected_station_ids
     * @return void
     */
    protected function verifyStationIdsLegitimacy(int $op_user_corp_id, array $selected_station_ids): void
    {
        $model = app(Stations::class);
        $station_ids = $model->getStationIds($op_user_corp_id);

        $diff_station_ids = array_diff($selected_station_ids, $station_ids);
        if (count($diff_station_ids) > 0) {
            throw new RuntimeException(
                sprintf('场站ID集合中存在无效的ID：%s', implode(',', $diff_station_ids)),
                [],
                RuntimeException::CodeBusinessException
            );
        }
    }

    protected function verifyActivityTime(string $start_time, string $end_time): void
    {
        $start_time_int = strtotime($start_time);
        $end_time_int = strtotime($end_time);

        if ($start_time_int >= $end_time_int) {
            throw new RuntimeException('活动结束时间必须大于活动开始时间', [], RuntimeException::CodeBusinessException);
        }
    }


}