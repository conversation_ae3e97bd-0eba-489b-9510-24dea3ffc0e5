<?php

namespace app\common\logic\admin\coupon_activity;

use app\common\cache\redis\entity\AdminLoginUser;
use app\common\lib\exception\RuntimeException;
use app\common\logic\admin\entity\SubmitReviewRequest;
use app\common\model\CouponActivity;
use app\common\model\CouponActivityStation;
use app\common\model\CouponOplog;
use app\common\model\CouponPattern;
use think\Request;

// 提交审核
class SubmitReview
{
    protected SubmitReviewRequest $request;
    protected AdminLoginUser $loginUser;
    protected Request $tp_request;

    public function __construct(
        AdminLoginUser      $loginUser,
        SubmitReviewRequest $request,
        Request             $tp_request
    )
    {
        $this->loginUser = $loginUser;
        $this->request = $request;
        $this->tp_request = $tp_request;
    }

    public function run(): array
    {

        // 验证操作权限
        $this->verifyOperationPermissions($this->loginUser);

        // 加载优惠券活动数据
        $activityData = app(CouponActivity::class)->getActivity(
            $this->request->id,
            ['status', 'user_id', 'is_del', 'id']
        );

        // 验证数据权限
        $this->verifyDataPermissions($this->loginUser, $activityData);

        // 验证活动是否已经具备提交审核的条件(状态、关联的场站、优惠券模板)
        $this->verifyIsCanSubmitReview($activityData);

        // 将活动状态更新为审核中
        $this->updateActivityStatus($this->request->id);

        // 记录操作日志
        $this->addOperationLog(
            $this->loginUser->corp_id, $this->loginUser->id,
            $this->tp_request->baseUrl(), $this->tp_request->post()
        );

        return [];
    }

    protected function addOperationLog(int $corp_id, int $user_id, string $op_interface, array $op_content): void
    {
        app(CouponOplog::class)->addLog(
            $corp_id,
            $user_id,
            CouponOplog::UserTypeAdmin,
            $op_interface,
            json_encode($op_content)
        );
    }

    protected function verifyDataPermissions(AdminLoginUser $loginUser, ?array $activityData): void
    {
        if (
            empty($activityData) ||
            $activityData['is_del'] === CouponActivity::IsDelYes ||
            $loginUser->id !== $activityData['user_id']
        ) {
            throw new RuntimeException('无效优惠券活动ID', [], RuntimeException::CodeBusinessException);
        }
    }

    protected function updateActivityStatus(int $activity_id): bool
    {
        return app(CouponActivity::class)->updateActivityStatus($activity_id, CouponActivity::StatusReview);
    }


    protected function verifyOperationPermissions(AdminLoginUser $loginUser): void
    {
        if ($loginUser->pid > 0 || $loginUser->corp_id === 0) {
            throw new RuntimeException('只有运营商主账号才允许提交审核', [], RuntimeException::CodeBusinessException);
        }
    }

    protected function verifyIsCanSubmitReview(array $activityData): void
    {
        if ($activityData['status'] !== CouponActivity::StatusCreating) {
            throw new RuntimeException('只有当活动处于创建中才能够提交审核!', [], RuntimeException::CodeBusinessException);
        }
        $station_count = app(CouponActivityStation::class)->getActivityStationCount($activityData['id']);
        if ($station_count === 0) {
            throw new RuntimeException('请先选择与活动关联的场站后再提交审核！', [], RuntimeException::CodeBusinessException);
        }
        $pattern_count = app(CouponPattern::class)->getActivityPatternCount($activityData['id']);
        if ($pattern_count === 0) {
            throw new RuntimeException('请先为活动添加关联优惠券再提交审核！', [], RuntimeException::CodeBusinessException);
        }
    }
}