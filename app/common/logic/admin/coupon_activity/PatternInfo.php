<?php

namespace app\common\logic\admin\coupon_activity;

use app\common\cache\redis\entity\AdminLoginUser;
use app\common\lib\exception\RuntimeException;
use app\common\logic\admin\entity\PatternInfoRequest;
use app\common\model\CouponActivity;
use app\common\model\CouponPattern;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

class PatternInfo
{
    protected PatternInfoRequest $request;
    protected AdminLoginUser $loginUser;

    public function __construct(AdminLoginUser $loginUser, PatternInfoRequest $request)
    {
        $this->loginUser = $loginUser;
        $this->request = $request;
    }


    /**
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function run(): array
    {
        // 增加数据权限的过滤条件
        $params = $this->addDataPermissionsFilterConditions($this->loginUser, $this->request->toArray());

        // 整理过滤条件
        $filters = $this->arrangeFilters($params);

        // 查询数据
        return $this->getPatternInfo($filters);
    }

    /**
     * @param array $filters
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function getPatternInfo(array $filters): array
    {
        $activity_data = app(CouponPattern::class)->getPatternInfo($filters);
        if (empty($activity_data)) {
            throw new RuntimeException(
                '无效现金券ID',
                [],
                RuntimeException::CodeBusinessException
            );
        }
        return $activity_data;
    }

    protected function arrangeFilters(array $params): array
    {
        $filters = [
            ['cp.id', '=', $params['pattern_id']],
            ['ca.is_del', '=', CouponActivity::IsDelNot]
        ];

        if (!empty($params['filter_corp_id'])) {
            $filters[] = ['ca.corp_id', '=', $params['filter_corp_id']];
        }

        return $filters;
    }

    protected function addDataPermissionsFilterConditions(AdminLoginUser $loginUser, array $params): array
    {
        if ($loginUser->corp_id > 0) {
            $params['filter_corp_id'] = $loginUser->corp_id;
        }

        return $params;
    }
}