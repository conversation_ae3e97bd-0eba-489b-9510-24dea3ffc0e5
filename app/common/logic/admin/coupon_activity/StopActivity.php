<?php

namespace app\common\logic\admin\coupon_activity;

use app\common\cache\redis\entity\AdminLoginUser;
use app\common\lib\exception\RuntimeException;
use app\common\logic\admin\entity\StopActivityRequest;
use app\common\model\CouponActivity;
use app\common\model\CouponOplog;
use think\Request;

// 停用活动
class StopActivity
{
    protected StopActivityRequest $request;
    protected AdminLoginUser $loginUser;
    protected Request $tp_request;

    public function __construct(
        AdminLoginUser      $loginUser,
        StopActivityRequest $request,
        Request             $tp_request
    )
    {
        $this->loginUser = $loginUser;
        $this->request = $request;
        $this->tp_request = $tp_request;
    }

    public function run(): array
    {
        // 验证操作权限
        $this->verifyOperationPermissions($this->loginUser);

        // 加载优惠券活动数据
        $activityData = app(CouponActivity::class)->getActivity(
            $this->request->id,
            ['corp_id', 'status', 'user_id', 'is_del']
        );

        // 验证数据权限
        $this->verifyDataPermissions($this->loginUser, $activityData);

        // 验证活动是否已经具备提交审核的条件(状态、关联的场站、优惠券模板)
        $this->verifyIsCanOperation($activityData);

        // 更新活动状态
        $this->updateActivityStatus($this->request->id);

        // 记录操作日志
        $this->addOperationLog(
            $activityData['corp_id'], $this->loginUser->id,
            $this->tp_request->baseUrl(), $this->tp_request->post()
        );

        return [];
    }

    protected function addOperationLog(int $corp_id, int $user_id, string $op_interface, array $op_content): void
    {
        app(CouponOplog::class)->addLog(
            $corp_id,
            $user_id,
            CouponOplog::UserTypeAdmin,
            $op_interface,
            json_encode($op_content)
        );
    }

    protected function verifyIsCanOperation(array $activityData): void
    {
        if ($activityData['status'] !== CouponActivity::StatusGrant) {
            throw new RuntimeException('只有当活动状态处于"发放中"才能够停用活动!', [], RuntimeException::CodeBusinessException);
        }
    }

    protected function verifyDataPermissions(AdminLoginUser $loginUser, ?array $activityData): void
    {
        if (
            empty($activityData) ||
            $activityData['is_del'] === CouponActivity::IsDelYes ||
            $loginUser->id !== $activityData['user_id']
        ) {
            throw new RuntimeException('无效优惠券活动ID', [], RuntimeException::CodeBusinessException);
        }
    }

    protected function updateActivityStatus(int $activity_id): bool
    {
        return app(CouponActivity::class)->updateActivityStatus($activity_id, CouponActivity::StatusReviewStop);
    }


    protected function verifyOperationPermissions(AdminLoginUser $loginUser): void
    {
        if ($loginUser->pid > 0 || $loginUser->corp_id === 0) {
            throw new RuntimeException('只有运营商主账号才允许停用活动', [], RuntimeException::CodeBusinessException);
        }
    }


}