<?php

namespace app\common\logic\admin\coupon_activity;

use app\common\cache\redis\entity\AdminLoginUser;
use app\common\lib\exception\RuntimeException;
use app\common\logic\admin\entity\UpdatePatternRequest;
use app\common\model\CouponActivity;
use app\common\model\CouponOplog;
use app\common\model\CouponPattern;
use think\Request;

class UpdatePattern
{
    protected UpdatePatternRequest $request;
    protected AdminLoginUser $loginUser;
    protected Request $tp_request;

    public function __construct(
        AdminLoginUser       $loginUser,
        UpdatePatternRequest $request,
        Request              $tp_request
    )
    {
        $this->loginUser = $loginUser;
        $this->request = $request;
        $this->tp_request = $tp_request;
    }

    public function run(): array
    {
        // 是否有操作权限
        $this->verifyOperationPermission($this->loginUser);
        // 加载现金券数据
        $patternData = $this->loadCouponPatternData($this->request->pattern_id);
        // 验证参数的合法性
        $this->verifyParamsLegitimacy($patternData, $this->loginUser);
        // 加载活动数据
        $activityData = $this->loadActivityData($patternData['activity_id']);
        // 验证目前是否允许操作
        $this->verifyActivityIsCanOperation($activityData);
        // 执行操作
        $this->updateCouponPattern(
            $this->request->pattern_id, $this->request->pattern_name,
            $this->request->par_value, $this->request->usage_amount
        );
        // 记录操作日志
        $this->addOperationLog(
            $this->loginUser->corp_id, $this->loginUser->id,
            $this->tp_request->baseUrl(), $this->tp_request->post()
        );

        return [];
    }

    protected function updateCouponPattern(int $pattern_id, string $pattern_name, int $par_value, int $usage_amount): int
    {
        return app(CouponPattern::class)->updatePattern(
            $pattern_id,
            [
                'name' => $pattern_name,
                'par_value' => $par_value,
                'usage_amount' => $usage_amount,
            ]
        );
    }

    protected function loadActivityData(int $activity_id): ?array
    {
        return app(CouponActivity::class)->getActivity($activity_id, ['status', 'is_del']);
    }

    protected function loadCouponPatternData(int $pattern_id): ?array
    {
        return app(CouponPattern::class)
            ->getPattern($pattern_id, ['user_id', 'activity_id']);
    }

    protected function verifyActivityIsCanOperation(?array $activityData): void
    {
        // 3.1. 验证活动是否被删除
        if ($activityData['is_del'] === CouponActivity::IsDelYes) {
            throw new RuntimeException(
                '关联的活动已被删除，无法更新现金券!',
                [],
                RuntimeException::CodeBusinessException
            );
        }
        // 3.2. 验证目前活动状态
        if ($activityData['status'] !== CouponActivity::StatusCreating) {
            throw new RuntimeException(
                '只有当关联的活动状态处于"创建中"时才允许更新现金券!',
                [],
                RuntimeException::CodeBusinessException
            );
        }

    }

    protected function verifyParamsLegitimacy(?array $patternData, AdminLoginUser $op_user): void
    {
        // 2.1. 验证现金券是否存在
        // 2.2. 验证现金券是否是当前操作员创建的
        if (
            empty($patternData) ||
            $op_user->id !== $patternData['user_id']
        ) {
            throw new RuntimeException('无效现金券ID', [], RuntimeException::CodeBusinessException);
        }
    }

    protected function verifyOperationPermission(AdminLoginUser $op_user): void
    {
        if ($op_user->pid > 0 || $op_user->corp_id === 0) {
            throw new RuntimeException('只有运营商主账号才能够更新现金券', [], RuntimeException::CodeBusinessException);
        }
    }

    protected function addOperationLog(int $corp_id, int $user_id, string $op_interface, array $op_content): void
    {
        app(CouponOplog::class)->addLog(
            $corp_id,
            $user_id,
            CouponOplog::UserTypeAdmin,
            $op_interface,
            json_encode($op_content)
        );
    }
}