<?php

namespace app\common\logic\admin\coupon_activity;

use app\common\cache\redis\entity\AdminLoginUser;
use app\common\lib\exception\RuntimeException;
use app\common\logic\admin\entity\AddPatternRequest;
use app\common\logic\admin\entity\UpdateActivityRequest;
use app\common\model\CouponActivity;
use app\common\model\CouponOplog;
use app\common\model\CouponPattern;
use think\facade\Db;
use think\Request;

class AddPattern
{
    protected AddPatternRequest $request;
    protected AdminLoginUser $loginUser;
    protected Request $tp_request;

    public function __construct(
        AdminLoginUser    $loginUser,
        AddPatternRequest $request,
        Request           $tp_request
    )
    {
        $this->loginUser = $loginUser;
        $this->request = $request;
        $this->tp_request = $tp_request;
    }

    public function run(): array
    {
        // 1. 是否有操作权限
        $this->verifyOperationPermission($this->loginUser);
        // 2. 验证参数的合法性
        $activityData = $this->verifyParamsLegitimacy($this->request, $this->loginUser);
        // 3. 验证活动目前是否允许操作
        $this->verifyActivityIsCanOperation($activityData['status']);
        // 4. 执行操作
        $new_id = app(CouponPattern::class)->createPattern(
            $this->request->pattern_name,
            $this->request->activity_id,
            $this->request->par_value,
            $this->request->usage_amount,
            $this->loginUser->id
        );

        // 记录操作日志
        $this->addOperationLog(
            $this->loginUser->corp_id, $this->loginUser->id,
            $this->tp_request->baseUrl(), $this->tp_request->post()
        );

        return ['new_id' => $new_id];
    }


    protected function addOperationLog(int $corp_id, int $user_id, string $op_interface, array $op_content): void
    {
        app(CouponOplog::class)->addLog(
            $corp_id,
            $user_id,
            CouponOplog::UserTypeAdmin,
            $op_interface,
            json_encode($op_content)
        );
    }

    protected function verifyActivityIsCanOperation(int $activity_status): void
    {
        // 3.1. 验证目前活动状态
        if ($activity_status !== CouponActivity::StatusCreating) {
            throw new RuntimeException(
                '活动只有当状态处于"创建中"时才允许添加现金券!',
                [],
                RuntimeException::CodeBusinessException
            );
        }
    }

    protected function verifyParamsLegitimacy(AddPatternRequest $params, AdminLoginUser $op_user): array
    {
        // 2.1. 验证活动是否存在
        // 2.2. 验证活动是否是当前操作员创建的
        $activityData = app(CouponActivity::class)->getActivity($params->activity_id, ['status', 'is_del', 'user_id']);
        if (
            empty($activityData) ||
            $activityData['is_del'] === CouponActivity::IsDelYes ||
            $op_user->id !== $activityData['user_id']
        ) {
            throw new RuntimeException('无效活动ID', [], RuntimeException::CodeBusinessException);
        }

        return $activityData;
    }

    protected function verifyOperationPermission(AdminLoginUser $op_user): void
    {
        if ($op_user->pid > 0 || $op_user->corp_id === 0) {
            throw new RuntimeException('只有运营商主账号才能够添加现金券', [], RuntimeException::CodeBusinessException);
        }
    }

}