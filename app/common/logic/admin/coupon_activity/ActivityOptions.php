<?php

namespace app\common\logic\admin\coupon_activity;

use app\common\cache\redis\entity\AdminLoginUser;
use app\common\model\CouponActivity;
use think\Request;

class ActivityOptions
{
    protected AdminLoginUser $loginUser;
    protected Request $request;

    public function __construct(AdminLoginUser $loginUser, Request $request)
    {
        $this->loginUser = $loginUser;
        $this->request = $request;
    }

    public function run(): array
    {
        // 增加数据权限的过滤条件
        $params = $this->addDataPermissionsFilterConditions($this->loginUser);

        $filters = $this->arrangeFilters($params);

        $options = $this->getOptions($filters);

        return [
            'options' => $options
        ];
    }

    protected function getOptions(array $filters): array
    {
        return app(CouponActivity::class)->getOptionsData($filters);
    }

    /**
     * 整理过滤条件
     *
     * @param array $filters
     * @return array[]
     */
    protected function arrangeFilters(array $filters): array
    {
        $new_filters = [
            ['is_del', '=', CouponActivity::IsDelNot]
        ];


        if (!empty($filters['filter_corp_id'])) {
            $new_filters[] = [
                'corp_id', '=', $filters['filter_corp_id']
            ];
        }

        return $new_filters;
    }

    /**
     * 增加数据权限过滤条件
     *
     * @param AdminLoginUser $loginUser
     * @return array
     */
    protected function addDataPermissionsFilterConditions(AdminLoginUser $loginUser): array
    {
        $params = [];

        // 如果是运营商账号，那么就只能查看该运营商的活动
        if ($loginUser->corp_id > 0) {
            $params['filter_corp_id'] = $loginUser->corp_id;
        }

        return $params;
    }
}