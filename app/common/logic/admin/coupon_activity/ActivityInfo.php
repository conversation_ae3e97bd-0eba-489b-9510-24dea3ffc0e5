<?php

namespace app\common\logic\admin\coupon_activity;

use app\common\cache\redis\entity\AdminLoginUser;
use app\common\lib\exception\RuntimeException;
use app\common\logic\admin\entity\ActivityInfoRequest;
use app\common\model\CouponActivity;
use app\common\model\CouponActivity as CouponActivityModel;
use app\common\model\CouponActivityStation;

class ActivityInfo
{
    protected ActivityInfoRequest $request;
    protected AdminLoginUser $loginUser;

    public function __construct(AdminLoginUser $loginUser, ActivityInfoRequest $request)
    {
        $this->loginUser = $loginUser;
        $this->request = $request;
    }

    public function run(): array
    {
        // 增加数据权限的过滤条件
        $params = $this->addDataPermissionsFilterConditions($this->loginUser, $this->request->toArray());

        // 整理过滤条件
        $filters = $this->arrangeFilters($params);

        // 查询数据
        $data = $this->getActivityInfo($filters);

        // 补充关联的场站ID
        return $this->fillRelationStationIds($data);
    }

    protected function fillRelationStationIds(array $activity): array
    {
        $activity['station_ids'] = app(CouponActivityStation::class)->getActivityStationIds($activity['id']);

        return $activity;
    }

    protected function getActivityInfo(array $filters): array
    {
        $activity_data = app(CouponActivityModel::class)->getActivityInfo($filters);
        if (empty($activity_data)) {
            throw new RuntimeException(
                '无效活动ID',
                [],
                RuntimeException::CodeBusinessException
            );
        }
        return $activity_data;
    }

    protected function arrangeFilters(array $params): array
    {
        $filters = [
            ['is_del', '=', CouponActivity::IsDelNot]
        ];

        if (!empty($params['filter_corp_id'])) {
            $filters[] = ['corp_id', '=', $params['filter_corp_id']];
        }
        if (!empty($params['activity_id'])) {
            $filters[] = ['id', '=', $params['activity_id']];
        }

        return $filters;
    }

    protected function addDataPermissionsFilterConditions(AdminLoginUser $loginUser, array $params): array
    {
        if ($loginUser->corp_id > 0) {
            $params['filter_corp_id'] = $loginUser->corp_id;
        }

        return $params;
    }
}