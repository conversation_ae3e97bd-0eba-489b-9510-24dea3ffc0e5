<?php

namespace app\common\logic\admin\coupon_activity;

use app\common\cache\redis\entity\AdminLoginUser;
use app\common\lib\VerifyData;
use app\common\model\AdminUsers;
use app\common\model\CouponOplog;
use app\common\model\Users;
use Respect\Validation\Validator as v;
use think\Request;

class CouponOplogList
{
    protected Request $request;
    protected AdminLoginUser $loginUser;

    public const SortFieldsID = 1; // 日志ID
    public const SortFieldsOpTime = 2; // 操作时间
    public const SortFieldsOptions = [
        ['value' => self::SortFieldsID, 'label' => '日志ID'],
        ['value' => self::SortFieldsOpTime, 'label' => '操作时间'],
    ];
    public const SortFieldsMap = [
        self::SortFieldsID => 'co.id',
        self::SortFieldsOpTime => 'co.op_time',
    ];

    // 排序方式
    public const SortTypeAsc = 2; // 升序
    public const SortTypeDesc = 1; // 降序
    public const SortTypeMap = [
        self::SortTypeAsc => 'ASC',
        self::SortTypeDesc => 'DESC'
    ];
    public const SortTypeOptions = [
        ['value' => self::SortTypeAsc, 'label' => '升序'],
        ['value' => self::SortTypeDesc, 'label' => '降序']
    ];

    public function __construct(AdminLoginUser $loginUser, Request $request)
    {
        $this->loginUser = $loginUser;
        $this->request = $request;
    }

    protected function getSortType(int $sort_type_option_value): string
    {
        return self::SortTypeMap[$sort_type_option_value] ?? self::SortTypeMap[self::SortTypeDesc];
    }

    protected function getSortField($option_value): string
    {
        return self::SortFieldsMap[$option_value] ?? self::SortFieldsMap[self::SortFieldsID];
    }

    public function sort_list_info(): array
    {
        return [
            'order_name' => self::SortFieldsOptions[0]['value'],
            'order_type' => self::SortTypeDesc,
            'sort_list' => self::SortFieldsOptions,
        ];
    }

    protected function transformSortParams(array $params): array
    {
        $params['sort_field'] = $this->getSortField($params['sort_field']);
        $params['sort_type'] = $this->getSortType($params['sort_type']);

        return $params;
    }

    /**
     * 增加数据权限过滤条件
     *
     * @param AdminLoginUser $loginUser
     * @param array $params
     * @return array
     */
    protected function addDataPermissionsFilterConditions(AdminLoginUser $loginUser, array $params): array
    {
        // 如果是运营商账号，那么就只能查看该运营商的活动
        if ($loginUser->corp_id > 0) {
            $params['filter_corp_id'] = $loginUser->corp_id;
        }

        return $params;
    }

    /**
     * 整理过滤条件
     *
     * @param array $filters
     * @return array[]
     */
    protected function arrangeFilters(array $filters): array
    {
        $new_filters = [];

        if (isset($filters['filter_user_type'])) {
            $new_filters[] = [
                'co.user_type', '=', $filters['filter_user_type']
            ];
        }
        if (!empty($filters['filter_user_id'])) {
            $new_filters[] = [
                'co.user_id', '=', $filters['filter_user_id']
            ];
        }


        if (!empty($filters['filter_op_interface'])) {
            $new_filters[] = [
                'co.op_interface', 'like', '%' . $filters['filter_op_interface'] . '%'
            ];
        }
        if (!empty($filters['filter_corp_id'])) {
            $new_filters[] = [
                'co.corp_id', '=', $filters['filter_corp_id']
            ];
        }

        return $new_filters;
    }

    protected function verifyParams(array $params): array
    {
        return v::input($params, VerifyData::coupon_oplog([
            'filter_user_type', 'filter_user_id', 'filter_op_interface',
            'page', 'limit',
            'sort_field', 'sort_type'
        ]));
    }

    protected function queryListData(array $filters, int $page, int $limit, string $sort_field, string $sort_type): array
    {
        return (new CouponOplog())->getListData(
            $filters, $page, $limit,
            $sort_field, $sort_type
        );
    }

    public function get_list_data(): array
    {
        $data = $this->request->post();

        // 验证参数
        $verifyData = $this->verifyParams($data);

        // 转换排序参数
        $params = $this->transformSortParams($verifyData);

        // 增加数据权限的过滤条件
        $params = $this->addDataPermissionsFilterConditions($this->loginUser, $params);

        // 整理过滤条件
        $filters = $this->arrangeFilters($params);

        // 返回列表数据
        $list = $this->queryListData(
            $filters, $params['page'], $params['limit'],
            $params['sort_field'], $params['sort_type']
        );

        // 获取用户ID集合
        [$applet_user_ids, $admin_user_ids] = $this->get_user_ids($list);

        // 加载小程序用户手机号
        if (count($applet_user_ids) > 0) {
            $appletUserIdToPhoneMap = $this->loadAppletUserIdToPhonesMap($applet_user_ids);
        } else {
            $appletUserIdToPhoneMap = [];
        }

        // 加载后台用户的名称
        if (count($admin_user_ids) > 0) {
            $adminUserIdToNameMap = $this->loadAdminUserIdToNameMap($admin_user_ids);
        } else {
            $adminUserIdToNameMap = [];
        }

        // 填充用户数据
        return $this->fillUserName($list, $adminUserIdToNameMap, $appletUserIdToPhoneMap);
    }

    protected function fillUserName(array $list, array $adminUserIdToNameMap, array $appletUserIdToPhoneMap): array
    {
        foreach ($list['data'] as $index => $value) {
            if ($value['user_type'] === CouponOplog::UserTypeAdmin) {
                $list['data'][$index]['user_name'] = $adminUserIdToNameMap[$value['user_id']] ?? null;
            } else {
                $list['data'][$index]['user_phone'] = $appletUserIdToPhoneMap[$value['user_id']] ?? null;
            }
        }
        return $list;
    }

    protected function loadAdminUserIdToNameMap(array $admin_user_ids): array
    {
        return app(AdminUsers::class)->where('id', 'in', $admin_user_ids)->column('name', 'id');
    }

    protected function loadAppletUserIdToPhonesMap(array $applet_user_ids): array
    {
        return app(Users::class)->where('id', 'in', $applet_user_ids)->column('phone', 'id');
    }


    protected function get_user_ids(array $list): array
    {
        $applet_user_ids = [];
        $admin_user_ids = [];
        foreach ($list['data'] as $value) {
            if ($value['user_type'] === CouponOplog::UserTypeApplet) {
                $applet_user_ids[] = $value['user_id'];
            } else {
                $admin_user_ids[] = $value['user_id'];
            }
        }

        if (!empty($applet_user_ids)) {
            $applet_user_ids = array_values(array_unique($applet_user_ids));
        }
        if (!empty($admin_user_ids)) {
            $admin_user_ids = array_values(array_unique($admin_user_ids));
        }

        return [$applet_user_ids, $admin_user_ids];
    }


}