<?php

namespace app\common\logic\admin\coupon_activity;

use app\common\cache\redis\entity\AdminLoginUser;
use app\common\lib\exception\RuntimeException;
use app\common\log\LogCollector;
use app\common\logic\admin\entity\CreateActivityRequest;
use app\common\model\CouponActivity as CouponActivityModel;
use app\common\model\CouponActivityStation;
use app\common\model\CouponOplog;
use app\common\model\Stations;
use think\Request;

class CreateActivity
{
    protected CreateActivityRequest $request;
    protected AdminLoginUser $loginUser;
    protected Request $tp_request;

    public function __construct(
        AdminLoginUser        $loginUser,
        CreateActivityRequest $request,
        Request               $tp_request
    )
    {
        $this->loginUser = $loginUser;
        $this->request = $request;
        $this->tp_request = $tp_request;
    }

    public function run(): array
    {
        // 验证是否具备创建活动的权限
        $this->verifyCreatePermission($this->loginUser);

        // 验证活动时间
        $this->verifyActivityTime($this->request->start_time, $this->request->end_time);

        // 验证场站ID集合是否合法
        $this->verifyStationIdsLegitimacy($this->loginUser->corp_id, $this->request->station_ids);

        // 创建活动
        $new_id = $this->createActivity(
            $this->request->name, $this->loginUser->corp_id,
            $this->loginUser->id, $this->request->grant_max_count,
            $this->request->effective_days, $this->request->start_time,
            $this->request->end_time
        );

        // 建立活动与场站的关系
        $this->buildActivityAndStationRelation($new_id, $this->request->station_ids);

        // 记录操作日志
        $this->addOperationLog(
            $this->loginUser->corp_id, $this->loginUser->id,
            $this->tp_request->baseUrl(), $this->tp_request->post()
        );

        return [
            'new_id' => $new_id
        ];
    }

    protected function addOperationLog(int $corp_id, int $user_id, string $op_interface, array $op_content): void
    {
        app(CouponOplog::class)->addLog(
            $corp_id,
            $user_id,
            CouponOplog::UserTypeAdmin,
            $op_interface,
            json_encode($op_content)
        );
    }

    protected function buildActivityAndStationRelation(int $activity_id, array $station_ids): int
    {
        if (!empty($station_ids)) {
            $row = app(CouponActivityStation::class)->batchAdd($activity_id, $station_ids);
        } else {
            $row = 0;
        }
        LogCollector::collectorRunLog(sprintf('活动关联的场站添加数量为：%d', $row));
        return $row;
    }

    protected function createActivity(
        string $name, int $corp_id, int $user_id, int $grant_max_count,
        int    $effective_days, string $start_time, string $end_time
    ): int
    {
        $new_id = (new CouponActivityModel())->createActivity(
            $name,
            $corp_id,
            $user_id,
            $grant_max_count,
            $effective_days,
            $start_time,
            $end_time,
        );
        LogCollector::collectorRunLog(sprintf('新建的活动ID为：%d', $new_id));
        return $new_id;
    }

    protected function verifyCreatePermission(AdminLoginUser $loginUser): void
    {
        if ($loginUser->corp_id === 0 || $loginUser->pid > 0) {
            throw new RuntimeException('只有运营商主账号才支持创建优惠券活动', [], RuntimeException::CodeBusinessException);
        }
    }

    /**
     * 验证场站ID集合的合法性
     *
     * @param int $user_corp_id
     * @param array $station_ids
     * @return void
     */
    protected function verifyStationIdsLegitimacy(int $user_corp_id, array $station_ids): void
    {
        $model = app(Stations::class);
        $db_station_ids = $model->getStationIds($user_corp_id);

        $diff_station_ids = array_diff($station_ids, $db_station_ids);
        if (count($diff_station_ids) > 0) {
            throw new RuntimeException(
                sprintf('场站ID集合中存在无效的ID：%s', implode(',', $diff_station_ids)),
                [],
                RuntimeException::CodeBusinessException
            );
        }
    }

    protected function verifyActivityTime(string $start_time, string $end_time): void
    {
        $start_time = strtotime($start_time);
        $end_time = strtotime($end_time);

        if ($start_time >= $end_time) {
            throw new RuntimeException('活动结束时间必须大于活动开始时间', [], RuntimeException::CodeBusinessException);
        }
    }


}