<?php

namespace app\common\logic\admin\coupon_activity;

use app\common\cache\redis\entity\AdminLoginUser;
use app\common\logic\admin\entity\ActivityListRequest;
use app\common\model\CouponActivity;
use app\common\model\CouponActivity as CouponActivityModel;

class ActivityList
{
    protected ActivityListRequest $request;
    protected AdminLoginUser $loginUser;

    public const SortFieldsID = 1; // 活动ID
    public const SortFieldsCreateTime = 2; // 创建时间
    public const SortFieldsGrantNum = 3; // 活动券已领取份数
    public const SortFieldsOptions = [
        ['value' => self::SortFieldsID, 'label' => '活动ID'],
        ['value' => self::SortFieldsCreateTime, 'label' => '创建时间'],
        ['value' => self::SortFieldsGrantNum, 'label' => '活动券已领取份数'],
    ];
    public const SortFieldsMap = [
        self::SortFieldsID => 'ca.id',
        self::SortFieldsCreateTime => 'ca.create_time',
        self::SortFieldsGrantNum => 'ca.grant_num'
    ];

    // 排序方式
    public const SortTypeAsc = 2; // 升序
    public const SortTypeDesc = 1; // 降序
    public const SortTypeMap = [
        self::SortTypeAsc => 'ASC',
        self::SortTypeDesc => 'DESC'
    ];
    public const SortTypeOptions = [
        ['value' => self::SortTypeAsc, 'label' => '升序'],
        ['value' => self::SortTypeDesc, 'label' => '降序']
    ];

    public function __construct(AdminLoginUser $loginUser, ActivityListRequest $request)
    {
        $this->loginUser = $loginUser;
        $this->request = $request;
    }

    protected function getSortType(int $sort_type_option_value): string
    {
        return self::SortTypeMap[$sort_type_option_value] ?? self::SortTypeMap[self::SortTypeDesc];
    }

    protected function getSortField($option_value): string
    {
        return self::SortFieldsMap[$option_value] ?? self::SortFieldsMap[self::SortFieldsID];
    }

    public function sort_list_info(): array
    {
        return [
            'order_name' => self::SortFieldsOptions[0]['value'],
            'order_type' => self::SortTypeDesc,
            'sort_list' => self::SortFieldsOptions,
        ];
    }

    protected function transformSortParams(array $params): array
    {
        $params['sort_field'] = $this->getSortField($params['sort_field']);
        $params['sort_type'] = $this->getSortType($params['sort_type']);

        return $params;
    }

    /**
     * 增加数据权限过滤条件
     *
     * @param AdminLoginUser $loginUser
     * @param array $params
     * @return array
     */
    protected function addDataPermissionsFilterConditions(AdminLoginUser $loginUser, array $params): array
    {
        // 如果是运营商账号，那么就只能查看该运营商的活动
        if ($loginUser->corp_id > 0) {
            $params['filter_corp_id'] = $loginUser->corp_id;
        }

        return $params;
    }

    /**
     * 整理过滤条件
     *
     * @param array $filters
     * @return array[]
     */
    protected function arrangeFilters(array $filters): array
    {
        $new_filters = [
            ['ca.is_del', '=', CouponActivity::IsDelNot]
        ];

        if (!empty($filters['filter_id'])) {
            $new_filters[] = [
                'ca.id', '=', $filters['filter_id']
            ];
        }

        if (!empty($filters['filter_name'])) {
            $new_filters[] = [
                'ca.name', 'like', '%' . $filters['filter_name'] . '%'
            ];
        }
        if (!empty($filters['filter_status'])) {
            $new_filters[] = [
                'ca.status', '=', $filters['filter_status']
            ];
        }
        if (!empty($filters['filter_corp_id'])) {
            $new_filters[] = [
                'ca.corp_id', '=', $filters['filter_corp_id']
            ];
        }

        return $new_filters;
    }

    public function get_list_data(): array
    {
        // 转换排序参数
        $params = $this->transformSortParams($this->request->toArray());

        // 增加数据权限的过滤条件
        $params = $this->addDataPermissionsFilterConditions($this->loginUser, $params);

        // 整理过滤条件
        $filters = $this->arrangeFilters($params);

        return (new CouponActivityModel())->getListData(
            $filters, $params['page'], $params['limit'],
            $params['sort_field'], $params['sort_type']
        );
    }


}