<?php

namespace app\common\logic\applet\coupon_collection;

use app\common\cache\redis\entity\AppletLoginUser;
use app\common\logic\applet\entity\CouponCollectionListRequest;
use app\common\model\CouponActivityStation;
use app\common\model\CouponCollection;

class CouponCollectionList
{
    public CouponCollectionListRequest $request;
    public AppletLoginUser $loginUser;

    public function __construct(AppletLoginUser $loginUser, CouponCollectionListRequest $request)
    {
        $this->loginUser = $loginUser;
        $this->request = $request;
    }

    public function run(): array
    {
        // 增加数据权限的过滤条件
        $params = $this->addDataPermissionsFilterConditions($this->loginUser, $this->request->toArray());

        // 整理过滤条件
        $filters = $this->arrangeFilters($params);

        // 查询列表数据
        $list = $this->getListData($filters, $this->request->page, $this->request->limit);

        // 获取本次查询的列表中的活动ID
        $activity_ids = $this->getListActivityIds($list);

        // 查询关联的场站数据
        $stationData = $this->queryRelationStationData($activity_ids);

        // 填充场站数据
        return $this->fillStationData($list, $stationData);
    }

    protected function fillStationData(array $list, array $stationData): array
    {
        foreach ($list['data'] as $key => $value) {
            if (isset($stationData[$value['activity_id']])) {
                $list['data'][$key]['stations'] = $stationData[$value['activity_id']];
                unset($list['data'][$key]['activity_id']);
            }
        }

        return $list;
    }

    protected function getListActivityIds(array $list): array
    {
        return array_unique(array_column($list['data'], 'activity_id'));
    }

    protected function queryRelationStationData(array $activity_ids): array
    {
        if (empty($activity_ids)) return [];

        $stationsData = app(CouponActivityStation::class)->batchGetActivityRelationStationData($activity_ids, [
            'cas.activity_id', 's.id', 's.name'
        ]);

        $activityToStationMap = [];
        foreach ($stationsData as $stationData) {
            $activityToStationMap[$stationData['activity_id']][] = [
                'id' => $stationData['id'],
                'name' => $stationData['name']
            ];
        }

        return $activityToStationMap;
    }

    protected function getListData(array $filters, int $page, int $limit): array
    {
        return app(CouponCollection::class)
            ->getListData($filters, $page, $limit);
    }

    protected function arrangeFilters(array $params): array
    {
        $filters = [];
        if (isset($params['user_id'])) {
            $filters[] = ['cc.user_id', '=', $params['user_id']];
        }
        if (isset($params['filter_type'])) {
            if ($params['filter_type'] === CouponCollectionListRequest::FilterTypeNotUsed) {
                // 未使用 并且 还没有过期的
                $filters[] = ['cc.status', '=', CouponCollection::StatusNotUsed];
                $filters[] = ['cc.expire_time', '>', date("Y-m-d H:i:s")];
            } else if ($params['filter_type'] === CouponCollectionListRequest::FilterTypeUsed) {
                $filters[] = ['cc.status', '=', CouponCollection::StatusUsed];
            } else if ($params['filter_type'] === CouponCollectionListRequest::FilterTypeExpire) {
                $filters[] = ['cc.expire_time', '<=', date("Y-m-d H:i:s")];
            }
        }

        return $filters;
    }

    protected function addDataPermissionsFilterConditions(AppletLoginUser $loginUser, array $params): array
    {
        $params['user_id'] = $loginUser->id;

        return $params;
    }
}