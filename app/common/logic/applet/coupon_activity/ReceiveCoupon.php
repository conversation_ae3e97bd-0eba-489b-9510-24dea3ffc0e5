<?php

namespace app\common\logic\applet\coupon_activity;

use app\common\cache\redis\entity\AppletLoginUser;
use app\common\lib\exception\RuntimeException;
use app\common\log\LogCollector;
use app\common\logic\applet\entity\ReceiveCouponRequest;
use app\common\model\CouponActivity as CouponActivityModel;
use app\common\model\CouponCollection;
use app\common\model\CouponOplog;
use app\common\model\CouponPattern;
use think\Request;

class ReceiveCoupon
{
    protected ReceiveCouponRequest $request;
    protected AppletLoginUser $loginUser;
    protected Request $tp_request;

    public function __construct(
        AppletLoginUser      $loginUser,
        ReceiveCouponRequest $request,
        Request              $tp_request
    )
    {
        $this->loginUser = $loginUser;
        $this->request = $request;
        $this->tp_request = $tp_request;
    }

    public function run(): array
    {
        // 验证优惠券是否已经领取
        $result = $this->queryIsReceive($this->loginUser->id, $this->request->activity_id);
        $this->verifyIsReceive($result);

        // 查询活动数据
        $activityData = $this->getActivityData($this->request->activity_id);

        // 验证是否能够领取这个活动的优惠券
        $this->verifyIsCanReceive($activityData);

        // 查询活动关联的所有优惠券的模板ID
        $pattern_ids = $this->getActivityAllPatternId($this->request->activity_id);

        // 计算优惠券的过期时间
        $expire_time = $this->calculationExpireTime($activityData['effective_days']);

        // 发放优惠券
        $receiveCount = $this->receiveCoupon($this->loginUser->id, $this->request->activity_id, $expire_time, $pattern_ids);
        LogCollector::collectorRunLog(sprintf("领取的优惠券数量:%s", $receiveCount));

        // 活动优惠券已领取次数 +1
        $this->increaseGrantCount($this->request->activity_id);

        // 记录操作日志
        $this->addOperationLog(
            $activityData['corp_id'], $this->loginUser->id,
            $this->tp_request->baseUrl(), $this->tp_request->post()
        );

        return [];
    }

    protected function addOperationLog(int $corp_id, int $user_id, string $op_interface, array $op_content): void
    {
        app(CouponOplog::class)->addLog(
            $corp_id,
            $user_id,
            CouponOplog::UserTypeApplet,
            $op_interface,
            json_encode($op_content)
        );
    }

    protected function queryIsReceive(int $user_id, int $activity_id): bool
    {
        return app(CouponCollection::class)->isReceive($user_id, $activity_id);
    }

    protected function verifyIsReceive(bool $is_receive): void
    {
        if ($is_receive) {
            throw new RuntimeException('当前活动的优惠券您已经领取过了！', [], RuntimeException::CodeBusinessException);
        }
    }

    protected function increaseGrantCount(int $activity_id): bool
    {
        return app(CouponActivityModel::class)->increaseGrantCount($activity_id);
    }

    protected function receiveCoupon(int $user_id, int $activity_id, string $expire_time, array $pattern_ids): int
    {
        return app(CouponCollection::class)->batchInserts(
            $user_id,
            $activity_id,
            $expire_time,
            $pattern_ids
        );
    }

    /**
     * 计算优惠券领取之后的过期时间
     *
     * @param int $effective_days 有效天数
     * @return string
     */
    protected function calculationExpireTime(int $effective_days): string
    {
        return date('Y-m-d H:i:s', strtotime(sprintf('+%d day', $effective_days)));
    }

    /**
     * 获取活动的所有优惠券模板ID
     *
     * @param int $activity_id 优惠券活动ID
     * @return array
     */
    protected function getActivityAllPatternId(int $activity_id): array
    {
        return app(CouponPattern::class)->getActivityAllPatternId($activity_id);
    }

    protected function getActivityData(int $activity_id): ?array
    {
        return app(CouponActivityModel::class)->getActivity($activity_id, [
            'corp_id', 'status', 'is_del', 'grant_max_count', 'grant_num', 'start_time', 'end_time', 'effective_days'
        ]);
    }

    protected function verifyIsCanReceive(?array $activityData): void
    {
        if (empty($activityData)) {
            throw new RuntimeException('无效活动ID', [], RuntimeException::CodeBusinessException);
        } else if ($activityData['is_del'] === CouponActivityModel::IsDelYes) {
            throw new RuntimeException('当前活动已被删除', [], RuntimeException::CodeBusinessException);
        } else if (in_array($activityData['status'], [
            CouponActivityModel::StatusCreating,
            CouponActivityModel::StatusReview
        ])) {
            throw new RuntimeException('活动未开始', [], RuntimeException::CodeBusinessException);
        } else if (
            in_array($activityData['status'], [
                CouponActivityModel::StatusGrant,
                CouponActivityModel::StatusReviewStop
            ]) &&
            time() < strtotime($activityData['start_time'])
        ) {
            throw new RuntimeException('活动未开始', [], RuntimeException::CodeBusinessException);
        } else if ($activityData['status'] === CouponActivityModel::StatusStop) {
            throw new RuntimeException('当前活动已被停用', [], RuntimeException::CodeBusinessException);
        } else if (
            $activityData['status'] === CouponActivityModel::StatusEnd ||
            time() > strtotime($activityData['end_time'])
        ) {
            throw new RuntimeException('活动已结束', [], RuntimeException::CodeBusinessException);
        } else if (
            $activityData['status'] === CouponActivityModel::StatusGrant
            && $activityData['grant_max_count'] <= $activityData['grant_num']
        ) {
            throw new RuntimeException('当前活动的优惠券已经被领取完了。', [], RuntimeException::CodeBusinessException);
        }
    }
}