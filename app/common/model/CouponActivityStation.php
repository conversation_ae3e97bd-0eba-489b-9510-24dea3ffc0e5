<?php

namespace app\common\model;

use think\db\Query;
use think\Model;

class CouponActivityStation extends Model
{
    protected $table = 'coupon_activity_station';

    public function batchAdd(int $activity_id, array $station_ids): int
    {
        $inserts = [];
        foreach ($station_ids as $station_id) {
            $inserts[] = [
                'activity_id' => $activity_id,
                'station_id' => $station_id,
                'create_time' => date('Y-m-d H:i:s')
            ];
        }
        $this->insertAll($inserts);
        return $this->getNumRows();
    }

    public function getActivityStationCount(int $activity_id): int
    {
        return $this->where('activity_id', '=', $activity_id)->count();
    }

    public function getActivityStationIds(int $activity_id): array
    {
        return $this->where('activity_id', '=', $activity_id)->column('station_id');
    }

    public function getActivityRelationStationData(int $activity_id, ?array $fields = null): array
    {
        if (is_null($fields)) $fields = 's.*';
        /**
         * @var Query $this
         */
        return $this->alias('cas')
            ->leftJoin('stations s', 's.id = cas.station_id')
            ->where('cas.activity_id', '=', $activity_id)
            ->field($fields)
            ->select()
            ->toArray();
    }

    public function batchGetActivityRelationStationData(array $activity_ids, ?array $fields = null): array
    {
        if (is_null($fields)) $fields = 's.*';
        /**
         * @var Query $this
         */
        return $this->alias('cas')
            ->leftJoin('stations s', 's.id = cas.station_id')
            ->where('cas.activity_id', 'in', $activity_ids)
            ->field($fields)
            ->select()
            ->toArray();
    }

    public function batchDelete(int $activity_id, array $station_ids): int
    {
        $where = [
            ['activity_id', '=', $activity_id],
            ['station_id', 'in', $station_ids]
        ];

        $this->where($where)->delete();
        return $this->getNumRows();
    }

}