<?php

namespace app\common\model;

use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\db\Query;
use think\facade\Db;
use think\Model;

class CouponActivity extends Model
{
    protected $table = 'coupon_activity';

    // 状态
    public const StatusCreating = 1; // 创建中
    public const StatusReview = 2; // 审核中
    public const StatusGrant = 3; // 发放中
    public const StatusEnd = 4; // 已结束
    public const StatusStop = 5; // 已停用
    public const StatusReviewStop = 6; // 停用审核中

    // 是否已删除
    public const IsDelNot = 0; // 未删除
    public const IsDelYes = 1; // 已删除


    protected function arrangeFilters(array $filters): array
    {
        $new_filters = [
            ['ca.is_del', '=', self::IsDelNot]
        ];

        if (!empty($filters['filter_id'])) {
            $new_filters[] = [
                'ca.id', '=', $filters['filter_id']
            ];
        }

        if (!empty($filters['filter_name'])) {
            $new_filters[] = [
                'ca.name', 'like', '%' . $filters['filter_name'] . '%'
            ];
        }
        if (!empty($filters['filter_status'])) {
            $new_filters[] = [
                'ca.status', '=', $filters['filter_status']
            ];
        }
        if (!empty($filters['filter_corp_id'])) {
            $new_filters[] = [
                'ca.corp_id', '=', $filters['filter_corp_id']
            ];
        }
        if (!empty($filters['filter_user_id'])) {
            $new_filters[] = [
                'ca.user_id', '=', $filters['filter_user_id']
            ];
        }

        return $new_filters;
    }

    /**
     * @param array $filters
     * @param int $page
     * @param int $limit
     * @param string $orderField
     * @param string $orderType
     * @param bool $return_sql
     * @return array
     * @throws DbException
     */
    public function getListData(
        array  $filters, int $page = 1, int $limit = 10,
        string $orderField = 'ca.id', string $orderType = 'desc', bool $return_sql = false
    ): array
    {
        $fields = [
            'ca.id', 'ca.name', 'ca.status', 'ca.grant_max_count',
            'ca.grant_num', 'ca.qcode_url', 'ca.start_time', 'ca.end_time',
            'ca.create_time', 'ca.update_time'
        ];
        /**
         * @var Query $query
         */
        $query = $this->alias('ca')
            ->leftJoin('admin_users au', 'au.id = ca.user_id')
            ->where($filters)
            ->field($fields)
            ->order($orderField, $orderType);

        if ($return_sql === true) {
            $sql = $query->buildSql();
            return ['sql' => $sql];
        }
        /**
         * @var Query $this
         */
        return $query->paginate(['list_rows' => $limit, 'page' => $page])
            ->toArray();

    }

    public function createActivity(
        string $name, int $corp_id, int $user_id,
        int    $grant_max_count, int $effective_days,
        string $start_time, string $end_time

    ): int
    {
        return $this->insertGetId([
            'name' => $name,
            'corp_id' => $corp_id,
            'user_id' => $user_id,
            'status' => self::StatusCreating,
            'grant_rule' => 0,
            'grant_max_count' => $grant_max_count,
            'grant_user_count' => 1, // 已经废弃的字段
            'effective_days' => $effective_days,
            'qcode_url' => '',
            'grant_num' => 0,
            'is_del' => self::IsDelNot,
            'start_time' => $start_time,
            'end_time' => $end_time,
            'create_time' => date('Y-m-d H:i:s'),
            'update_time' => null
        ]);
    }

    public function updateActivity(int $id, array $updateData): bool
    {
        $this->where('id', '=', $id)->update($updateData);
        return $this->getNumRows() > 0;
    }

    public function deleteActivity(int $activity_id): int
    {
        $this->where('id', '=', $activity_id)->update([
            'is_del' => self::IsDelYes,
            'update_time' => date('Y-m-d H:i:s')
        ]);
        return $this->getNumRows();
    }

    public function getActivityCount(): int
    {
        return $this->where('is_del', '=', self::IsDelNot)->count();
    }

    /**
     * @param int $activity_id
     * @param array $field
     * @return array|null
     * @throws DbException
     * @throws DataNotFoundException
     * @throws ModelNotFoundException
     */
    public function getActivity(int $activity_id, array $field = []): ?array
    {
        $where = [
            ['id', '=', $activity_id]
        ];
        $data = $this->where($where)->field($field)->find();
        if (empty($data)) return null;
        return $data->toArray();
    }


    public function getActivityInfo(array $filters, ?array $fields = null): ?array
    {
        if (empty($fields)) {
            $fields = [
                'id', 'name', 'status', 'grant_max_count',
                'grant_num', 'effective_days', 'qcode_url',
                'start_time', 'end_time', 'create_time', 'update_time'
            ];
        }


        $data = $this->where($filters)->field($fields)->find();
        if (empty($data)) return null;
        return $data->toArray();
    }

    public function updateActivityStatus(int $activity_id, int $status): bool
    {
        $where = [
            ['id', '=', $activity_id]
        ];
        $this->where($where)->update([
            'status' => $status,
            'update_time' => date('Y-m-d H:i:s')
        ]);
        return $this->getNumRows() > 0;
    }

    public function updateActivityStatusAndQrCode(int $activity_id, int $status, string $qrcode): bool
    {
        $where = [
            ['id', '=', $activity_id]
        ];
        $this->where($where)->update([
            'status' => $status,
            'qcode_url' => $qrcode,
            'update_time' => date('Y-m-d H:i:s')
        ]);
        return $this->getNumRows() > 0;
    }

    /**
     * 发放次数自增1
     */
    public function increaseGrantCount(int $activity_id): bool
    {
        $this->where('id', '=', $activity_id)->update([
            'grant_num' => Db::raw('grant_num+1'),
            'update_time' => date('Y-m-d H:i:s')
        ]);
        return $this->getNumRows() > 0;
    }


    public function getOptionsData(array $filters): array
    {
        $fields = [
            'id', 'name',
        ];

        return $this->where($filters)
            ->field($fields)
            ->select()
            ->toArray();
    }
}