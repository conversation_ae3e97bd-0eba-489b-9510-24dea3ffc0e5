<?php

namespace app\common\model;

use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\db\Query;
use think\Model;

class CouponCollection extends Model
{
    protected $table = 'coupon_collection';

    // 使用状态
    public const StatusNotUsed = 0; // 未使用
    public const StatusUsed = 1; // 已使用

    /**
     * 查询用户是否已经领取过了指定活动的优惠券
     *
     * @param int $user_id
     * @param int $activity_id
     * @return bool false:未领取 true:已领取
     */
    public function isReceive(int $user_id, int $activity_id): bool
    {
        $where = [
            ['user_id', '=', $user_id],
            ['activity_id', '=', $activity_id]
        ];
        return $this->where($where)->count() > 0;
    }

    public function batchInserts(int $user_id, int $activity_id, string $expire_time, array $pattern_ids): int
    {
        $inserts = [];
        foreach ($pattern_ids as $pattern_id) {
            $inserts[] = [
                'pattern_id' => $pattern_id,
                'activity_id' => $activity_id,
                'user_id' => $user_id,
                'status' => self::StatusNotUsed,
                'expire_time' => $expire_time,
                'create_time' => date('Y-m-d H:i:s')
            ];
        }
        $this->insertAll($inserts);
        return $this->getNumRows();
    }

    public function getActivityCoupons(int $user_id, int $activity_id): array
    {
        $where = [
            ['cc.user_id', '=', $user_id],
            ['cc.activity_id', '=', $activity_id]
        ];
        /**
         * @var Query $this
         */
        return $this->alias('cc')
            ->leftJoin('coupon_pattern cp', 'cp.id = cc.pattern_id')
            ->where($where)
            ->field([
                'cc.id', 'cp.name', 'cp.par_value', 'cp.usage_amount',
                'cc.status', 'cc.expire_time',
            ])
            ->select()
            ->toArray();
    }

    /**
     * @param array $filters
     * @param int $page
     * @param int $limit
     * @param string $orderField
     * @param string $orderType
     * @return array
     * @throws DbException
     */
    public function getListData(
        array  $filters, int $page = 1, int $limit = 10,
        string $orderField = 'cc.create_time', string $orderType = 'desc'
    ): array
    {
        $fields = [
            'cc.id', 'cp.name', 'cc.status', 'cc.expire_time',
            'cp.par_value', 'cp.usage_amount', 'cc.activity_id'
        ];

        /**
         * @var Query $this
         */
        return $this->alias('cc')
            ->leftJoin('coupon_pattern cp', 'cp.id = cc.pattern_id')
            ->where($filters)
            ->field($fields)
            ->order($orderField, $orderType)
            ->paginate(['list_rows' => $limit, 'page' => $page])
            ->toArray();
    }

    /**
     * @param array $filters
     * @param int $page
     * @param int $limit
     * @param string $orderField
     * @param string $orderType
     * @return array
     * @throws DbException
     */
    public function getAdminListData(
        array  $filters, int $page = 1, int $limit = 10,
        string $orderField = 'cc.create_time', string $orderType = 'desc'
    ): array
    {
        $fields = [
            'cc.id', 'u.phone', 'cp.name', 'cc.status',
            'cp.par_value', 'cp.usage_amount',
            'cc.create_time', 'cc.use_time', 'cc.expire_time',
            'ca.name as activity_name'
        ];
        /**
         * @var Query $this
         */
        return $this->alias('cc')
            ->leftJoin('coupon_pattern cp', 'cp.id = cc.pattern_id')
            ->leftJoin('users u', 'u.id = cc.user_id')
            ->leftJoin('coupon_activity ca', 'ca.id = cc.activity_id')
            ->where($filters)
            ->field($fields)
            ->order($orderField, $orderType)
            ->paginate(['list_rows' => $limit, 'page' => $page])
            ->toArray();
    }

    public function getInfoData(array $filters, ?array $fields = null): ?array
    {
        if (empty($fields)) {
            $fields = [
                'cc.id', 'cc.activity_id', 'u.phone', 'cc.status',
                'cp.name', 'cp.par_value', 'cp.usage_amount',
                'cco.order_id', 'cc.create_time', 'cc.use_time', 'cc.expire_time',
            ];
        }


        /**
         * @var Query $this
         */
        $data = $this->alias('cc')
            ->leftJoin('coupon_activity ca', 'ca.id = cc.activity_id')
            ->leftJoin('coupon_pattern cp', 'cp.id = cc.pattern_id')
            ->leftJoin('coupon_collection_order cco', 'cco.collection_id = cc.id')
            ->leftJoin('users u', 'u.id = cc.user_id')
            ->where($filters)
            ->field($fields)
            ->find();

        if (empty($data)) return null;
        return $data->toArray();
    }

    /**
     * 找出符合指定场站使用的优惠券
     *
     * @param int $user_id 用户ID
     * @param int $station_id 场站ID
     * @return array
     * @throws DbException
     * @throws DataNotFoundException
     * @throws ModelNotFoundException
     */
    public function getStationEffectiveCoupon(int $user_id, int $station_id): array
    {
        $where = [
            // 属于用户的
            ['cc.user_id', '=', $user_id],
            // 还没有被使用的
            ['cc.status', '=', self::StatusNotUsed],
            // 适用于指定场站的
            ['cas.station_id', '=', $station_id],
            // 还未过期的
            ['cc.expire_time', '>=', date('Y-m-d H:i:s')]
        ];

        $fields = [
            'cc.id', 'cp.par_value', 'cp.usage_amount', 'cc.expire_time'
        ];

        /**
         * @var Query $this
         */
        return $this->alias('cc')
            ->leftJoin('coupon_pattern cp', 'cp.id = cc.pattern_id')
            ->leftJoin('coupon_activity_station cas', 'cas.activity_id = cc.activity_id')
            ->field($fields)
            ->where($where)
            ->group('cc.id')
            ->select()
            ->toArray();
    }

    public function useCoupon(int $id): bool
    {
        $this->where('id', '=', $id)->update([
            'status' => self::StatusUsed,
            'use_time' => date('Y-m-d H:i:s')
        ]);
        return $this->getNumRows() > 0;
    }

    /**
     * 给用户的优惠券数据加上排他锁
     * 在事务未提交之前，其他会话无法再次获取到该用户的优惠券，也不能更新该用户的优惠券数据。
     * 事务提交后，排他锁自动解锁。
     *
     * @param int $user_id
     * @return int 锁住的行数
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function lockUserCoupon(int $user_id): int
    {
        $data = $this->where('user_id', '=', $user_id)
            ->field(['id'])
            ->lock(true)
            ->select()
            ->toArray();

        return count($data);
    }
}