<?php

namespace app\common\model;

use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\db\Query;
use think\Model;

class CouponPattern extends Model
{
    protected $table = 'coupon_pattern';

    public function getActivityPatternCount(int $activity_id): int
    {
        return $this->where('activity_id', '=', $activity_id)->count();
    }


    public function createPattern(
        string $name, int $activity_id, int $par_value, int $usage_amount, int $user_id
    ): int
    {
        return $this->insertGetId([
            'name' => $name,
            'activity_id' => $activity_id,
            'par_value' => $par_value,
            'usage_amount' => $usage_amount,
            'user_id' => $user_id,
            'create_time' => date('Y-m-d H:i:s')
        ]);
    }

    public function updatePattern(
        int $pattern_id, array $update
    ): bool
    {
        $this->where('id', '=', $pattern_id)->update($update);
        return $this->getNumRows() > 0;
    }

    public function getPattern(int $id, ?array $fields = null): ?array
    {
        if (is_null($fields)) $fields = '*';

        $data = $this->where('id', '=', $id)->field($fields)->find();
        if (empty($data)) return null;
        return $data->toArray();
    }


    public function getListData(
        array  $filters, int $page = 1, int $limit = 10,
        string $orderField = 'cp.id', string $orderType = 'desc', bool $return_sql = false
    ): array
    {
        $fields = [
            'cp.id', 'cp.name', 'cp.par_value', 'cp.usage_amount',
            'cp.create_time',
        ];
        /**
         * @var Query $this
         */
        $query = $this->alias('cp')
            ->leftJoin('coupon_activity ca', 'ca.id = cp.activity_id')
            ->where($filters)
            ->field($fields)
            ->order($orderField, $orderType);

        if ($return_sql === true) {
            $sql = $query->buildSql();
            return ['sql' => $sql];
        }
        /**
         * @var Query $this
         */
        return $query->paginate(['list_rows' => $limit, 'page' => $page])
            ->toArray();
    }


    public function getActivityAllPattern(
        int $activity_id, bool $return_sql = false
    ): array
    {
        $fields = [
            'id', 'name', 'par_value', 'usage_amount',
        ];
        /**
         * @var Query $this
         */
        $query = $this->alias('cp')
            ->where('cp.activity_id', '=', $activity_id)
            ->field($fields);

        if ($return_sql === true) {
            $sql = $query->buildSql();
            return ['sql' => $sql];
        }
        /**
         * @var Query $this
         */
        return $query->select()
            ->toArray();
    }

    public function getActivityAllPatternId(
        int $activity_id, bool $return_sql = false
    ): array
    {
        $fields = [
            'id', 'name', 'par_value', 'usage_amount',
        ];
        /**
         * @var Query $this
         */
        $query = $this->alias('cp')
            ->where('cp.activity_id', '=', $activity_id)
            ->field($fields);

        if ($return_sql === true) {
            $sql = $query->buildSql();
            return ['sql' => $sql];
        }
        /**
         * @var Query $this
         */
        return $query->column('cp.id');
    }

    /**
     * @param array $filters
     * @return array|null
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getPatternInfo(array $filters): ?array
    {
        /**
         * @var Query $this
         */
        $data = $this->alias('cp')
            ->leftJoin('coupon_activity ca', 'ca.id = cp.activity_id')
            ->where($filters)
            ->field([
                'cp.id', 'cp.name', 'cp.par_value', 'cp.usage_amount', 'cp.create_time'
            ])
            ->find();
        if (empty($data)) return null;
        return $data->toArray();
    }


    /**
     * 基于现金券ID获取现金券活动数据
     *
     * @param int $pattern_id 现金券ID
     * @param ?array $fields = null
     * @return ?array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getCouponActivityBasedOnPatternId(int $pattern_id, ?array $fields = null): ?array
    {
        if (is_null($fields)) $fields = 'ca.*';

        /**
         * @var Query $this
         */
        $data = $this->alias('cp')
            ->leftJoin('coupon_activity ca', 'ca.id = cp.activity_id')
            ->where('cp.id', '=', $pattern_id)
            ->field($fields)
            ->find();
        if (empty($data)) return null;

        return $data->toArray();
    }


    public function deletePattern(int $pattern_id): bool
    {
        return $this->where('id', '=', $pattern_id)->delete();
    }
}