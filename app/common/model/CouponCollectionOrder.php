<?php

namespace app\common\model;

use think\Model;

class CouponCollectionOrder extends Model
{
    protected $table = 'coupon_collection_order';

    public function recordUseRelation(string $order_id, int $coupon_collection_id): int
    {
        $this->insert([
            'order_id' => $order_id,
            'collection_id' => $coupon_collection_id,
            'create_time' => date('Y-m-d H:i:s')
        ]);
        return $this->getNumRows();
    }
}