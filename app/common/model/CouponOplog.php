<?php

namespace app\common\model;

use think\db\Query;
use think\Model;

class CouponOplog extends Model
{
    protected $table = 'coupon_oplog';

    public const UserTypeAdmin = 0; // 后台用户
    public const UserTypeApplet = 1; // 小程序用户

    public function addLog(int $corp_id, int $user_id, int $user_type, string $op_interface, string $op_content): int
    {
        return $this->insertGetId([
            'corp_id' => $corp_id,
            'user_id' => $user_id,
            'user_type' => $user_type,
            'op_interface' => $op_interface,
            'op_content' => $op_content,
            'op_time' => date('Y-m-d H:i:s')
        ]);
    }

    public function getListData(
        array  $filters, int $page = 1, int $limit = 10,
        string $orderField = 'ca.id', string $orderType = 'desc', bool $return_sql = false
    ): array
    {
        $fields = [
            'co.id', 'co.user_type', 'co.user_id', 'co.op_interface',
            'co.op_content', 'co.op_time'
        ];

        return $this->alias('co')
            ->where($filters)
            ->field($fields)
            ->order($orderField, $orderType)
            ->paginate(['list_rows' => $limit, 'page' => $page])
            ->toArray();

    }
}