<?php
/** @noinspection PhpUnused */
declare(strict_types=1);

namespace app\common\queue;

use app\common\model\UserBalanceLog as UserBalanceLogModel;
use app\common\model\Users;
use app\common\lib\SendApplet;
use think\facade\Log;
use Throwable;

/**
 * 微信支付通知 RabbitMQ 消费者
 *
 * 用于处理来自 order 交换机的微信支付成功通知
 * 业务逻辑：只给用户增加余额，不处理订单状态
 */
class WechatPayNotifyConsumer extends BaseConsumer
{
    private const EXCHANGE_NAME = 'order';
    private const EXCHANGE_TYPE = 'direct';
    private const QUEUE_NAME = 'order_pay_notify_wechat';
    private const ROUTING_KEY = 'order.pay.notify.wechat';

    /**
     * 构造函数
     */
    public function __construct()
    {
        // 调用父类构造函数，设置交换机和队列信息
        parent::__construct(
            self::EXCHANGE_NAME,    // 交换机名称
            self::QUEUE_NAME,       // 队列名称
            self::EXCHANGE_TYPE,    // 交换机类型
            self::ROUTING_KEY       // 路由键
        );
    }

    /**
     * 处理消息的业务逻辑（实现基类的抽象方法）
     *
     * @param string $messageBody 消息内容
     * @return string 处理结果常量
     */
    protected function processMessage(string $messageBody): string
    {
        return $this->handlePaymentNotification($messageBody);
    }

    /**
     * 处理支付通知业务逻辑
     *
     * @param string $messageBody
     * @return string
     */
    private function handlePaymentNotification(string $messageBody): string
    {
        try {
            // 解析 JSON 消息
            $payData = json_decode($messageBody, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                Log::error('JSON 解析失败: ' . json_last_error_msg());
                return parent::RESULT_SKIP; // JSON 格式错误，跳过不重试
            }

            $userId = (int)$payData['user_id'];
            $amount = (int)$payData['price'];
            $orderId = $payData['id'] ?? '';

            Log::info(sprintf('开始处理用户[%d]余额增加[%d分]，订单号[%s]', $userId, $amount, $orderId));

            // 添加用户余额日志
            $addResult = app(UserBalanceLogModel::class)->add(
                $userId,
                $amount,
                '支付成功，加用户余额：' . $amount . '分，订单号：' . $orderId,
                '充值'
            );

            if (empty($addResult)) {
                Log::error('添加余额日志失败');
                return parent::RESULT_FAILED;
            }

            // 增加用户余额
            $increaseBalanceResult = app(Users::class)->increaseBalance($userId, $amount);
            if ($increaseBalanceResult === 0) {
                Log::error('增加用户余额失败');
                return parent::RESULT_FAILED;
            }

            Log::info(sprintf('用户[%d]余额增加成功，金额[%d分]', $userId, $amount));

        } catch (Throwable $e) {
            Log::error(sprintf('处理支付通知异常: %s', $e->getMessage()));
            return parent::RESULT_FAILED;
        }

        try {
            // 发送小程序通知
            SendApplet::send_uid($userId, [
                'type' => 'pay_notice',
                'result' => 'success',
                'msg' => '支付成功，加余额成功'
            ]);
        } catch (Throwable $e) {
            Log::error(sprintf('发送小程序通知失败: %s', $e->getMessage()));
        }

        return parent::RESULT_SUCCESS;
    }

}
