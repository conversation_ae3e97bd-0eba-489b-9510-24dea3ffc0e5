<?php

namespace app\common\lib\charging\applet\request;

use app\common\model\Order;

class StartChargeRequest extends BaseRequest
{
    public string $user_token;
    // 订单关联的用户ID
    public int $user_id;


//    public AppletLoginUser $loginUser;
    public int $shots_id;
    public ?string $reservation_time = null;
    public ?int $power_limit = null;
    // 支付方式,默认为余额
    public int $pay_mode = Order::PayModeBalance;
}