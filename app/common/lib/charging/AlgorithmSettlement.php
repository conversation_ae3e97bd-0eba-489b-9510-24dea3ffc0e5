<?php
declare (strict_types=1);

namespace app\common\lib\charging;

// 算法结算
use app\common\lib\charging\request\TransactionRecordRequest;
use app\common\lib\WeChat;
use app\common\log\SocketLogCollector;
use app\common\model\Users;
use app\ms\Api;
use think\facade\Log;
use Throwable;

trait AlgorithmSettlement
{
    /**
     * 计算(尖|峰|平|谷)纯充电费用
     * 不包含服务费用
     *
     * @param int $electricity 充电量(精确小数点后4位)
     * @param int $price 电价(精确到小数点后5位)
     * @return int 纯充电费用(精确到小数点后4位)
     */
    public static function calculateChargeCost(int $electricity, int $price): int
    {
        return (int)ceil($electricity * $price / 100000);
    }

    /**
     * 计算(尖|峰|平|谷)服务费用
     *
     * @param int $electricity 充电量(精确小数点后4位)
     * @param int $service_price 服务价格(精确到小数点后5位)
     * @param float $discount 优惠折扣(精确到小数点后1位)
     * @return int 服务费用(精确到小数点后4位)
     */
    public static function calculateServiceCost(int $electricity, int $service_price, float $discount): int
    {
        return (int)ceil($electricity * $service_price * ($discount / 100) / 100000);
    }

    /**
     * 计算(尖|峰|平|谷)纯充电费用
     *  不包含服务费用
     *
     * @param int $electricity 充电量(精确小数点后4位)
     * @param int $price 电价(精确到小数点后5位)
     * @param int $service_price 服务价格(精确到小数点后5位)
     * @param float $discount 优惠折扣(精确到小数点后1位)
     * @return int 纯充电费用(精确到小数点后4位)
     */
    public static function calculateCost(int $electricity, int $price, int $service_price, float $discount): int
    {
        $chargeCost = self::calculateChargeCost($electricity, $price);
        $service_cost = self::calculateServiceCost($electricity, $service_price, $discount);

        return $service_cost + $chargeCost;
    }

    public function abnormalSettlement(
        array $orderData
    ): SettlementResult
    {
        $result = [];
        $electricityData = [
            'sharp' => $orderData['sharp_electricity'],
            'peak' => $orderData['peak_electricity'],
            'flat' => $orderData['flat_electricity'],
            'valley' => $orderData['valley_electricity'],
            'db_sharp' => $orderData['sharp_electricity'],
            'db_peak' => $orderData['peak_electricity'],
            'db_flat' => $orderData['flat_electricity'],
            'db_valley' => $orderData['valley_electricity']
        ];

        // 检查充电量是否超过理论最大值
        $processedElectricity = $this->checkMaxElectricity($electricityData, $orderData['charge_duration'], $orderData['id'], '异常结算');

        // 计算费用详情
        $result = $this->calculateCostDetail($processedElectricity, $orderData, $result);

        $result['electricity_total'] = $processedElectricity['total'];
        $result['original_total_service_cost'] = $result['original_sharp_service_cost'] + $result['original_peak_service_cost'] + $result['original_flat_service_cost'] + $result['original_valley_service_cost'] + $result['surcharge'];
        $result['total_service_cost'] = $result['sharp_service_cost'] + $result['peak_service_cost'] + $result['flat_service_cost'] + $result['valley_service_cost'] + $result['surcharge'];
        $result['total_charge_cost'] = $result['sharp_charge_cost'] + $result['peak_charge_cost'] + $result['flat_charge_cost'] + $result['valley_charge_cost'];
        // 实际支付的费用
        $result['total_cost'] = (int)ceil(($result['total_service_cost'] + $result['total_charge_cost']) / 100);
        // 原本需要支付的费用
        $result['original_total_cost'] = (int)ceil(($result['original_total_service_cost'] + $result['total_charge_cost']) / 100);
        $result['type'] = SettlementResult::TypeAbnormalSettlement;

        return new SettlementResult($result);
    }

    public function normalSettlement(
        TransactionRecordRequest $request,
        array                    $orderData
    ): SettlementResult
    {
        $result = [];
        $electricityData = [
            'sharp' => $request->sharp_charge,
            'peak' => $request->peak_charge,
            'flat' => $request->flat_charge,
            'valley' => $request->valley_charge,
            'db_sharp' => $orderData['sharp_electricity'],
            'db_peak' => $orderData['peak_electricity'],
            'db_flat' => $orderData['flat_electricity'],
            'db_valley' => $orderData['valley_electricity']
        ];

        // 检查充电量是否超过理论最大值
        $processedElectricity = $this->checkMaxElectricity($electricityData, $this->orderData['charge_duration'], $this->request->transaction_serial_number);

        // 使用处理后的充电量
        $result = $this->calculateCostDetail($processedElectricity, $orderData, $result);


        $result['total_service_cost'] = max(0,$result['sharp_service_cost'] + $result['peak_service_cost'] + $result['flat_service_cost'] + $result['valley_service_cost'] + $result['surcharge']);
        $result['original_total_service_cost'] = max(0,$result['original_sharp_service_cost'] + $result['original_peak_service_cost'] + $result['original_flat_service_cost'] + $result['original_valley_service_cost'] + $result['surcharge']);
        $result['total_charge_cost'] = $result['sharp_charge_cost'] + $result['peak_charge_cost'] + $result['flat_charge_cost'] + $result['valley_charge_cost'];
        $result['original_total_cost'] = (int)ceil(($result['original_total_service_cost'] + $result['total_charge_cost']) / 100);
        $result['total_cost'] = (int)ceil(($result['total_service_cost'] + $result['total_charge_cost']) / 100);
        $result['type'] = SettlementResult::TypeNormalSettlement;

        return new SettlementResult($result);
    }

    /**
     * 检查充电量是否超过理论最大值
     *
     * @param array $electricityData 包含各时段充电量的数组
     * @param int $chargeDuration 充电时长（秒）
     * @param string $orderId 订单ID
     * @param string $orderType 订单类型，用于日志记录
     * @return array 处理后的充电量数据
     */
    protected function checkMaxElectricity(array $electricityData, int $chargeDuration, string $orderId, string $orderType = '正常结算'): array
    {
        // 记录原始充电量
        $this->socketLogCollector->collectorRunLog(sprintf('%s - 设备记录的尖时充电量:%d | 服务端记录的尖时充电量:%d',
            $orderType, $electricityData['sharp'], $electricityData['db_sharp']));
        $this->socketLogCollector->collectorRunLog(sprintf('%s - 设备记录的峰时充电量:%d | 服务端记录的峰时充电量:%d',
            $orderType, $electricityData['peak'], $electricityData['db_peak']));
        $this->socketLogCollector->collectorRunLog(sprintf('%s - 设备记录的平时充电量:%d | 服务端记录的平时充电量:%d',
            $orderType, $electricityData['flat'], $electricityData['db_flat']));
        $this->socketLogCollector->collectorRunLog(sprintf('%s - 设备记录的谷时充电量:%d | 服务端记录的谷时充电量:%d',
            $orderType, $electricityData['valley'], $electricityData['db_valley']));

        // 取最大值
        $sharp_electricity = max($electricityData['sharp'], $electricityData['db_sharp']);
        $peak_electricity = max($electricityData['peak'], $electricityData['db_peak']);
        $flat_electricity = max($electricityData['flat'], $electricityData['db_flat']);
        $valley_electricity = max($electricityData['valley'], $electricityData['db_valley']);
        $total_electricity = $sharp_electricity + $peak_electricity + $flat_electricity + $valley_electricity;

        // 理论最大充电量 = 充电时长 * 70000 * 1.2(20%误差)
        $max_electricity = ($chargeDuration / 3600) * 70000 * 1.2;
        // 如果超过理论最大充电量,则全部为0不计费,后续手动处理
        if ($total_electricity > $max_electricity) {
            $sharp_electricity = 0;
            $peak_electricity = 0;
            $flat_electricity = 0;
            $valley_electricity = 0;

            Log::error('超过理论最大充电量,订单号:'. $orderId);
            $errorMsg = '充电时长:'. $chargeDuration .'|理论最大充电量:' . $max_electricity. '|实际充电量:' . $total_electricity;
            Log::error($errorMsg);

            // 写入订单异常表
            $this->orderData['id'] = $this->request->transaction_serial_number;
            $this->orderData['electricity_charged'] = $total_electricity;
            $this->orderData['msg'] = '充电时长:'. ($this->orderData['charge_duration']).'|理论最大充电量:' . $max_electricity. '|实际充电量:' . $total_electricity;
            $this->orderData['type'] = '超过理论最大充电量';

            // 写入订单异常表
            Api::send("/order/orders/exception",'POST', $this->orderData, ['Authorization' => env('SERVER_TOKEN')]);
        }

        $total_electricity = 0;
        // 如果没有超过理论最大充电量则原样返回,超过则返回0
        return [
            'sharp' => $sharp_electricity,
            'peak' => $peak_electricity,
            'flat' => $flat_electricity,
            'valley' => $valley_electricity,
            'total' => $total_electricity
        ];
    }

    /**
     * @param array $processedElectricity
     * @param array $orderData
     * @param array $result
     * @return array
     */
    public function calculateCostDetail(array $processedElectricity, array $orderData, array $result): array
    {
        $sharp_electricity = $processedElectricity['sharp'];
        $peak_electricity = $processedElectricity['peak'];
        $flat_electricity = $processedElectricity['flat'];
        $valley_electricity = $processedElectricity['valley'];
        $electricity_total = $processedElectricity['total'];

        // 尖时充电费用
        $result['sharp_charge_cost'] = self::calculateChargeCost(
            $sharp_electricity,
            $orderData['sharp_fee']
        );
        // 峰时充电费用
        $result['peak_charge_cost'] = self::calculateChargeCost(
            $peak_electricity,
            $orderData['peak_fee']
        );
        // 平时充电费用
        $result['flat_charge_cost'] = self::calculateChargeCost(
            $flat_electricity,
            $orderData['flat_fee'],
        );
        // 谷时充电费用
        $result['valley_charge_cost'] = self::calculateChargeCost(
            $valley_electricity,
            $orderData['valley_fee'],
        );

        // 尖时服务费用
        $result['sharp_service_cost'] = self::calculateServiceCost(
            $sharp_electricity,
            $orderData['sharp_ser_fee'],
            $orderData['discount']
        );
        $result['original_sharp_service_cost'] = self::calculateServiceCost(
            $sharp_electricity,
            $orderData['sharp_ser_fee'],
            100
        );
        // 峰时服务费用
        $result['peak_service_cost'] = self::calculateServiceCost(
            $peak_electricity,
            $orderData['peak_ser_fee'],
            $orderData['discount']
        );
        $result['original_peak_service_cost'] = self::calculateServiceCost(
            $peak_electricity,
            $orderData['peak_ser_fee'],
            100
        );
        // 平时服务费用
        $result['flat_service_cost'] = self::calculateServiceCost(
            $flat_electricity,
            $orderData['flat_ser_fee'],
            $orderData['discount']
        );
        $result['original_flat_service_cost'] = self::calculateServiceCost(
            $flat_electricity,
            $orderData['flat_ser_fee'],
            100
        );
        // 谷时服务费用
        $result['valley_service_cost'] = self::calculateServiceCost(
            $valley_electricity,
            $orderData['valley_ser_fee'],
            $orderData['discount']
        );
        $result['original_valley_service_cost'] = self::calculateServiceCost(
            $valley_electricity,
            $orderData['valley_ser_fee'],
            100
        );
        // 附加费用(所有时段电量 * 每度电附加费率)
        $result['surcharge'] = self::calculateServiceCost(
            $electricity_total,
            $orderData['surcharge'],
            100
        );
        return $result;
    }

    /**
     * 发送充电完成微信通知
     *
     * @param array $orderData 订单数据
     * @param SocketLogCollector $socketLogCollector 日志收集器
     * @return void
     */
    public function sendWechatEndNotification(array $orderData, SocketLogCollector $socketLogCollector): void
    {
        try {
            if (empty($orderData)) {
                $socketLogCollector->collectorRunLog('发送充电完成微信通知失败：找不到订单信息', SocketLogCollector::LevelError);
                return;
            }

            // 获取用户open_id
            $usersModel = app(Users::class);
            $userData = $usersModel->where('id', $orderData['user_id'])->field(['open_id'])->find();

            if (empty($userData) || empty($userData['open_id'])) {
                $socketLogCollector->collectorRunLog('发送充电完成微信通知失败：找不到用户open_id', SocketLogCollector::LevelError);
                return;
            }

            // 格式化金额，从分转为元
            $amount = number_format($orderData['pay_money'] / 100, 2) . '元';

            // 格式化电量，从瓶时转为度
            $electricity = number_format($orderData['electricity_total'] / 100, 2) . 'kWh';

            // 结束原因
            $endReason = $orderData['msg'];

            // 构建消息参数
            $params = [
                'character_string1' => ['value' => $orderData['id']], // 订单编号
                'time7' => ['value' => date('Y-m-d H:i:s')], // 结束时间
                'character_string8' => ['value' => $electricity], // 充电量
                'amount6' => ['value' => $amount], // 消费金额
                'thing9' => ['value' => $endReason] // 结束原因
            ];

            // 发送微信模板消息
            $wechat = app(WeChat::class);
            $result = $wechat->send_template_message($userData['open_id'], $orderData['id'], $params, 'end');

            $socketLogCollector->collectorRunLog('发送充电完成微信通知结果：' . json_encode_cn($result));
        } catch (Throwable $e) {
            $socketLogCollector->collect($e);
        }
    }
}
