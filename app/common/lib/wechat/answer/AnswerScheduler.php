<?php

namespace app\common\lib\wechat\answer;

use app\common\lib\ExceptionLogCollector;
use app\common\lib\wechat\AesUtil;
use app\common\lib\wechat\entity\answer\FapiaoCompleteAnswer;
use app\common\lib\wechat\entity\answer\TitleCompleteAnswer;
use app\common\lib\wechat\Sign;
use think\Container;
use think\Request;
use think\response\Json;

class AnswerScheduler
{

    protected string $mch_id = '';
    protected string $api_client_key = '';
    protected string $api_client_key_sn = '';
    protected string $api_client_public_key = '';

    public function __construct()
    {
        $this->mch_id = config('wechat.mch_id');
        $this->api_client_key_sn = config('wechat.apiclient_key_sn');
        $this->api_client_key = config('wechat.apiclient_key');
        $this->api_client_public_key = config('wechat.api_client_public_key');
    }

    public function handler(Request $request): Json
    {
//        $wechatpaySerial = $request->header('wechatpay-serial');
//        if ($wechatpaySerial !== $this->api_client_key_sn) {
//            ExceptionLogCollector::recordErrorLog('微信支付平台证书序列号不匹配', debug_backtrace());
//            return $this->failResponse([
//                "code" => "FAIL",
//                "message" => "微信支付平台证书序列号不匹配"
//            ]);
//        }

        // 验证签名
        $timestamp = $request->header('wechatpay-timestamp');
        $nonce = $request->header('wechatpay-nonce');
        $body = $request->getContent();
        $signature = $request->header('wechatpay-signature');
        ExceptionLogCollector::recordErrorLog('$timestamp = ' . $timestamp);
        ExceptionLogCollector::recordErrorLog('$nonce = ' . $nonce);
        ExceptionLogCollector::recordErrorLog('$body = ' . $body);
        ExceptionLogCollector::recordErrorLog('$signature = ' . $signature);

        $Sign = new Sign();
        if (!$Sign->verifySignature((int)$timestamp, $nonce, $body, $signature)) {
            ExceptionLogCollector::recordErrorLog('签名验证失败', debug_backtrace());
            return $this->failResponse([
                "code" => "FAIL",
                "message" => "签名验证失败"
            ]);
        }

        // 解密证书和回调报文
        $body = json_decode($body, true);
        $AesUtil = new AesUtil();
        try {
            $data = $AesUtil->decryptToString(
                $body['resource']['associated_data'],
                $body['resource']['nonce'],
                $body['resource']['ciphertext']
            );

            switch ($body['event_type']) {
                // 用户填写完成抬头信息
                case 'FAPIAO.USER_APPLIED':
                    $answer = new TitleCompleteAnswer(json_decode($data, true));
                    (new TitleCompleteAnswerHandler())->businessHandler($answer);
                    break;
                // 开具发票完成
                case 'FAPIAO.ISSUED':
                    $answer = new FapiaoCompleteAnswer(json_decode($data, true));
                    (new FapiaoCompleteAnswerHandler())->businessHandler($answer);
                    break;
                // 发票卡券已插入用户卡包
                case 'FAPIAO.CARD_INSERTED':
                    $answer = $this->successResponse();
                    break;
                default:
                    ExceptionLogCollector::recordErrorLog('未知微信回调事件类型', debug_backtrace());
                    throw new \RuntimeException('未知微信回调事件类型');
            }

            return $this->successResponse();

        } catch (\Throwable $e) {
            ExceptionLogCollector::collect($e);
            return $this->failResponse([
                "code" => "FAIL",
                "message" => "服务器异常"
            ], 500);
        }
    }

    protected function failResponse(array $data, int $httpCode = 400): Json
    {
        return Container::getInstance()->invokeClass(Json::class, [$data, $httpCode]);
    }

    protected function successResponse(array $data = [], int $httpCode = 200): Json
    {
        return Container::getInstance()->invokeClass(Json::class, [$data, $httpCode]);
    }
}