<?php
/** @noinspection PhpUnused */

namespace app\admin\controller;


use app\common\logic\admin\common\UploadImage;
use hg\apidoc\annotation as Apidoc;
use think\exception\ValidateException;
use think\facade\Filesystem;
use think\response\Json;
use Throwable;
use app\common\model\File as FileModel;

#[Apidoc\Title("公共模块")]
class Common extends BaseController
{

    #[
        Apidoc\Title("上传图片"),
        Apidoc\Author("lwj 2023.8.4 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/Common/upload_img"),
        Apidoc\Param(name: "file", type: "file", require: true, desc: "图片文件"),
        Apidoc\Returned(name: "id", type: "int", require: true, desc: "图片ID"),
        Apidoc\Returned(name: "name", type: "string", require: true, desc: "图片名称"),
        Apidoc\Returned(name: "size", type: "int", require: true, desc: "图片大小(单位:字节)"),
        Apidoc\Returned(name: "md5", type: "string", require: true, desc: "MD5"),
        Apidoc\Returned(name: "sha1", type: "string", require: true, desc: "SHA1"),
        Apidoc\Returned(name: "type", type: "string", require: true, desc: "图片类型"),
        Apidoc\Returned(name: "format", type: "string", require: true, desc: "图片格式"),
        Apidoc\Returned(name: "url", type: "string", require: true, desc: "图片访问地址"),
        Apidoc\Returned(name: "user_id", type: "int", require: true, desc: "上传图片的用户ID")
    ]
    public function upload_img(): Json
    {
        return $this->openExceptionCatch(function () {
            return (new UploadImage($this->request, $this->loginUser))->run();
        });
    }

    #[
        Apidoc\Title("获取主机域名"),
        Apidoc\Author("lwj 2023.8.4 新增"),
        Apidoc\Method("GET"),
        Apidoc\Url("/admin/Common/get_host"),
    ]
    public function get_host(): Json
    {
        $res_data = ['host' => config('my.host')];
        return $this->res_success($res_data);
    }
}