<?php

namespace app\admin\controller;

use app\common\lib\VerifyData;
use app\common\logic\admin\coupon_activity\ActivityList;
use app\common\logic\admin\coupon_collection\CouponCollectionInfo;
use app\common\logic\admin\coupon_collection\CouponCollectionList;
use app\common\logic\admin\coupon_collection\entity\CouponCollectionInfoRequest;
use app\common\logic\admin\coupon_collection\entity\CouponCollectionListRequest;
use app\common\logic\admin\entity\ActivityListRequest;
use hg\apidoc\annotation as Apidoc;
use Respect\Validation\Validator as v;
use think\response\Json;

#[Apidoc\Title("用户优惠券")]
class CouponCollection extends BaseController
{

    #[
        Apidoc\Title("获取列表排序信息"),
        Apidoc\Author("cbj 2024.09.26 新增"),
        Apidoc\Method("GET"),
        Apidoc\Url("/admin/coupon_collection/sort_list_info"),
        Apidoc\Returned(name: "order_name", type: "int", require: true, desc: "默认排序字段"),
        Apidoc\Returned(name: "order_type", type: "int", require: true, desc: "默认排序方式"),
        Apidoc\Returned(name: "sort_field_options", type: "array", require: true, desc: "排序字段选项", children: [
            ['name' => 'value', 'type' => 'int', 'desc' => '排序字段值'],
            ['name' => 'label', 'type' => 'int', 'desc' => '排序字段名称'],
        ]),
        Apidoc\Returned(name: "sort_type_options", type: "array", require: true, desc: "排序类型选项", children: [
            ['name' => 'value', 'type' => 'int', 'desc' => '排序类型值'],
            ['name' => 'label', 'type' => 'int', 'desc' => '排序类型名称'],
        ]),
    ]
    public function sort_list_info(): Json
    {
        return $this->openExceptionCatch(function () {
            return [
                'order_name' => CouponCollectionList::SortFieldsOptions[0]['value'],
                'order_type' => CouponCollectionList::SortTypeDesc,
                'sort_field_options' => CouponCollectionList::SortFieldsOptions,
                'sort_type_options' => CouponCollectionList::SortTypeOptions
            ];
        });
    }

    #[
        Apidoc\Title("用户优惠券列表"),
        Apidoc\Author("cbj 2024.09.30 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/coupon_collection/list"),
        Apidoc\Param(name: "filter_user_phone", type: "int", require: false, default: null, desc: "过滤用户手机"),
        Apidoc\Param(name: "filter_activity_id", type: "int", require: false, default: null, desc: "过滤活动ID"),
        Apidoc\Param(name: "filter_use_status", type: "int", require: false, default: null, desc: "过滤使用状态 0:未使用 1:已使用"),
        Apidoc\Param(name: "page", type: "int", require: true, default: 1, desc: "页码"),
        Apidoc\Param(name: "limit", type: "int", require: true, default: 10, desc: "限制数量"),
        Apidoc\Param(name: "sort_field", type: "int", require: true, default: 1, desc: "排序类型"),
        Apidoc\Param(name: "sort_type", type: "int", require: true, default: 1, desc: "排序字段"),
        Apidoc\Returned(name: "total", type: "int", require: true, desc: "总数"),
        Apidoc\Returned(name: "per_page", type: "int", require: true, desc: "每页显示"),
        Apidoc\Returned(name: "current_page", type: "int", require: true, desc: "当前页数"),
        Apidoc\Returned(name: "last_page", type: "int", require: true, desc: "最后页数"),
        Apidoc\Returned(name: "data", type: "array", desc: "数据", children: [
            ['name' => 'id', 'type' => 'int', 'desc' => 'ID'],
            ['name' => 'phone', 'type' => 'int', 'desc' => '用户手机号'],
            ['name' => 'name', 'type' => 'string', 'desc' => '现金券名称'],
            ['name' => 'status', 'type' => 'int', 'desc' => '现金券状态；0-未使用；1-已使用；'],
            ['name' => 'par_value', 'type' => 'int', 'desc' => '面值，精确到小数点后1位'],
            ['name' => 'usage_amount', 'type' => 'int', 'desc' => '满减金额。精确到小数点后1位'],
            ['name' => 'create_time', 'type' => 'date', 'desc' => '领取时间'],
            ['name' => 'use_time', 'type' => 'date|null', 'desc' => '使用时间(当返回null时表示还未被使用)'],
            ['name' => 'expire_time', 'type' => 'date', 'desc' => '过期时间'],
            ['name' => 'activity_name', 'type' => 'string', 'desc' => '活动名称']
        ]),
    ]
    public function list(): Json
    {
        return $this->openExceptionCatch(function () {

            $data = $this->request->post();
            // 验证数据
            $verifyData = v::input($data, VerifyData::admin_coupon_collection([
                'filter_user_phone', 'filter_activity_id', 'filter_use_status',
                'page', 'limit', 'sort_field', 'sort_type'
            ]));

            $ActivityListRequest = new CouponCollectionListRequest($verifyData);
            return (new CouponCollectionList($this->loginUser, $ActivityListRequest))->get_list_data();
        });
    }

    #[
        Apidoc\Title("用户优惠券详情"),
        Apidoc\Author("cbj 2024.09.30 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/coupon_collection/info"),
        Apidoc\Param(name: "id", type: "int", require: true, default: 1, desc: "用户优惠券ID"),
        Apidoc\Returned(name: "id", type: "int", require: true, desc: "ID"),
        Apidoc\Returned(name: "activity_id", type: "int", require: true, desc: "活动ID"),
        Apidoc\Returned(name: "phone", type: "int", require: true, desc: "用户手机号"),
        Apidoc\Returned(name: "status", type: "int", require: true, desc: "优惠券使用状态 1:已使用 0:未使用"),
        Apidoc\Returned(name: "name", type: "string", require: true, desc: "优惠券名称"),
        Apidoc\Returned(name: "par_value", type: "int", require: true, desc: "面值，精确到小数点后1位"),
        Apidoc\Returned(name: "usage_amount", type: "int", require: true, desc: "满减金额。精确到小数点后1位"),
        Apidoc\Returned(name: "order_id", type: "string|null", require: true, desc: "充电订单ID(当为null时表示未被使用)"),
        Apidoc\Returned(name: "create_time", type: "string", require: true, desc: "领取的时间"),
        Apidoc\Returned(name: "use_time", type: "string|null", require: true, desc: "使用的时间(当为null时表示未被使用)"),
        Apidoc\Returned(name: "expire_time", type: "string", require: true, desc: "过期时间"),
        Apidoc\Returned(name: "stations", type: "array", desc: "关联的场站", children: [
            ['name' => 'id', 'type' => 'int', 'desc' => '场站ID'],
            ['name' => 'name', 'type' => 'string', 'desc' => '场站名称'],
        ]),
    ]
    public function info(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();
            // 验证数据
            $verifyData = v::input($data, VerifyData::admin_coupon_collection([
                'id',
            ]));

            $ActivityListRequest = new CouponCollectionInfoRequest($verifyData);
            return (new CouponCollectionInfo($this->loginUser, $ActivityListRequest))->run();
        });
    }
}