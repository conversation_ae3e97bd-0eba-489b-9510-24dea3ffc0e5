<?php

namespace app\admin\controller;

use app\common\lib\VerifyData;
use app\common\logic\admin\coupon_activity\ActivityInfo;
use app\common\logic\admin\coupon_activity\ActivityList;
use app\common\logic\admin\coupon_activity\ActivityOptions;
use app\common\logic\admin\coupon_activity\AddPattern;
use app\common\logic\admin\coupon_activity\CouponOplogList;
use app\common\logic\admin\coupon_activity\CreateActivity;
use app\common\logic\admin\coupon_activity\DeleteActivity;
use app\common\logic\admin\coupon_activity\DeletePattern;
use app\common\logic\admin\coupon_activity\PatternInfo;
use app\common\logic\admin\coupon_activity\PatternList;
use app\common\logic\admin\coupon_activity\ReviewAdopt;
use app\common\logic\admin\coupon_activity\ReviewFailed;
use app\common\logic\admin\coupon_activity\StopActivity;
use app\common\logic\admin\coupon_activity\StopActivityReviewAdopt;
use app\common\logic\admin\coupon_activity\StopActivityReviewFailed;
use app\common\logic\admin\coupon_activity\SubmitReview;
use app\common\logic\admin\coupon_activity\UpdateActivity;
use app\common\logic\admin\coupon_activity\UpdatePattern;
use app\common\logic\admin\entity\ActivityInfoRequest;
use app\common\logic\admin\entity\ActivityListRequest;
use app\common\logic\admin\entity\AddPatternRequest;
use app\common\logic\admin\entity\CreateActivityRequest;
use app\common\logic\admin\entity\DeleteActivityRequest;
use app\common\logic\admin\entity\DeletePatternRequest;
use app\common\logic\admin\entity\PatternInfoRequest;
use app\common\logic\admin\entity\PatternListRequest;
use app\common\logic\admin\entity\ReviewAdoptRequest;
use app\common\logic\admin\entity\ReviewFailedRequest;
use app\common\logic\admin\entity\StopActivityRequest;
use app\common\logic\admin\entity\StopActivityReviewAdoptRequest;
use app\common\logic\admin\entity\StopActivityReviewFailedRequest;
use app\common\logic\admin\entity\SubmitReviewRequest;
use app\common\logic\admin\entity\UpdateActivityRequest;
use app\common\logic\admin\entity\UpdatePatternRequest;
use hg\apidoc\annotation as Apidoc;
use Respect\Validation\Validator as v;
use think\response\Json;

#[Apidoc\Title("优惠券活动")]
class CouponActivity extends BaseController
{

    #[
        Apidoc\Title("获取活动列表排序信息"),
        Apidoc\Author("cbj 2024.09.26 新增"),
        Apidoc\Method("GET"),
        Apidoc\Url("/admin/coupon_activity/sort_list_info"),
        Apidoc\Returned(name: "order_name", type: "int", require: true, desc: "默认排序字段"),
        Apidoc\Returned(name: "order_type", type: "int", require: true, desc: "默认排序方式"),
        Apidoc\Returned(name: "sort_field_options", type: "array", require: true, desc: "排序字段选项", children: [
            ['name' => 'value', 'type' => 'int', 'desc' => '排序字段值'],
            ['name' => 'label', 'type' => 'int', 'desc' => '排序字段名称'],
        ]),
        Apidoc\Returned(name: "sort_type_options", type: "array", require: true, desc: "排序类型选项", children: [
            ['name' => 'value', 'type' => 'int', 'desc' => '排序类型值'],
            ['name' => 'label', 'type' => 'int', 'desc' => '排序类型名称'],
        ]),
    ]
    public function sort_list_info(): Json
    {
        return $this->openExceptionCatch(function () {
            return [
                'order_name' => ActivityList::SortFieldsOptions[0]['value'],
                'order_type' => ActivityList::SortTypeDesc,
                'sort_field_options' => ActivityList::SortFieldsOptions,
                'sort_type_options' => ActivityList::SortTypeOptions
            ];
        });
    }

    #[
        Apidoc\Title("活动列表"),
        Apidoc\Author("cbj 2024.09.24 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/coupon_activity/list"),
        Apidoc\Param(name: "filter_id", type: "int", require: false, desc: "过滤活动ID"),
        Apidoc\Param(name: "filter_name", type: "string", require: false, desc: "过滤活动名称"),
        Apidoc\Param(name: "filter_status", type: "int", require: false, default: null, desc: "过滤活动状态"),
        Apidoc\Param(name: "page", type: "int", require: true, default: 1, desc: "页码"),
        Apidoc\Param(name: "limit", type: "int", require: true, default: 10, desc: "限制数量"),
        Apidoc\Param(name: "sort_field", type: "int", require: true, default: 1, desc: "排序类型"),
        Apidoc\Param(name: "sort_type", type: "int", require: true, default: 1, desc: "排序字段"),
        Apidoc\Returned(name: "total", type: "int", require: true, desc: "总数"),
        Apidoc\Returned(name: "per_page", type: "int", require: true, desc: "每页显示"),
        Apidoc\Returned(name: "current_page", type: "int", require: true, desc: "当前页数"),
        Apidoc\Returned(name: "last_page", type: "int", require: true, desc: "最后页数"),
        Apidoc\Returned(name: "data", type: "array", desc: "数据", children: [
            ['name' => 'id', 'type' => 'int', 'desc' => 'ID'],
            ['name' => 'name', 'type' => 'string', 'desc' => '名称'],
            ['name' => 'status', 'type' => 'int', 'desc' => '活动状态：1-创建中；2-审核中；3-发放中；4-已结束；5-已停用；6-停用审核中'],
            ['name' => 'grant_max_count', 'type' => 'int', 'desc' => '活动中优惠券包发放上限'],
            ['name' => 'grant_num', 'type' => 'int', 'desc' => '活动券已领取份数'],
            ['name' => 'qcode_url', 'type' => 'string', 'desc' => '活动二维码URL(为空表示未生成)'],
            ['name' => 'start_time', 'type' => 'string', 'desc' => '活动开始时间'],
            ['name' => 'end_time', 'type' => 'string', 'desc' => '活动结束时间'],
            ['name' => 'create_time', 'type' => 'string', 'desc' => '创建时间'],
            ['name' => 'update_time', 'type' => 'string', 'desc' => '更新时间'],
        ]),
    ]
    public function list(): Json
    {
        return $this->openExceptionCatch(function () {

            $data = $this->request->post();
            // 验证数据
            $verifyData = v::input($data, VerifyData::coupon_activity([
                'filter_id', 'filter_name', 'filter_status',
                'page', 'limit',
                'sort_field', 'sort_type'
            ]));

            $ActivityListRequest = new ActivityListRequest($verifyData);
            return (new ActivityList($this->loginUser, $ActivityListRequest))->get_list_data();
        });
    }

    #[
        Apidoc\Title("活动详情"),
        Apidoc\Author("cbj 2024.09.26 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/coupon_activity/info"),
        Apidoc\Param(name: "activity_id", type: "int", require: true, desc: "活动ID"),
        Apidoc\Returned(name: "id", type: "int", require: true, desc: "活动ID"),
        Apidoc\Returned(name: "name", type: "string", require: true, desc: "活动名称"),
        Apidoc\Returned(name: "status", type: "int", require: true, desc: "活动状态 1-创建中；2-审核中；3-发放中；4-已结束；5-已停用；6-停用审核中"),
        Apidoc\Returned(name: "grant_max_count", type: "int", require: true, desc: "活动中优惠券包发放上限"),
        Apidoc\Returned(name: "grant_num", type: "int", require: true, desc: "活动券已领取份数"),
        Apidoc\Returned(name: "effective_days", type: "int", require: true, desc: "优惠券领取后的有效天数"),
        Apidoc\Returned(name: "qcode_url", type: "string", require: true, desc: "活动二维码URL"),
        Apidoc\Returned(name: "start_time", type: "string", require: true, desc: "活动开始时间"),
        Apidoc\Returned(name: "end_time", type: "string", require: true, desc: "活动结束时间"),
        Apidoc\Returned(name: "create_time", type: "string", require: true, desc: "创建时间"),
        Apidoc\Returned(name: "update_time", type: "string|null", require: true, desc: "更新时间(为null时表示还没有更新过)"),
        Apidoc\Returned(name: "station_ids", type: "array", require: true, desc: "关联的活动ID集合"),
    ]
    public function info(): Json
    {
        return $this->openExceptionCatch(function () {

            $data = $this->request->post();
            // 验证数据
            $verifyData = v::input($data, VerifyData::coupon_activity([
                'activity_id',
            ]));

            $request = new ActivityInfoRequest($verifyData);
            return (new ActivityInfo($this->loginUser, $request))->run();
        });
    }

    #[
        Apidoc\Title("创建优惠券活动"),
        Apidoc\Desc('只有运营商主账号才能够创建(corp_id > 0 && pid === 0)'),
        Apidoc\Author("cbj 2024.09.24 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/coupon_activity/create_activity"),
        Apidoc\Param(name: "name", type: "string", require: true, desc: "活动名称，长度:1~128"),
        Apidoc\Param(name: "grant_max_count", type: "int", require: true, default: 1000, desc: "活动中优惠券包发放上限，取值范围:1~10000"),
        Apidoc\Param(name: "effective_days", type: "int", require: true, default: 30, desc: "领取后的有效天数，最小值：1"),
        Apidoc\Param(name: "start_time", type: "string", require: true, desc: "开始时间(格式:YYYY-mm-dd HH:ii:ss)，不能大于等于活动结束时间"),
        Apidoc\Param(name: "end_time", type: "string", require: true, desc: "结束时间(格式:YYYY-mm-dd HH:ii:ss)，不能小于等于活动开始时间"),
        Apidoc\Param(name: "station_ids", type: "array", require: true, desc: "关联的场站ID集合"),
        Apidoc\Returned(name: "new_id", type: "int", desc: "活动ID"),
    ]
    public function create_activity(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();
            // 验证数据
            $verifyData = v::input($data, VerifyData::coupon_activity([
                'name',
                'grant_max_count',
                'effective_days',
                'start_time',
                'end_time',
                'station_ids'
            ]));

            $request = new CreateActivityRequest($verifyData);
            $logic = new CreateActivity($this->loginUser, $request, $this->request);
            return $logic->run();
        }, true);
    }

    #[
        Apidoc\Title("更新优惠券活动"),
        Apidoc\Desc('
        1. 只有运营商主账号才能够更新(corp_id > 0 && pid === 0)
        2. 活动状态处于"创建中"时，才允许更新活动。
        '),
        Apidoc\Author("cbj 2024.09.25 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/coupon_activity/update_activity"),
        Apidoc\Param(name: "id", type: "int", require: true, desc: "活动ID，最小值:1"),
        Apidoc\Param(name: "name", type: "string", require: true, desc: "活动名称，长度:1~128"),
        Apidoc\Param(name: "grant_max_count", type: "int", require: true, default: 1000, desc: "活动中优惠券包发放上限，取值范围:1~10000"),
        Apidoc\Param(name: "effective_days", type: "int", require: true, default: 30, desc: "领取后的有效天数，最小值：1"),
        Apidoc\Param(name: "start_time", type: "string", require: true, desc: "开始时间(格式:YYYY-mm-dd HH:ii:ss)，不能大于等于活动结束时间"),
        Apidoc\Param(name: "end_time", type: "string", require: true, desc: "结束时间(格式:YYYY-mm-dd HH:ii:ss)，不能小于等于活动开始时间"),
        Apidoc\Param(name: "station_ids", type: "array", require: true, desc: "关联的场站ID集合"),
        Apidoc\Returned(name: "new_id", type: "int", desc: "活动ID"),
    ]
    public function update_activity(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();
            // 验证数据
            $verifyData = v::input($data, VerifyData::coupon_activity([
                'id',
                'name',
                'grant_max_count',
                'effective_days',
                'start_time',
                'end_time',
                'station_ids'
            ]));

            $request = new UpdateActivityRequest($verifyData);
            $logic = new UpdateActivity($this->loginUser, $request, $this->request);
            return $logic->run();

        }, true);
    }

    #[
        Apidoc\Title("删除优惠券活动"),
        Apidoc\Desc('1. 只有运营商主账号才能够更新(corp_id > 0 && pid === 0)；
                     2. 活动状态处于"创建中"或"已停用"时，才允许删除活动。'),
        Apidoc\Author("cbj 2024.09.25 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/coupon_activity/delete_activity"),
        Apidoc\Param(name: "activity_id", type: "int", require: true, desc: "活动ID，最小值:1"),
    ]
    public function delete_activity(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();
            // 验证数据
            $verifyData = v::input($data, VerifyData::coupon_activity([
                'activity_id',
            ]));

            $request = new DeleteActivityRequest($verifyData);
            $logic = new DeleteActivity($this->loginUser, $request, $this->request);
            return $logic->run();

        }, true);
    }

    #[
        Apidoc\Title("添加现金券"),
        Apidoc\Desc('
        1. 只有运营商主账号才能够更新(corp_id > 0 && pid === 0)；
        2. 活动状态处于"创建中"时，才允许添加现金券。
        '),
        Apidoc\Author("cbj 2024.09.25 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/coupon_activity/add_pattern"),
        Apidoc\Param(name: "activity_id", type: "int", require: true, desc: "活动ID，最小值:1"),
        Apidoc\Param(name: "pattern_name", type: "string", require: true, desc: "现金券名称，长度:1~128"),
        Apidoc\Param(name: "par_value", type: "int", require: true, default: 1000, desc: "面值，精确到小数点后1位，最小值:1"),
        Apidoc\Param(name: "usage_amount", type: "int", require: true, default: 30, desc: "满减金额。精确到小数点后1位，最小值:1"),
        Apidoc\Returned(name: "new_id", type: "int", desc: "活动ID"),
    ]
    public function add_pattern(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();
            // 验证数据
            $verifyData = v::input($data, VerifyData::coupon_activity([
                'activity_id',
                'pattern_name',
                'par_value',
                'usage_amount',
            ]));

            $request = new AddPatternRequest($verifyData);
            $logic = new AddPattern($this->loginUser, $request, $this->request);
            return $logic->run();
        });
    }

    #[
        Apidoc\Title("获取现金券列表排序信息"),
        Apidoc\Author("cbj 2024.09.26 新增"),
        Apidoc\Method("GET"),
        Apidoc\Url("/admin/coupon_activity/pattern_sort_list_info"),
        Apidoc\Returned(name: "order_name", type: "int", require: true, desc: "默认排序字段"),
        Apidoc\Returned(name: "order_type", type: "int", require: true, desc: "默认排序方式"),
        Apidoc\Returned(name: "sort_field_options", type: "array", require: true, desc: "排序字段选项", children: [
            ['name' => 'value', 'type' => 'int', 'desc' => '排序字段值'],
            ['name' => 'label', 'type' => 'int', 'desc' => '排序字段名称'],
        ]),
        Apidoc\Returned(name: "sort_type_options", type: "array", require: true, desc: "排序类型选项", children: [
            ['name' => 'value', 'type' => 'int', 'desc' => '排序类型值'],
            ['name' => 'label', 'type' => 'int', 'desc' => '排序类型名称'],
        ]),
    ]
    public function pattern_sort_list_info(): Json
    {
        return $this->openExceptionCatch(function () {
            return [
                'order_name' => PatternList::SortFieldsOptions[0]['value'],
                'order_type' => PatternList::SortTypeDesc,
                'sort_field_options' => PatternList::SortFieldsOptions,
                'sort_type_options' => PatternList::SortTypeOptions
            ];
        });
    }

    #[
        Apidoc\Title("现金券列表"),
        Apidoc\Author("cbj 2024.09.25 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/coupon_activity/pattern_list"),
        Apidoc\Param(name: "activity_id", type: "int", require: true, desc: "活动ID，最小值:1"),
        Apidoc\Param(name: "page", type: "int", require: true, default: 1, desc: "页码"),
        Apidoc\Param(name: "limit", type: "int", require: true, default: 10, desc: "限制数量"),
        Apidoc\Param(name: "sort_field", type: "int", require: true, default: 1, desc: "排序类型"),
        Apidoc\Param(name: "sort_type", type: "int", require: true, default: 1, desc: "排序字段"),
        Apidoc\Returned(name: "total", type: "int", require: true, desc: "总数"),
        Apidoc\Returned(name: "per_page", type: "int", require: true, desc: "每页显示"),
        Apidoc\Returned(name: "current_page", type: "int", require: true, desc: "当前页数"),
        Apidoc\Returned(name: "last_page", type: "int", require: true, desc: "最后页数"),
        Apidoc\Returned(name: "data", type: "array", desc: "数据", children: [
            ['name' => 'id', 'type' => 'int', 'desc' => 'ID'],
            ['name' => 'name', 'type' => 'string', 'desc' => '名称'],
            ['name' => 'par_value', 'type' => 'int', 'desc' => '面值，精确到小数点后1位'],
            ['name' => 'usage_amount', 'type' => 'string', 'desc' => '满减金额。精确到小数点后1位'],
            ['name' => 'create_time', 'type' => 'string', 'desc' => '创建时间'],
        ]),
    ]
    public function pattern_list(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();
            // 验证数据
            $verifyData = v::input($data, VerifyData::coupon_activity([
                'activity_id',
                'page', 'limit',
                'sort_field', 'sort_type',
            ]));

            $request = new PatternListRequest($verifyData);
            $logic = new PatternList($this->loginUser, $request);
            return $logic->get_list_data();
        });
    }

    #[
        Apidoc\Title("现金券详情"),
        Apidoc\Author("cbj 2024.09.25 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/coupon_activity/pattern_info"),
        Apidoc\Param(name: "pattern_id", type: "int", require: true, desc: "活动ID，最小值:1"),
        Apidoc\Returned(name: "id", type: "int", require: true, desc: "现金券ID"),
        Apidoc\Returned(name: "name", type: "string", require: true, desc: "现金券名称"),
        Apidoc\Returned(name: "par_value", type: "int", require: true, desc: "面值，精确到小数点后1位"),
        Apidoc\Returned(name: "usage_amount", type: "int", require: true, desc: "满减金额。精确到小数点后1位"),
        Apidoc\Returned(name: "create_time", type: "string", require: true, desc: "创建时间"),
    ]
    public function pattern_info(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();
            // 验证数据
            $verifyData = v::input($data, VerifyData::coupon_activity([
                'pattern_id',
            ]));

            $request = new PatternInfoRequest($verifyData);
            $logic = new PatternInfo($this->loginUser, $request);
            return $logic->run();
        });
    }

    #[
        Apidoc\Title("更新现金券"),
        Apidoc\Desc('
        1. 只有运营商主账号才能够更新(corp_id > 0 && pid === 0)；
        2. 活动状态处于"创建中"时，才允许更新现金券。
        '),
        Apidoc\Author("cbj 2024.09.26 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/coupon_activity/update_pattern"),
        Apidoc\Param(name: "pattern_id", type: "int", require: true, desc: "现金券ID，最小值:1"),
        Apidoc\Param(name: "pattern_name", type: "string", require: true, desc: "现金券名称，长度:1~128"),
        Apidoc\Param(name: "par_value", type: "int", require: true, default: 1000, desc: "面值，精确到小数点后1位，最小值:1"),
        Apidoc\Param(name: "usage_amount", type: "int", require: true, default: 30, desc: "满减金额。精确到小数点后1位，最小值:1"),
        Apidoc\Returned(name: "new_id", type: "int", desc: "活动ID"),
    ]
    public function update_pattern(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();
            // 验证数据
            $verifyData = v::input($data, VerifyData::coupon_activity([
                'pattern_id',
                'pattern_name',
                'par_value',
                'usage_amount',
            ]));

            $request = new UpdatePatternRequest($verifyData);
            $logic = new UpdatePattern($this->loginUser, $request, $this->request);
            return $logic->run();
        });
    }

    #[
        Apidoc\Title("删除现金券"),
        Apidoc\Desc('
        1. 只有运营商主账号才能够更新(corp_id > 0 && pid === 0)；
        2. 活动状态处于"创建中"时，才允许更新现金券。
        '),
        Apidoc\Author("cbj 2024.09.27 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/coupon_activity/delete_pattern"),
        Apidoc\Param(name: "pattern_id", type: "int", require: true, desc: "活动ID，最小值:1"),
    ]
    public function delete_pattern(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();
            // 验证数据
            $verifyData = v::input($data, VerifyData::coupon_activity([
                'pattern_id',
            ]));

            $request = new DeletePatternRequest($verifyData);
            $logic = new DeletePattern($this->loginUser, $request, $this->request);
            return $logic->run();
        });
    }

    #[
        Apidoc\Title("提交发放审核"),
        Apidoc\Desc('
        1. 只有运营商主账号才能够提交审核(corp_id > 0 && pid === 0)；
        2. 活动状态处于"创建中"时，才允许提交发放审核。
        '),
        Apidoc\Author("cbj 2024.09.25 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/coupon_activity/submit_review"),
        Apidoc\Param(name: "id", type: "int", require: true, desc: "活动ID，最小值：1"),
    ]
    public function submit_review(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();
            // 验证数据
            $verifyData = v::input($data, VerifyData::coupon_activity([
                'id',
            ]));

            $request = new SubmitReviewRequest($verifyData);
            return (new SubmitReview($this->loginUser, $request, $this->request))->run();
        });
    }

    #[
        Apidoc\Title("活动发放审核通过"),
        Apidoc\Desc('
        1. 只有超级管理员才能够审核(corp_id === 0 && pid === 0)；
        2. 活动状态处于"审核中"时，才允许审核通过。
        '),
        Apidoc\Author("cbj 2024.09.25 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/coupon_activity/grant_review_adopt"),
        Apidoc\Param(name: "id", type: "int", require: true, desc: "活动ID，最小值：1"),
    ]
    public function grant_review_adopt(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();
            // 验证数据
            $verifyData = v::input($data, VerifyData::coupon_activity([
                'id',
            ]));

            $request = new ReviewAdoptRequest($verifyData);
            return (new ReviewAdopt($this->loginUser, $request, $this->request))->run();
        });
    }


    #[
        Apidoc\Title("活动发放审核不通过"),
        Apidoc\Desc('
        1. 只有超级管理员才能够审核(corp_id === 0 && pid === 0)；
        2. 活动状态处于"审核中"时，才允许审核不通过。
        '),
        Apidoc\Author("cbj 2024.09.25 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/coupon_activity/grant_review_failed"),
        Apidoc\Param(name: "id", type: "int", require: true, desc: "活动ID，最小值：1"),
    ]
    public function grant_review_failed(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();
            // 验证数据
            $verifyData = v::input($data, VerifyData::coupon_activity([
                'id',
            ]));

            $request = new ReviewFailedRequest($verifyData);
            return (new ReviewFailed($this->loginUser, $request, $this->request))->run();
        });
    }

    #[
        Apidoc\Title("停用活动"),
        Apidoc\Desc('
        1. 只有运营商主账号才能够停用活动(corp_id > 0 && pid === 0)；
        2. 活动状态处于"发放中"时，才允许停用活动。
        '),
        Apidoc\Author("cbj 2024.09.28 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/coupon_activity/stop_activity"),
        Apidoc\Param(name: "id", type: "int", require: true, desc: "活动ID，最小值：1"),
    ]
    public function stop_activity(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();
            // 验证数据
            $verifyData = v::input($data, VerifyData::coupon_activity([
                'id',
            ]));

            $request = new StopActivityRequest($verifyData);
            return (new StopActivity($this->loginUser, $request, $this->request))->run();
        });
    }

    #[
        Apidoc\Title("停用活动审核通过"),
        Apidoc\Desc('
        1. 只有超级管理员才能够审核(corp_id === 0 && pid === 0)；
        2. 活动状态处于"停用审核中"时，才允许审核通过。
        '),
        Apidoc\Author("cbj 2024.09.28 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/coupon_activity/stop_activity_review_adopt"),
        Apidoc\Param(name: "id", type: "int", require: true, desc: "活动ID，最小值：1"),
    ]
    public function stop_activity_review_adopt(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();
            // 验证数据
            $verifyData = v::input($data, VerifyData::coupon_activity([
                'id',
            ]));

            $request = new StopActivityReviewAdoptRequest($verifyData);
            return (new StopActivityReviewAdopt($this->loginUser, $request, $this->request))->run();
        });
    }


    #[
        Apidoc\Title("停用活动审核不通过"),
        Apidoc\Desc('
        1. 只有超级管理员才能够审核(corp_id === 0 && pid === 0)；
        2. 活动状态处于"停用审核中"时，才允许审核不通过。
        '),
        Apidoc\Author("cbj 2024.09.28 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/coupon_activity/stop_activity_review_failed"),
        Apidoc\Param(name: "id", type: "int", require: true, desc: "活动ID，最小值：1"),
    ]
    public function stop_activity_review_failed(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();
            // 验证数据
            $verifyData = v::input($data, VerifyData::coupon_activity([
                'id',
            ]));

            $request = new StopActivityReviewFailedRequest($verifyData);
            return (new StopActivityReviewFailed($this->loginUser, $request, $this->request))->run();
        });
    }

    #[
        Apidoc\Title("活动选项"),
        Apidoc\Author("cbj 2024.10.08 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/coupon_activity/activity_options"),
        Apidoc\Returned(name: "options", type: "array", require: true, desc: "选项", children: [
            ['name' => 'id', 'type' => 'int', 'desc' => '活动ID'],
            ['name' => 'name', 'type' => 'string', 'desc' => '活动名称']
        ]),
    ]
    public function activity_options(): Json
    {
        return $this->openExceptionCatch(function () {

            return (new ActivityOptions($this->loginUser, $this->request))->run();

        });
    }

    #[
        Apidoc\Title("获取操作日志列表排序信息"),
        Apidoc\Author("cbj 2024.10.08 新增"),
        Apidoc\Method("GET"),
        Apidoc\Url("/admin/coupon_activity/oplog_sort_list_info"),
        Apidoc\Returned(name: "order_name", type: "int", require: true, desc: "默认排序字段"),
        Apidoc\Returned(name: "order_type", type: "int", require: true, desc: "默认排序方式"),
        Apidoc\Returned(name: "sort_field_options", type: "array", require: true, desc: "排序字段选项", children: [
            ['name' => 'value', 'type' => 'int', 'desc' => '排序字段值'],
            ['name' => 'label', 'type' => 'int', 'desc' => '排序字段名称'],
        ]),
        Apidoc\Returned(name: "sort_type_options", type: "array", require: true, desc: "排序类型选项", children: [
            ['name' => 'value', 'type' => 'int', 'desc' => '排序类型值'],
            ['name' => 'label', 'type' => 'int', 'desc' => '排序类型名称'],
        ]),
    ]
    public function oplog_sort_list_info(): Json
    {
        return $this->openExceptionCatch(function () {
            return [
                'order_name' => CouponOplogList::SortFieldsOptions[0]['value'],
                'order_type' => CouponOplogList::SortTypeDesc,
                'sort_field_options' => CouponOplogList::SortFieldsOptions,
                'sort_type_options' => CouponOplogList::SortTypeOptions
            ];
        });
    }

    #[
        Apidoc\Title("操作日志列表"),
        Apidoc\Author("cbj 2024.10.08 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/coupon_activity/oplog_list"),
        Apidoc\Param(name: "filter_user_type", type: "int", require: false, default: null, desc: "过滤操作人员类型 0-后台用户；1-小程序用户"),
        Apidoc\Param(name: "filter_user_id", type: "int", require: false, default: null, desc: "过滤操作人员ID"),
        Apidoc\Param(name: "filter_op_interface", type: "int", require: false, default: null, desc: "操作接口地址(模糊查询)"),
        Apidoc\Param(name: "page", type: "int", require: true, default: 1, desc: "页码"),
        Apidoc\Param(name: "limit", type: "int", require: true, default: 10, desc: "限制数量"),
        Apidoc\Param(name: "sort_field", type: "int", require: true, default: 1, desc: "排序类型"),
        Apidoc\Param(name: "sort_type", type: "int", require: true, default: 1, desc: "排序字段"),
        Apidoc\Returned(name: "total", type: "int", require: true, desc: "总数"),
        Apidoc\Returned(name: "per_page", type: "int", require: true, desc: "每页显示"),
        Apidoc\Returned(name: "current_page", type: "int", require: true, desc: "当前页数"),
        Apidoc\Returned(name: "last_page", type: "int", require: true, desc: "最后页数"),
        Apidoc\Returned(name: "data", type: "array", require: true, desc: "数据", children: [
            ['name' => 'id', 'type' => 'int', 'require' => true, 'desc' => '日志ID'],
            ['name' => 'user_type', 'type' => 'int', 'require' => true, 'desc' => '操作人员类型 0:后台用户 1:小程序用户'],
            ['name' => 'user_name', 'type' => 'string', 'require' => false, 'desc' => '用户名(当操作人员类型为后台用户时，才会有这个字段。)'],
            ['name' => 'user_phone', 'type' => 'string', 'require' => false, 'desc' => '用户手机号(当操作人员类型为小程序用户时，才会有这个字段。)'],
            ['name' => 'op_interface', 'type' => 'string', 'require' => true, 'desc' => '操作的接口地址'],
            ['name' => 'op_content', 'type' => 'string', 'require' => true, 'desc' => '操作的接口参数'],
            ['name' => 'op_time', 'type' => 'string', 'require' => true, 'desc' => '操作时间'],
        ]),
    ]
    public function oplog_list(): Json
    {
        return $this->openExceptionCatch(function () {
            return (new CouponOplogList($this->loginUser, $this->request))->get_list_data();
        });
    }

}