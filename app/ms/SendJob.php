<?php
declare(strict_types=1);

namespace app\ms;

use app\common\queue\BaseConsumer;
use think\facade\Log;

/**
 * API 同步任务 RabbitMQ 消费者
 *
 * 用于处理来自 api_sync 交换机的 API 同步请求
 * 支持时间戳去重和失败重试机制
 */
class SendJob extends BaseConsumer
{
    private const EXCHANGE_NAME = 'api_sync';
    private const EXCHANGE_TYPE = 'x-delayed-message';
    private const QUEUE_NAME = 'api_sync';
    private const ROUTING_KEY = 'api.sync';

    /**
     * 构造函数
     */
    public function __construct()
    {
        // 调用父类构造函数，设置交换机和队列信息
        parent::__construct(
            self::EXCHANGE_NAME,    // 交换机名称
            self::QUEUE_NAME,       // 队列名称
            self::EXCHANGE_TYPE,    // 交换机类型
            self::ROUTING_KEY       // 路由键
        );

        // 启用延时重试功能
        // 延时时间分别为0s/15s/15s/30s/3m/10m/20m/30m/30m/30m/60m/3h/3h/3h/6h/6h
        $this->enableDelayRetry([0, 15, 15, 30, 180, 600, 1200, 1800, 1800, 1800, 3600, 10800, 10800, 10800, 21600, 21600]);
    }

    /**
     * 处理消息的业务逻辑（实现基类的抽象方法）
     *
     * @param string $messageBody 消息内容
     * @return string 处理结果常量
     */
    protected function processMessage(string $messageBody): string
    {
        // 解析消息内容
        $data = json_decode($messageBody, true);
        if (!$data) {
            Log::error('API 同步消息格式错误: ' . $messageBody);
            return self::RESULT_SKIP; // 跳过格式错误的消息
        }

        // 从传入数据中获取请求信息
        $url = $data['url'] ?? '';
        $method = $data['method'] ?? 'POST';
        $params = $data['data'] ?? [];
        $key = $data['key'] ?? '';
        $headers = $data['headers'] ?? [];
        $timestamp = $data['timestamp'] ?? 0;

        // 检查缓存中的最新时间戳
        $latestTimestamp = cache("sync_api:{$key}");

        // 如果缓存中的时间戳不存在或比当前任务的时间戳早，跳过该任务
        if ($latestTimestamp === null || $timestamp < $latestTimestamp) {
            Log::warning("cache:{$key} is null or timestamp < latestTimestamp");
            // 打印被拒绝的请求信息
            Log::warning("url:{$url},method:{$method},params:" . json_encode_cn($params) . "timestamp:{$timestamp},latestTimestamp:{$latestTimestamp}");
            return self::RESULT_SKIP; // 跳过过期的消息
        }

        // 执行请求逻辑
        try {
            $response = Api::executeRequest($url, $method, $params, $headers);
            // 记录成功日志
            Log::info('API 同步请求成功: ' . json_encode($response));
            return self::RESULT_SUCCESS;
        } catch (\Exception $e) {
            // 请求失败，记录失败信息
            Log::error('API 同步请求失败: ' . $e->getMessage());
            return self::RESULT_DELAY_RETRY; // 失败消息进行延时重试
        }
    }
}