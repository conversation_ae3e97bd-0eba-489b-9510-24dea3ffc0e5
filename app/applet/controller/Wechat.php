<?php
/** @noinspection PhpDynamicAsStaticMethodCallInspection */

/** @noinspection PhpUnused */

namespace app\applet\controller;

use app\common\lib\ExceptionLogCollector;
use app\common\lib\wechat\answer\AnswerScheduler;
use app\common\lib\wechat\BlockchainElectronicInvoiceClient;
use app\common\lib\wechat\entity\request\CardTemplateInfo;
use app\common\model\ElectronicInvoiceConfig;
use think\Container;
use think\facade\Db;
use think\response\Json;
use hg\apidoc\annotation as Apidoc;
use RuntimeException;
use Throwable;

#[Apidoc\Title("微信接口")]
class Wechat extends BaseController
{
    #[
        Apidoc\Title("更新电子发票相关配置"),
        Apidoc\Author("cbj 2023.10.30 新增"),
        Apidoc\Desc("先更新数据库中的数据，再调用这个接口来将配置更新到微信那边。"),
        Apid<PERSON>\Method("POST"),
        Apidoc\Url("/applet/wechat/update_config"),
        Apidoc\Param(name: "token", type: "string", require: true, desc: "内部API令牌(防止被外人调用)"),
        Apidoc\Param(name: "type", type: "string", require: true, desc: "类型 card_template_info:卡券模板信息 开发配置:development_config"),
    ]
    public function update_config(): Json
    {
        $token = $this->request->post('token');
        if ($token !== config('wechat.internal_api_token')) {
            return Container::getInstance()->invokeClass(Json::class, [[], 404]);
        }

        $type = $this->request->post('type');
        try {
            Db::startTrans();
            // 查询配置数据
            $ElectronicInvoiceConfig = app(ElectronicInvoiceConfig::class);
            $config = $ElectronicInvoiceConfig->getAllConfig();

            switch ($type) {
                case ElectronicInvoiceConfig::TypeCardTemplateInfo:
                    $CardTemplateInfo = new CardTemplateInfo(
                        $config[ElectronicInvoiceConfig::TypeCardTemplateInfo][ElectronicInvoiceConfig::KeyLogoUrl]
                    );
                    $BlockchainElectronicInvoiceClient = new BlockchainElectronicInvoiceClient();
                    $response = $BlockchainElectronicInvoiceClient->CreateCardTemplate($CardTemplateInfo);
                    if ($response->isFailed === true || $response->httpCode !== 200) {
                        ExceptionLogCollector::recordErrorLog(json_encode($response));
                        throw new RuntimeException('更新失败');
                    }

                    $body = $response->analysisJsonBody();

                    $ElectronicInvoiceConfig->updateCardTemplateCardAppid($body['card_appid']);
                    $ElectronicInvoiceConfig->updateCardTemplateCardId($body['card_id']);
                    $ElectronicInvoiceConfig->effectiveConfig(ElectronicInvoiceConfig::TypeCardTemplateInfo);
                    break;
                case ElectronicInvoiceConfig::TypeDevelopmentConfig:

                    $callbackUrl = $config[ElectronicInvoiceConfig::TypeDevelopmentConfig][ElectronicInvoiceConfig::KeyCallbackUrl];
                    $BlockchainElectronicInvoiceClient = new BlockchainElectronicInvoiceClient();
                    $sdcResponse = $BlockchainElectronicInvoiceClient->setDevelopmentConfig($callbackUrl);
                    if ($sdcResponse->isFailed === true || $sdcResponse->httpCode !== 200) {
                        ExceptionLogCollector::recordErrorLog(json_encode($sdcResponse));
                        throw new RuntimeException('更新失败');
                    }

                    $ElectronicInvoiceConfig->effectiveConfig(ElectronicInvoiceConfig::TypeDevelopmentConfig);
                    break;
                default:
                    throw new RuntimeException('无效类型');
            }

            Db::commit();
        } catch (Throwable $e) {
            Db::rollback();
            ExceptionLogCollector::recordErrorLog($e);
            return $this->res_error([], '更新失败');
        }

        return $this->res_success([], '更新成功');
    }

    public function electronic_invoice_callback(): Json
    {
        trace('header => ' . json_encode($this->request->header()), 'wechat_callback');
        trace('body => ' . json_encode($this->request->all()), 'wechat_callback');

        return (new AnswerScheduler())->handler($this->request);
    }
}