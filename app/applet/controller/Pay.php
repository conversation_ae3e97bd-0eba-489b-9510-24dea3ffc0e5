<?php
/** @noinspection PhpDynamicAsStaticMethodCallInspection */
declare (strict_types=1);
namespace app\applet\controller;

use app\common\lib\exception\RuntimeException;
use app\common\lib\ExceptionLogCollector;
use app\common\lib\recharge\request\WechatPayNoticeRequest;
use app\common\lib\recharge\WechatPayNotice;
use app\common\lib\SendApplet;
use app\common\lib\WeChatPay;
use app\common\model\PayOrder as PayOrderModel;
use app\common\traits\Curd;
use app\ms\Api;
use Exception;
use think\Container;
use think\facade\Db;
use think\facade\Log;
use think\response\Json;
use app\common\model\RefundOrder as RefundOrderModel;
use Throwable;


class Pay extends BaseController
{
    use Curd;

    public function initialize(): void
    {
        $this->modelClass = new PayOrderModel();
    }

    /**
     * 创建微信支付分订单接口
     */
    public function wechat_score(){
        // TODO: 调用订单微服务创建订单
        try {
            $res = Api::send("/order/pay/wechat/score");
            return $this->res_success($res['data'], $res['msg'], $res['code']);
        }catch (Throwable $e) {
            Log::error($e->getMessage());
            return $this->res_error([], '服务器异常,稍后再试');
        }
    }

    /**
     * 微信支付回调接口
     * lwj 2023.8.15 新增
     */
    public function weixin_callback(): Json
    {
        $body = file_get_contents('php://input');
        trace('微信支付回调接口=》返回body数据：' . $body, '信息');
        $params = $this->request->param();
        trace('微信支付回调接口=》返回参数：' . json_encode_cn($params), '信息');
        $header = $this->request->header();
        trace('微信支付回调接口=》返回header参数：' . json_encode_cn($header), '信息');

        $sn = $header["wechatpay-serial"];
        $wechatpay_signature = base64_decode($header['wechatpay-signature']);

        $WeChatPay = new WeChatPay;

        $public_key = $WeChatPay->get_cer($sn);
        if (!$public_key) return json(['code' => 'FAIL', 'message' => '获取平台证书错误']);

        $message = $header['wechatpay-timestamp'] . "\n" .
            $header['wechatpay-nonce'] . "\n" .
            $body . "\n";
        $verify = openssl_verify($message, $wechatpay_signature, $public_key, 'SHA256');
        trace('微信支付回调接口，回调检验结果：' . json_encode_cn($verify), '信息');
        if (!$verify) return json(['code' => 'FAIL', 'message' => '回调检验不通过']);

        $res_data_arr = $WeChatPay->callback_decrypt($params['resource']['associated_data'], $params['resource']['nonce'], $params['resource']['ciphertext']);
        if (!$res_data_arr) return json(['code' => 'error', 'message' => '解码错误']);

        // 前面的代码后续在整理
        // 业务逻辑
        try {
            $request = new WechatPayNoticeRequest($res_data_arr);
            (new WechatPayNotice($request))->handler();
            return json(['code' => 'SUCCESS', 'message' => '成功']);
        } catch (RuntimeException $e) {
            ExceptionLogCollector::collect($e);
            return match ($e->getCode()) {
                RuntimeException::CodeBusinessException => json(['code' => 'FAIL', 'message' => $e->getMessage()]),
                default => json(['code' => 'FAIL', 'message' => '服务器异常']),
            };
        } catch (Throwable $e) {
            ExceptionLogCollector::collect($e);
            return json(['code' => 'FAIL', 'message' => '服务器异常']);
        }
    }

    /**
     * 微信退款回调接口
     * lwj 2024.2.19 新增
     * lwj 2024.2.20 修改
     */
    public function weixin_refund_callback(): Json
    {
        try {
            $body = file_get_contents('php://input');
            trace('微信退款回调接口=》返回body数据：' . $body, '信息');
            $params = $this->request->param();
            trace('微信退款回调接口=》返回参数：' . json_encode_cn($params), '信息');
            $header = $this->request->header();
            trace('微信退款回调接口=》返回header参数：' . json_encode_cn($header), '信息');

            $sn = $header["wechatpay-serial"];
            $wechatpay_signature = base64_decode($header['wechatpay-signature']);

            $WeChatPay = new WeChatPay;

            $public_key = $WeChatPay->get_cer($sn);
            if (!$public_key) throw new Exception('获取平台证书错误');
    //        if (!$public_key) return json(['code' => 'FAIL', 'message' => '获取平台证书错误']);

            $message = $header['wechatpay-timestamp'] . "\n" .
                $header['wechatpay-nonce'] . "\n" .
                $body . "\n";
            $verify = openssl_verify($message, $wechatpay_signature, $public_key, 'SHA256');
            trace('微信退款回调接口，回调检验结果：' . json_encode_cn($verify), '信息');
            if (!$verify) throw new Exception('回调检验不通过');
    //        if (!$verify) return json(['code' => 'FAIL', 'message' => '回调检验不通过']);

            $res_data_arr = $WeChatPay->callback_decrypt($params['resource']['associated_data'], $params['resource']['nonce'], $params['resource']['ciphertext']);
            if (!$res_data_arr) throw new Exception('解码错误');
    //        if (!$res_data_arr) return json(['code' => 'error', 'message' => '解码错误']);

            $res=Db::name('refund_callback')
                ->json(['log'])
                ->insert([
                    "refund_id" => $res_data_arr["out_refund_no"],
                    "out_refund_id" => $res_data_arr["refund_id"],
                    "order_id" => $res_data_arr["out_trade_no"],
                    "trade_no" => $res_data_arr["transaction_id"],
                    "type" => "微信支付-退款回调-已校验",
                    "log" => $res_data_arr,
                    "create_time" => date("Y-m-d H:i:s")
            ]);
            trace('微信退款回调接口，写入回调结果：' . json_encode_cn($res), '信息');

            $RefundOrder = new RefundOrderModel;

            $refund_id = $res_data_arr["out_refund_no"];
            $refund_order = $RefundOrder->where("id", $refund_id)->find();
            if (!$refund_order) throw new Exception('订单不存在');
//            if (!$refund_order) return json(['code' => 'FAIL', 'message' => '订单不存在']);
            if ($refund_order['state'] == 20) return json(['code' => 'SUCCESS', 'message' => '成功']);

            if ($res_data_arr['amount']['refund'] !== $refund_order['refund_price']) {
                $RefundOrder
                    ->where('id', $refund_id)
                    ->limit(1)
                    ->update([
                        'state' => 4,
                        'msg' => '金额校验错误'
                    ]);
                trace('微信退款回调接口=》金额校验错误返回：' . json_encode_cn($res_data_arr), '错误');
                SendApplet::send_uid($refund_order['user_id'],[
                    'type' => 'refund_notice',
                    'result' => 'fail',
                    'msg' => '退款失败，金额校验错误'
                ]);
                throw new Exception('金额校验错误');
//                return json(['code'=>'SUCCESS','message'=>'成功']);
            }

            if ($res_data_arr['refund_status'] == 'SUCCESS') {
                $refund_order2 = $RefundOrder->where('id', $refund_id)->find();
                if ($refund_order2 && $refund_order2['state'] == 20) {
                    trace('微信退款回调接口=》状态为20，直接返回：', '信息');
                    return json(['code' => 'SUCCESS', 'message' => '成功']);
                }

                $RefundOrder
                    ->where('id', $refund_id)
                    ->limit(1)
                    ->update([
                        'state' => 20,
                        'msg' => '退款成功',
                        'payer_refund' => $res_data_arr['amount']['payer_refund'],
                        'out_notice_status' => $res_data_arr['refund_status'],
                        'success_time' => date('Y-m-d H:i:s', strtotime($res_data_arr['success_time'])),
                    ]);
                $this->modelClass
                    ->where('id', $refund_order['order_id'])
                    ->limit(1)
                    ->update([
                        'refund_state' => 3,
                    ]);
                SendApplet::send_uid($refund_order['user_id'],[
                    'type' => 'refund_notice',
                    'result' => 'success',
                    'msg' => '退款成功',
                    'refund_status' => $res_data_arr['refund_status'],
                ]);
                return json(['code' => 'SUCCESS', 'message' => '成功']);
            }

            $RefundOrder
                ->where('id', $refund_id)
                ->limit(1)
                ->update([
                    'state' => 30,
                    'msg' => '退款失败',
                    'payer_refund' => $res_data_arr['amount']['payer_refund'],
                    'out_notice_status' => $res_data_arr['refund_status'],
                ]);

            $this->modelClass
                ->where('id', $refund_order['order_id'])
                ->limit(1)
                ->update([
                    'refund_state' => 4,
                ]);

            SendApplet::send_uid($refund_order['user_id'],[
                'type' => 'refund_notice',
                'result' => 'fail',
                'msg' => '退款失败，未知错误',
                'refund_status' => $res_data_arr['refund_status'],
            ]);
            trace('微信支付回调接口=》支付失败返回：' . json_encode_cn($res_data_arr), '信息');
            return json(['code' => 'SUCCESS', 'message' => '成功']);
        } catch (Throwable $e) {
            ExceptionLogCollector::collect($e);
            return Container::getInstance()->invokeClass(Json::class, ['服务器异常', 500]);
//            return json(['code' => 'FAIL', 'message' => '服务器异常']);
        }
    }


//    /**
//     * 支付测试
//     * lwj 2024.1.17 新增
//     */
//    public function pay_test(): Json
//    {
//        try {
//            $data = $this->request->post();
//            $user_id=$data['user_id'];
//            $price=$data['price'];
//            $increaseBalanceResult = app(Users::class)->increaseBalance($user_id, $price);
//            SendApplet::send_uid($user_id, [
//                'type' => 'pay_notice',
//                'result' => 'success',
//                'msg' => '支付成功，加余额成功'
//            ]);
//            event('UserBalanceChange', new UserBalanceChangeEvent([
//                'type' => UserBalanceChangeEvent::TypeBalanceRecharge,
//                'user_id' => $user_id,
//                'trigger_time' => time(),
//            ]));
//            return $this->res_success();
//        } catch (Throwable $e) {
//            return $this->res_error([], '处理异常：' . $e->getMessage());
//        }
//    }

}