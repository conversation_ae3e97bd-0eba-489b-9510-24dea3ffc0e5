<?php
/** @noinspection PhpUnused */

/** @noinspection PhpDynamicAsStaticMethodCallInspection */

namespace app\applet\controller;

use app\common\lib\VerifyData;
use app\common\logic\applet\coupon_collection\CouponCollectionList;
use app\common\logic\applet\entity\CouponCollectionListRequest;
use Respect\Validation\Validator as v;
use think\response\Json;
use hg\apidoc\annotation as Apidoc;

#[Apidoc\Title("用户优惠券")]
class CouponCollection extends BaseController
{
    #[
        Apidoc\Title("优惠券列表"),
        Apidoc\Author("cbj 2024.09.28 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/applet/coupon_collection/list"),
        Apidoc\Param(name: "filter_type", type: "int", require: false, default: 1, desc: "过滤类型 0:未使用的 1:已使用的 2:已过期的"),
        A<PERSON><PERSON>\Param(name: "page", type: "int", require: true, default: 1, desc: "页码"),
        Apidoc\Param(name: "limit", type: "int", require: true, default: 10, desc: "显示条数"),
        Apidoc\Returned(name: "total", type: "int", require: true, desc: "总数"),
        Apidoc\Returned(name: "per_page", type: "int", require: true, desc: "每页显示"),
        Apidoc\Returned(name: "current_page", type: "int", require: true, desc: "当前页数"),
        Apidoc\Returned(name: "last_page", type: "int", require: true, desc: "最后页数"),
        Apidoc\Returned(name: "data", type: "array", desc: "数据", children: [
            ['name' => 'id', 'type' => 'int', 'desc' => 'ID'],
            ['name' => 'name', 'type' => 'string', 'desc' => '现金券名称'],
            ['name' => 'status', 'type' => 'int', 'desc' => '现金券状态；0-未使用；1-已使用；'],
            ['name' => 'expire_time', 'type' => 'string', 'desc' => '过期时间'],
            ['name' => 'par_value', 'type' => 'int', 'desc' => '面值，精确到小数点后1位'],
            ['name' => 'usage_amount', 'type' => 'int', 'desc' => '满减金额。精确到小数点后1位'],
            ['name' => 'stations', 'type' => 'array', 'desc' => '关联的场站', 'children' => [
                ['name' => 'id', 'type' => 'int', 'desc' => '活动ID'],
                ['name' => 'name', 'type' => 'int', 'desc' => '活动名称'],
            ]],
        ]),
    ]
    public function list(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();
            // 验证数据
            $verifyData = v::input($data, VerifyData::coupon_collection([
                'filter_type', 'page', 'limit'
            ]));

            $request = new CouponCollectionListRequest($verifyData);
            $logic = new CouponCollectionList($this->loginUser, $request);

            return $logic->run();
        });
    }
}