<?php
/** @noinspection PhpUnused */

/** @noinspection PhpDynamicAsStaticMethodCallInspection */

namespace app\applet\controller;

use app\common\lib\exception\RuntimeException;
use app\common\lib\VerifyData;
use app\common\logic\applet\coupon_activity\ReceiveCoupon;
use app\common\logic\applet\entity\ReceiveCouponRequest;
use app\common\model\CouponActivityStation;
use app\common\model\CouponPattern;
use Respect\Validation\Validator as v;
use think\response\Json;
use hg\apidoc\annotation as Apidoc;
use app\common\model\CouponActivity as CouponActivityModel;

#[Apidoc\Title("优惠券活动")]
class CouponActivity extends BaseController
{
    #[
        Apidoc\Title("活动详情"),
        Apidoc\Author("cbj 2024.09.28 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/applet/coupon_activity/info"),
        Apid<PERSON>\Param(name: "activity_id", type: "int", require: true, default: 1, desc: "优惠券活动ID"),
        Apidoc\Returned(name: "is_receive", type: "int", require: true, desc: "用户是否已领取 1:已领取 0:未领取"),
        Apidoc\Returned(name: "activity_data", type: "array", require: true, desc: "活动数据", children: [
            ['name' => 'id', 'type' => 'int', 'require' => true, 'desc' => '活动ID'],
            ['name' => 'name', 'type' => 'string', 'require' => true, 'desc' => '活动名称'],
            ['name' => 'status', 'type' => 'int', 'require' => true, 'desc' => '活动状态 1-创建中；2-审核中；3-发放中；4-已结束；5-已停用；6-停用审核中'],
            ['name' => 'is_del', 'type' => 'int', 'require' => true, 'desc' => '是否已删除 1:已删除 0:未删除'],
            ['name' => 'grant_max_count', 'type' => 'int', 'require' => true, 'desc' => '活动中优惠券包发放上限'],
            ['name' => 'grant_num', 'type' => 'int', 'require' => true, 'desc' => '活动券已领取份数'],
            ['name' => 'effective_days', 'type' => 'int', 'require' => true, 'desc' => '优惠券领取后的有效天数'],
            ['name' => 'start_time', 'type' => 'string', 'require' => true, 'desc' => '活动开始时间'],
            ['name' => 'end_time', 'type' => 'string', 'require' => true, 'desc' => '活动结束时间'],
        ]),
        Apidoc\Returned(name: "station_data", type: "array", require: true, desc: "关联的场站数据", children: [
            ['name' => 'id', 'type' => 'int', 'desc' => '场站ID'],
            ['name' => 'name', 'type' => 'string', 'desc' => '场站名称'],
        ]),
        Apidoc\Returned(name: "coupon_patterns", type: "array", require: true, desc: "关联的现金券数据", children: [
            ['name' => 'id', 'type' => 'int', 'desc' => 'ID'],
            ['name' => 'name', 'type' => 'string', 'desc' => '名称'],
            ['name' => 'par_value', 'type' => 'int', 'desc' => '面值，精确到小数点后1位'],
            ['name' => 'usage_amount', 'type' => 'string', 'desc' => '满减金额。精确到小数点后1位'],
            ['name' => 'create_time', 'type' => 'string', 'desc' => '创建时间'],
        ]),
    ]
    public function info(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();
            // 验证数据
            $verifyData = v::input($data, VerifyData::coupon_activity([
                'activity_id',
            ]));

            // 查询活动数据
            $fields = [
                'id', 'name', 'status', 'is_del', 'grant_max_count',
                'grant_num', 'effective_days',
                'start_time', 'end_time',
            ];
            $activityData = app(CouponActivityModel::class)->getActivityInfo(['id' => $verifyData['activity_id']], $fields);

            if (empty($activityData)) {
                throw new RuntimeException('无效活动ID');
            }

            // 查询关联的场站名称
            $relationStationData = app(CouponActivityStation::class)->getActivityRelationStationData($verifyData['activity_id'], ['s.id', 's.name']);

            // 查询关联的现金券数据
            $couponPatterns = app(CouponPattern::class)->getActivityAllPattern($verifyData['activity_id']);

            // 查询当前用户是否已领取
            $is_receive = app(\app\common\model\CouponCollection::class)->isReceive(
                $this->loginUser->id, $verifyData['activity_id']
            ) ? 1 : 0;

            return [
                'is_receive' => $is_receive,
                'activity_data' => $activityData,
                'station_data' => $relationStationData,
                'coupon_patterns' => $couponPatterns
            ];
        }, false, 'lock:coupon_activity:info:' . $this->loginUser->id);
    }

    #[
        Apidoc\Title("领取优惠券"),
        Apidoc\Author("cbj 2024.09.28 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/applet/coupon_activity/receive_coupon"),
        Apidoc\Param(name: "activity_id", type: "int", require: true, default: 1, desc: "优惠券活动ID"),
    ]
    public function receive_coupon(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();
            // 验证数据
            $verifyData = v::input($data, VerifyData::coupon_activity([
                'activity_id',
            ]));

            $response = $this->openExceptionCatch(function () use ($verifyData) {

                $request = new ReceiveCouponRequest($verifyData);

                return (new ReceiveCoupon($this->loginUser, $request, $this->request))->run();

            }, true, sprintf('lock:coupon_activity:receive:%s', $verifyData['activity_id']), 10);


            $responseBody = $response->getData();
            if ($responseBody['code'] === 200) {
                return $responseBody['data'];
            } else {
                return $this->res_error([], $responseBody['msg'], is_open_exception_catch: true);
            }
        });
    }
}