<?php
/** @noinspection PhpUnused */
declare (strict_types=1);

namespace app\applet\controller;

use app\common\lib\charging\applet\request\ScanCodeGetInfoRequest;
use app\common\lib\charging\applet\request\StartChargeRequest;
use app\common\lib\charging\applet\request\StopChargeRequest;
use app\common\lib\charging\applet\ReservationCharge;
use app\common\lib\charging\applet\ScanChargingQrcodeGetInfo;
use app\common\lib\charging\applet\ScanCodeGetInfo;
use app\common\lib\charging\applet\StartCharge;
use app\common\lib\charging\applet\StopCharge;
use app\common\lib\VerifyData;
use app\common\model\Order as OrderModel;
use hg\apidoc\annotation as Apidoc;
use Respect\Validation\Exceptions\ValidationException;
use Respect\Validation\Validator as v;
use think\response\Json;
use Throwable;


#[Apidoc\Title("充电")]
class Charging extends BaseController
{

    #[
        Apidoc\Title("扫码获取信息"),
        Apidoc\Author("lwj 2023.8.18 新增，lwj 2023.9.19 修改"),
        Apidoc\Method("POST"),
        Apidoc\Url("/applet/Charging/scan_code_get_info"),
        Apidoc\Param(name: 'id', type: "int", require: true, desc: '充电枪编号'),
        Apidoc\Returned(name: "shots", type: "array", desc: "数据", children: [
            ['name' => 'id', 'type' => 'int', 'desc' => '充电枪编号，16位数字'],
            ['name' => 'name', 'type' => 'string', 'desc' => '充电枪名称'],
            ['name' => 'corp_name', 'type' => 'string', 'desc' => '运营商名称'],
            ['name' => 'station_name', 'type' => 'string', 'desc' => '充电站名称'],
            ['name' => 'piles_name', 'type' => 'string', 'desc' => '充电桩名称'],
            ['name' => 'sequence', 'type' => 'int', 'desc' => '枪序号'],
            ['name' => 'is_online', 'type' => 'int', 'desc' => '枪在线状态：1-离线，2-在线'],
            ['name' => 'work_status', 'type' => 'int', 'desc' => '枪工作状态：1-空闲，2-充电中'],
            ['name' => 'link_status', 'type' => 'int', 'desc' => '枪连接状态：1-无连接，2-连接中'],
            ['name' => 'is_support_reservation', 'type' => 'int', 'desc' => '是否支持预约充电 0:不支持 1:支持']
        ]),
        Apidoc\Returned(name: "user", type: "array", desc: "数据", children: [
            ['name' => 'id', 'type' => 'int', 'desc' => '用户id'],
            ['name' => 'balance', 'type' => 'int', 'desc' => '可用余额，单位分'],
            ['name' => 'freeze_balance', 'type' => 'int', 'desc' => '冻结余额，单位分'],
        ]),
        Apidoc\Returned(name: 'discount', type: 'int', desc: '[新增]优惠折扣(精确到小数点后1位)')
    ]
    public function scan_code_get_info(): Json
    {
        return $this->openExceptionCatch(function () {

            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::shots([
                'id',
            ]));

            return (new ScanCodeGetInfo(new ScanCodeGetInfoRequest([
                'id' => $verify_data['id'],
                'loginUser' => $this->loginUser
            ])))->run();
        });
    }

    #[
        Apidoc\Title("扫充电二维码获取枪信息"),
        Apidoc\Author("lwj 2023.8.18 新增，lwj 2023.9.19 修改"),
        Apidoc\Method("POST"),
        Apidoc\Url("/applet/Charging/scan_charging_qrcode_get_info"),
        Apidoc\Param(name: 'charging_qrcode_id', type: "int", require: true, desc: '充电二维码ID'),
        Apidoc\Returned(name: "shots", type: "array", desc: "数据", children: [
            ['name' => 'id', 'type' => 'int', 'desc' => '充电枪编号，16位数字'],
            ['name' => 'name', 'type' => 'string', 'desc' => '充电枪名称'],
            ['name' => 'corp_name', 'type' => 'string', 'desc' => '运营商名称'],
            ['name' => 'station_name', 'type' => 'string', 'desc' => '充电站名称'],
            ['name' => 'piles_name', 'type' => 'string', 'desc' => '充电桩名称'],
            ['name' => 'sequence', 'type' => 'int', 'desc' => '枪序号'],
            ['name' => 'is_online', 'type' => 'int', 'desc' => '枪在线状态：1-离线，2-在线'],
            ['name' => 'work_status', 'type' => 'int', 'desc' => '枪工作状态：1-空闲，2-充电中'],
            ['name' => 'link_status', 'type' => 'int', 'desc' => '枪连接状态：1-无连接，2-连接中'],
            ['name' => 'is_support_reservation', 'type' => 'int', 'desc' => '是否支持预约充电 0:不支持 1:支持']
        ]),
        Apidoc\Returned(name: "user", type: "array", desc: "数据", children: [
            ['name' => 'id', 'type' => 'int', 'desc' => '用户id'],
            ['name' => 'balance', 'type' => 'int', 'desc' => '可用余额，单位分'],
            ['name' => 'freeze_balance', 'type' => 'int', 'desc' => '冻结余额，单位分'],
        ]),
        Apidoc\Returned(name: 'discount', type: 'int', desc: '[新增]优惠折扣(精确到小数点后1位)')
    ]
    public function scan_charging_qrcode_get_info(): Json
    {
        return $this->openExceptionCatch(function () {
            return (new ScanChargingQrcodeGetInfo($this->request, $this->loginUser))->run();
        });
    }

    #[
        Apidoc\Title("开始充电"),
        Apidoc\Author("lwj 2023.8.21 新增，lwj 2023.9.22 修改"),
        Apidoc\Method("POST"),
        Apidoc\Url("/applet/Charging/start_charging"),
        Apidoc\Param(name: 'shots_id', type: "int", require: true, desc: '充电枪编号'),
        Apidoc\Returned(name: "shots", type: "array", desc: "数据", children: [
            ['name' => 'id', 'type' => 'int', 'desc' => '充电枪编号，16位数字'],
            ['name' => 'name', 'type' => 'string', 'desc' => '充电枪名称'],
            ['name' => 'corp_name', 'type' => 'string', 'desc' => '运营商名称'],
            ['name' => 'station_name', 'type' => 'string', 'desc' => '充电站名称'],
            ['name' => 'piles_name', 'type' => 'string', 'desc' => '充电桩名称'],
            ['name' => 'sequence', 'type' => 'int', 'desc' => '枪序号'],
            ['name' => 'is_online', 'type' => 'int', 'desc' => '枪在线状态：1-离线，2-在线'],
            ['name' => 'work_status', 'type' => 'int', 'desc' => '枪工作状态：1-空闲，2-充电中'],
            ['name' => 'link_status', 'type' => 'int', 'desc' => '枪连接状态：1-无连接，2-连接中'],
        ]),
        Apidoc\Returned(name: "user", type: "array", desc: "数据", children: [
            ['name' => 'id', 'type' => 'int', 'desc' => '用户id'],
            ['name' => 'balance', 'type' => 'int', 'desc' => '可用余额，单位分'],
            ['name' => 'freeze_balance', 'type' => 'int', 'desc' => '冻结余额，单位分'],
        ]),
    ]
    public function start_charging(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::start_charging([
                'shots_id',
                'pay_mode'
            ]));
            // 默认为余额支付
            $verify_data['pay_mode'] = $verify_data['pay_mode'] ?? OrderModel::PayModeBalance;

            $verify_data = array_merge($verify_data, [
                'user_id' => $this->loginUser->id,
                'user_token' => $this->loginUser->token
            ]);

            return (new StartCharge(new StartChargeRequest($verify_data)))->run();
        });
    }

    #[
        Apidoc\Title("预约充电"),
        Apidoc\Author("cbj 2024-07-03 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/applet/Charging/reservation_charging"),
        Apidoc\Param(name: 'shots_id', type: "int", require: true, desc: '充电枪编号'),
        Apidoc\Param(name: 'reservation_time', type: "string", require: true, desc: '预约时间(格式:YYYY-mm-dd HH:ii:ss)'),
        Apidoc\Returned(name: "shots", type: "array", desc: "数据", children: [
            ['name' => 'id', 'type' => 'int', 'desc' => '充电枪编号，16位数字'],
            ['name' => 'name', 'type' => 'string', 'desc' => '充电枪名称'],
            ['name' => 'corp_name', 'type' => 'string', 'desc' => '运营商名称'],
            ['name' => 'station_name', 'type' => 'string', 'desc' => '充电站名称'],
            ['name' => 'piles_name', 'type' => 'string', 'desc' => '充电桩名称'],
            ['name' => 'sequence', 'type' => 'int', 'desc' => '枪序号'],
            ['name' => 'is_online', 'type' => 'int', 'desc' => '枪在线状态：1-离线，2-在线'],
            ['name' => 'work_status', 'type' => 'int', 'desc' => '枪工作状态：1-空闲，2-充电中'],
            ['name' => 'link_status', 'type' => 'int', 'desc' => '枪连接状态：1-无连接，2-连接中'],
        ]),
        Apidoc\Returned(name: "user", type: "array", desc: "数据", children: [
            ['name' => 'id', 'type' => 'int', 'desc' => '用户id'],
            ['name' => 'balance', 'type' => 'int', 'desc' => '可用余额，单位分'],
            ['name' => 'freeze_balance', 'type' => 'int', 'desc' => '冻结余额，单位分'],
        ]),
    ]
    public function reservation_charging(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::reservation_charging([
                'shots_id',
                'reservation_time'
            ]));

            $verify_data = array_merge($verify_data, [
                'user_id' => $this->loginUser->id,
                'user_token' => $this->loginUser->token
            ]);

            return (new ReservationCharge(new StartChargeRequest($verify_data)))->run();
        }, false, sprintf('applet_reservation_charging_%s', $this->loginUser->id));
    }

//    #[
//        Apidoc\Title("预约充电变更立即充电"),
//        Apidoc\Author("cbj 2024-07-17 新增"),
//        Apidoc\Method("POST"),
//        Apidoc\Url("/applet/Charging/to_charge_now"),
//        Apidoc\Param(name: 'id', type: "string", require: true, desc: '交易流水号'),
//    ]
//    public function to_charge_now(): Json
//    {
//        return $this->openExceptionCatch(function () {
//            $data = $this->request->post();
//            $verify_data = v::input($data, VerifyData::charging_order([
//                'id',
//            ]));
//
//            $orderData = OrderRepositories::getOrderData($verify_data['id'], ['status', 'piles_id', 'user_id']);
//            if (empty($orderData) || $orderData['user_id'] !== $this->loginUser->id) {
//                throw new RuntimeException('无效交易流水哈', [], RuntimeException::CodeBusinessException);
//            }
//
//            if ($orderData['status'] === OrderRepositories::StatusCharging) {
//                throw new RuntimeException('订单已开始充电', [], RuntimeException::CodeBusinessException);
//            } else if (in_array($orderData['status'], [
//                OrderRepositories::StatusComplete,
//                OrderRepositories::StatusAbnormal,
//                OrderRepositories::StatusCompulsorySettlement
//            ])) {
//                throw new RuntimeException('订单已结束', [], RuntimeException::CodeBusinessException);
//            }
//
//            $serialNumber = SerialNumber::increaseSequence();
//
//            $package = Kernel::create($serialNumber, BasePackage::TypeToChargeNow, [
//                'applet_user_id' => $this->loginUser->id,
//                'piles_id' => $orderData['piles_id'],
//                'transaction_serial_number' => $verify_data['id']
//            ]);
//            (new TransferServiceWriteQueue())->push($package->encode());
//
//            return [];
//        }, false, sprintf('applet_to_charge_now_%s', $this->loginUser->id));
//    }


    #[
        Apidoc\Title("停止充电"),
        Apidoc\Author("lwj 2023.8.22 新增，lwj 2023.9.19 修改"),
        Apidoc\Method("POST"),
        Apidoc\Url("/applet/Charging/stop_charging"),
        Apidoc\Param(name: 'id', type: "string", require: true, desc: '交易流水号'),
        Apidoc\Returned(name: "shots", type: "array", desc: "数据", children: [
            ['name' => 'id', 'type' => 'int', 'desc' => '充电枪编号，16位数字'],
            ['name' => 'name', 'type' => 'string', 'desc' => '充电枪名称'],
            ['name' => 'corp_name', 'type' => 'string', 'desc' => '运营商名称'],
            ['name' => 'station_name', 'type' => 'string', 'desc' => '充电站名称'],
            ['name' => 'piles_name', 'type' => 'string', 'desc' => '充电桩名称'],
            ['name' => 'sequence', 'type' => 'int', 'desc' => '枪序号'],
            ['name' => 'is_online', 'type' => 'int', 'desc' => '枪在线状态：1-离线，2-在线'],
            ['name' => 'work_status', 'type' => 'int', 'desc' => '枪工作状态：1-空闲，2-充电中'],
            ['name' => 'link_status', 'type' => 'int', 'desc' => '枪连接状态：1-无连接，2-连接中'],
        ]),
        Apidoc\Returned(name: "user", type: "array", desc: "数据", children: [
            ['name' => 'id', 'type' => 'int', 'desc' => '用户id'],
            ['name' => 'balance', 'type' => 'int', 'desc' => '可用余额，单位分'],
            ['name' => 'freeze_balance', 'type' => 'int', 'desc' => '冻结余额，单位分'],
        ]),
    ]
    public function stop_charging(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();
            $verify_data = v::input($data, VerifyData::charging_order([
                'id',
            ]));

            $params = array_merge(['order_id' => $verify_data['id']], ['loginUser' => $this->loginUser]);
            (new StopCharge(new StopChargeRequest($params)))->run();
        }, false, sprintf('applet_stop_charging_%s', $this->loginUser->id));
    }


    #[
        Apidoc\Title("获取充电订单信息"),
        Apidoc\Author("lwj 2023.9.4 新增"),
        Apidoc\Method("POST"),
        Apidoc\Url("/applet/Charging/get_charging_order_info"),
        Apidoc\Param(name: "id", type: "string", require: true, desc: "交易流水号"),

        Apidoc\Returned(name: "id", type: "string", require: true, desc: "交易流水号"),
        Apidoc\Returned(name: "type", type: "int", require: true, desc: "订单类型 1:立即充电 2:预约充电"),
        Apidoc\Returned(name: "stations_name", type: "string", require: true, desc: "场站名称"),
        Apidoc\Returned(name: "shot_id", type: "int", require: true, desc: "枪id"),
        Apidoc\Returned(name: "status", type: "int", require: true, desc: "订单状态 1:下单 3:充电中 5:完成 6:异常结束 7:自动结算 8:预约"),
        Apidoc\Returned(name: "reservation_time", type: "null|string", require: true, desc: "预约时间(订单类型为立即充电时，该自动为null)"),
        Apidoc\Returned(name: "realtime_monitoring_data", type: "array", require: true, desc: "实时数据", children: [
            ['name' => 'order_id', 'type' => 'string', "require" => false, 'desc' => '订单ID'],
            ['name' => 'transaction_serial_number', 'type' => 'string', "require" => false, 'desc' => '订单ID'],
            ['name' => 'piles_id', 'type' => 'int', "require" => false, 'desc' => '充电桩ID'],
            ['name' => 'shots_id', 'type' => 'int', "require" => false, 'desc' => '充电枪ID'],
            ['name' => 'status', 'type' => 'int', "require" => false, 'desc' => '状态 0:离线 1:故障 2:空闲 3:充电'],
            ['name' => 'is_rest', 'type' => 'int', "require" => false, 'desc' => '是否归位 0:否 1:是 2:未知'],
            ['name' => 'is_plugged_int', 'type' => 'int', "require" => false, 'desc' => '是否插枪 0:否 1:是'],
            ['name' => 'output_voltage', 'type' => 'int', "require" => false, 'desc' => '输出电压(精确到小数点后一位)'],
            ['name' => 'output_current', 'type' => 'int', "require" => false, 'desc' => '输出电流(精确到小数点后一位)'],
            ['name' => 'gun_wire_temperature', 'type' => 'int', "require" => false, 'desc' => '枪线温度(偏移量-50)'],
            ['name' => 'gun_wire_code', 'type' => 'string', "require" => false, 'desc' => '枪线编码'],
            ['name' => 'soc', 'type' => 'string', "require" => false, 'desc' => 'SOC'],
            ['name' => 'maximum_battery_temperature', 'type' => 'int', "require" => false, 'desc' => '电池组最高温度'],
            ['name' => 'cumulative_charging_time', 'type' => 'int', "require" => false, 'desc' => '累计充电时间(单位:分钟)'],
            ['name' => 'remaining_time', 'type' => 'int', "require" => false, 'desc' => '剩余时间(单位:分钟|交流桩置零)'],
            ['name' => 'charging_percentage', 'type' => 'int', "require" => false, 'desc' => '充电度数(精确到小数点后四位)'],
            ['name' => 'calculated_loss_charging_percentage', 'type' => 'int', "require" => false, 'desc' => '计损充电度数(精确到小数点后四位)'],
            ['name' => 'amount_charged', 'type' => 'int', "require" => false, 'desc' => '已充金额(精确到小数点后四位)'],
            ['name' => 'hardware_failure', 'type' => 'string', "require" => false, 'desc' => '硬件故障'],
        ]),
    ]
    public function get_charging_order_info(): Json
    {
        try {
            $data = $this->request->post();

            $verify_data = v::input($data, VerifyData::charging_order([
                'id',
            ]));

            $order_model = new OrderModel();

            $info = $order_model->alias('a')
                ->append(['realtime_monitoring_data'])
                ->join('stations b', 'a.station_id = b.id')
                ->field("a.id,a.type,a.status,b.name as stations_name,a.shot_id,a.reservation_time")
                ->withAttr('realtime_monitoring_data', function ($value, $data) {
                    return cache('upload_realtime_monitoring_data_order_id' . $data['id']) ?? [];
                })
                ->where('a.id', $verify_data['id'])
                ->find();
            // TODO:如果电流为0那么加上statusTips字段提示
            $info['statusTips'] = '';
            if (empty($info['realtime_monitoring_data']) || $info['realtime_monitoring_data']['output_current'] == 0) {
                $info['statusTips'] = '等待车辆同意充电中，请务必关闭车端的预约充电功能';
            }
            if (!$info) return $this->res_error([], '订单不存在');
            return $this->res_success($info);

        } catch (ValidationException $e) {
            return $this->res_error([], $e->getMessage());
        } catch (Throwable $e) {
            return $this->res_error([], '处理异常：' . $e->getMessage());
        }
    }


    /**
     * 获取订单编号
     * lwj 2023.8.21 新增
     * lwj 2023.9.13 修改
     * @param string|int $shots_id
     * @return string|bool|int
     */
    public function get_order_no(string|int $shots_id): string|bool|int
    {
        $order_model = new OrderModel();
        $max_attempts = 10;
        $attempt = 0;
        while ($attempt < $max_attempts) {
            $order_no = get_order_id_sn($shots_id);
            if (strlen($order_no) > 32) continue;
            try {
                $exist_order_no = $order_model->field('id')->find($order_no);
            } catch (Throwable) {
                return false;
            }
            if (!$exist_order_no) {
                break;
            }
            $attempt++;
        }
        if (empty($order_no)) return false;
        return $order_no;
    }

}