<?php
/** @noinspection PhpDynamicAsStaticMethodCallInspection */
declare (strict_types=1);

namespace app\command;

use app\common\log\SocketLogCollector;
use app\common\model\CouponActivity;
use think\console\Input;
use think\console\Output;

/**
 * 优惠券活动到期后自动结束
 * cbj 2024.10.09
 */
class CouponActivityExpireToEndStatus extends BaseCommand
{

    public const COMMAND = 'coupon-activity-expire-to-end-status';

    protected function configure(): void
    {
        // 指令配置
        $this->setName(self::COMMAND)
            ->setDescription('优惠券活动到期后自动结束');
    }


    /**
     * @param Input $input
     * @param Output $output
     * @param SocketLogCollector $socketLogCollector
     * @return void
     */
    protected function handler(Input $input, Output $output, SocketLogCollector $socketLogCollector): void
    {
        $where = [
            ['status', 'in', [CouponActivity::StatusGrant, CouponActivity::StatusReviewStop, CouponActivity::StatusReview]],
            ['end_time', '<=', date('Y-m-d H:i:s')]
        ];
        app(CouponActivity::class)->where($where)->update([
            'status' => CouponActivity::StatusEnd
        ]);
    }
}