[supervisord]
nodaemon=false
logfile=/var/www/html/runtime/supervisor/supervisord.log
pidfile=/var/www/html/runtime/supervisor/supervisord.pid

# 消费者:余额更新队列
[program:user-balance-change-queue-consumer]
command=php /var/www/html/think user-balance-change-queue-consumer start
autostart=true
autorestart=true
user=root
redirect_stderr=true
stdout_logfile=/var/www/html/runtime/supervisor/log/user-balance-change-queue-consumer.log

# 告警监控队列消费者
[program:alarm-monitoring-queue-consumer]
command=php /var/www/html/think alarm-monitoring-queue-consumer start
autostart=true
autorestart=true
user=root
redirect_stderr=true
stdout_logfile=/var/www/html/runtime/supervisor/log/alarm-monitoring-queue-consumer.log

# 企业微信消息队列消费者
[program:enterprise-wechat-message-queue-consumer]
command=php /var/www/html/think enterprise-wechat-message-queue-consumer start
autostart=true
autorestart=true
user=root
redirect_stderr=true
stdout_logfile=/var/www/html/runtime/supervisor/log/enterprise-wechat-message-queue-consumer.log

# 消息推送队列消费者
[program:message-push-queue-consumer]
command=php /var/www/html/think message-push-queue-consumer start
autostart=true
autorestart=true
user=root
redirect_stderr=true
stdout_logfile=/var/www/html/runtime/supervisor/log/message-push-queue-consumer.log

# 发送协议包到中转服务的消费者
[program:transfer-service-write-queue-consumer]
command=php /var/www/html/think transfer-service-write-queue-consumer start
autostart=true
autorestart=true
user=root
redirect_stderr=true
stdout_logfile=/var/www/html/runtime/supervisor/log/transfer-service-write-queue-consumer.log

# 读取到的中转服务协议包的消费者
[program:transfer-service-read-queue-consumer]
command=php /var/www/html/think transfer-service-read-queue-consumer start
autostart=true
autorestart=true
user=root
redirect_stderr=true
stdout_logfile=/var/www/html/runtime/supervisor/log/transfer-service-read-queue-consumer.log

# 中转服务客户端只读
[program:transfer-service-client-read]
command=php /var/www/html/think transfer-service-client-read start
autostart=true
autorestart=true
user=root
redirect_stderr=true
stdout_logfile=/var/www/html/runtime/supervisor/log/transfer-service-client-read.log

# 超时处理任务
[program:TimeoutHandleJob]
command=php /var/www/html/think TimeoutHandleJob
autostart=true
autorestart=true
user=root
redirect_stderr=true
stdout_logfile=/var/www/html/runtime/supervisor/log/TimeoutHandleJob.log

# 下发指令处理定时器
[program:SendHandleTimer]
command=php /var/www/html/think SendHandleTimer
autostart=true
autorestart=true
user=root
redirect_stderr=true
stdout_logfile=/var/www/html/runtime/supervisor/log/SendHandleTimer.log

# API 同步 RabbitMQ 消费者
[program:api-sync-consumer]
command=php /var/www/html/think api-sync
autostart=true
autorestart=true
user=root
redirect_stderr=true
stdout_logfile=/var/www/html/runtime/supervisor/log/api-sync-consumer.log

# WechatPayNotifyQueueConsumer | 订单支付通知队列消费者
[program:wechat-pay-notify-queue-consumer]
command=php /var/www/html/think OrderPay start
autostart=true
autorestart=true
user=root
redirect_stderr=true
stdout_logfile=/var/www/html/runtime/supervisor/log/wechat-pay-notify-queue-consumer.log

# Crontab | 查询状态: supervisorctl status crontab
[program:crontab]
command=crond -f
autostart=true
autorestart=true
user=root
redirect_stderr=true
stdout_logfile=/var/www/html/runtime/supervisor/log/crontab.log