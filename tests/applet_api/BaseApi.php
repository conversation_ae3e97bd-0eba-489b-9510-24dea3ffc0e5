<?php

namespace tests\applet_api;

use app\common\cache\redis\entity\AppletLoginUser;
use PHPUnit\Framework\TestCase;
use think\App;
use think\Request;

class BaseApi extends TestCase
{
    protected function init_post_request(array $params, AppletLoginUser $loginUser): App
    {
        // 初始化应用
        $app = new App();
        $app->initialize();

        // 初始化请求对象并设置请求参数
        $request = new Request();
        $request->setMethod('POST');
        $request->withPost($params);
        $request->appletLoginUser = $loginUser;
        $postParams = $request->post();
        $this->assertIsArray($postParams);
        $this->assertSame($params, $postParams);

        // 将请求对象绑定到应用中
        $app->bind(Request::class, $request);
        $this->assertInstanceOf(Request::class, $app->request);
        $this->assertIsArray($app->request->post());
        $this->assertSame($params, $app->request->post());

        return $app;
    }
}