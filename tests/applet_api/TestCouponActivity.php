<?php

namespace tests\applet_api;

use app\applet\controller\CouponActivity;
use app\common\cache\redis\entity\AdminLoginUser;
use app\common\cache\redis\entity\AppletLoginUser;
use app\common\lib\QrCode;
use app\common\model\AdminUsers;
use app\common\logic\admin\coupon_activity\ActivityList;
use app\common\model\CouponActivity as CouponActivityModel;
use app\common\model\Users;

class TestCouponActivity extends BaseApi
{
    public function test_info()
    {
        $loginUser = new AppletLoginUser((new Users())->getUserData(1000000000012));
        $app = $this->init_post_request([
            'activity_id' => 82
        ], $loginUser);

        $response = (new CouponActivity($app))->info();
        var_export($response);
    }

}