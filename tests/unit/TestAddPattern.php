<?php

namespace tests\unit;

use app\common\cache\redis\entity\AdminLoginUser;
use app\common\lib\exception\RuntimeException;
use app\common\lib\VerifyData;
use app\common\logic\admin\coupon_activity\AddPattern;
use app\common\logic\admin\entity\AddPatternRequest;
use app\common\model\CouponActivity;
use PHPUnit\Framework\TestCase;
use ReflectionClass;
use Respect\Validation\Validator as v;
use think\facade\Db;
use think\Request;
use Throwable;

class TestAddPattern extends TestCase
{
    public function testVerifyParamsActivityId()
    {
        // 参数要求：
        // activity_id: 数值类型 最小值: 1


        $params = [
            'activity_id' => '1',
        ];

        // 非字符串类型
        try {
            v::input($params, VerifyData::coupon_activity([
                'activity_id',
            ]));

            $this->fail('没有抛出验证异常');
        } catch (Throwable $e) {
            $this->assertSame('活动ID 必须是integer类型', $e->getMessage());
        }


        // 验证名称超出最大长度
        $params['activity_id'] = 0;
        try {
            v::input($params, VerifyData::coupon_activity([
                'activity_id',
            ]));
            $this->fail('没有抛出验证异常');
        } catch (Throwable $e) {
            $this->assertSame('活动ID 必须大于或等于 1', $e->getMessage());
        }

        $params['activity_id'] = 1123;
        try {
            $verifyData = v::input($params, VerifyData::coupon_activity([
                'activity_id',
            ]));
            $this->assertSame($params, $verifyData);
        } catch (Throwable $e) {
            $this->fail('预期不会抛出异常，实际抛出了。');
        }
    }

    public function testVerifyParamsPatternName()
    {
        // 参数要求：
        // pattern_name: 字符串类型 长度范围: 1~128


        $params = [
            'pattern_name' => 1,
        ];

        // 非字符串类型
        try {
            v::input($params, VerifyData::coupon_activity([
                'pattern_name',
            ]));

            $this->fail('预期会抛出异常，实际没有抛出验证异常');
        } catch (Throwable $e) {
            $this->assertSame('现金券名称 必须是string类型', $e->getMessage());
        }


        // 验证名称超出最大长度
        $params['pattern_name'] = str_repeat('测', 129);
        try {
            v::input($params, VerifyData::coupon_activity([
                'pattern_name',
            ]));
            $this->fail('预期会抛出异常，实际没有抛出验证异常');
        } catch (Throwable $e) {
            $this->assertSame('现金券名称 长度必须在 1 与 128 之间', $e->getMessage());
        }


        $params['pattern_name'] = '满10元减5元';
        try {
            $verifyData = v::input($params, VerifyData::coupon_activity([
                'pattern_name',
            ]));
            $this->assertSame($params, $verifyData);
        } catch (Throwable $e) {
            $this->fail('预期不会抛出异常，实际抛出了。');
        }
    }

    public function testVerifyParamsParValue()
    {
        // 参数要求：
        // par_value: 数值类型 最小值: 1


        $params = [
            'par_value' => '1',
        ];

        try {
            v::input($params, VerifyData::coupon_activity([
                'par_value',
            ]));

            $this->fail('没有抛出验证异常');
        } catch (Throwable $e) {
            $this->assertSame('面值 必须是integer类型', $e->getMessage());
        }


        // 验证名称超出最大长度
        $params['par_value'] = 0;
        try {
            v::input($params, VerifyData::coupon_activity([
                'par_value',
            ]));
            $this->fail('没有抛出验证异常');
        } catch (Throwable $e) {
            $this->assertSame('面值 必须大于或等于 1', $e->getMessage());
        }

        $params['par_value'] = 1123;
        try {
            $verifyData = v::input($params, VerifyData::coupon_activity([
                'par_value',
            ]));
            $this->assertSame($params, $verifyData);
        } catch (Throwable $e) {
            $this->fail('预期不会抛出异常，实际抛出了。');
        }
    }

    public function testVerifyParamsUsageAmount()
    {
        // 参数要求：
        // usage_amount: 数值类型 最小值: 1


        $params = [
            'usage_amount' => '1',
        ];

        try {
            v::input($params, VerifyData::coupon_activity([
                'usage_amount',
            ]));

            $this->fail('没有抛出验证异常');
        } catch (Throwable $e) {
            $this->assertSame('满减金额 必须是integer类型', $e->getMessage());
        }


        // 验证名称超出最大长度
        $params['usage_amount'] = 0;
        try {
            v::input($params, VerifyData::coupon_activity([
                'usage_amount',
            ]));
            $this->fail('没有抛出验证异常');
        } catch (Throwable $e) {
            $this->assertSame('满减金额 必须大于或等于 1', $e->getMessage());
        }

        $params['usage_amount'] = 1123;
        try {
            $verifyData = v::input($params, VerifyData::coupon_activity([
                'usage_amount',
            ]));
            $this->assertSame($params, $verifyData);
        } catch (Throwable $e) {
            $this->fail('预期不会抛出异常，实际抛出了。');
        }
    }

    public function testVerifyParamsAll()
    {
        $params = [
            'activity_id' => 1,
            'pattern_name' => '满10元减5元',
            'par_value' => 50,
            'usage_amount' => 100,
        ];

        try {
            $verifyData = v::input($params, VerifyData::coupon_activity([
                'activity_id',
                'pattern_name',
                'par_value',
                'usage_amount',
            ]));
            $this->assertSame($params, $verifyData);
        } catch (Throwable $e) {
            $this->fail('预期不会抛出异常，实际抛出了。');
        }
    }

    public function testAddPatternRequest()
    {
        $params = [
            'activity_id' => 1,
            'pattern_name' => '满10元减5元',
            'par_value' => 50,
            'usage_amount' => 100,
        ];
        $request = new AddPatternRequest($params);

        $this->assertSame($params['activity_id'], $request->activity_id);
        $this->assertSame($params['pattern_name'], $request->pattern_name);
        $this->assertSame($params['par_value'], $request->par_value);
        $this->assertSame($params['usage_amount'], $request->usage_amount);
    }

    public function testVerifyOperationPermission()
    {
        $request = new AddPatternRequest([]);
        $logic = new AddPattern(new AdminLoginUser([]), $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyOperationPermission');
        $method->setAccessible(true);

        // 不允许添加现金券的示例
        try {
            $method->invokeArgs($logic, [new AdminLoginUser(['pid' => 0, 'corp_id' => 0])]);
            $this->fail('预期会抛出异常，实际没有。');
        } catch (RuntimeException $e) {
            $this->assertSame(RuntimeException::CodeBusinessException, $e->getCode());
            $this->assertSame('只有运营商主账号才能够添加现金券', $e->getMessage());
        }

        try {
            $method->invokeArgs($logic, [new AdminLoginUser(['pid' => 2, 'corp_id' => 10001])]);
            $this->fail('预期会抛出异常，实际没有。');
        } catch (RuntimeException $e) {
            $this->assertSame(RuntimeException::CodeBusinessException, $e->getCode());
            $this->assertSame('只有运营商主账号才能够添加现金券', $e->getMessage());
        }

        // 允许添加现金券的示例
        try {
            $method->invokeArgs($logic, [new AdminLoginUser(['pid' => 0, 'corp_id' => 10001])]);
            $this->assertTrue(true, '预期不会抛出异常');
        } catch (RuntimeException $e) {
            $this->fail('预期不会抛出异常，实际抛出了。');
        }
    }

    public function testVerifyParamsLegitimacy()
    {
        $request = new AddPatternRequest([]);
        $logic = new AddPattern(new AdminLoginUser([]), $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyParamsLegitimacy');
        $method->setAccessible(true);


        // 参数不合法的例子1 - 根据活动ID在数据库里没有找到
        $couponActivityModel = $this->createStub(CouponActivity::class);
        $couponActivityModel->method('getActivity')->willReturn(null);
        bind(CouponActivity::class, $couponActivityModel);
        try {
            $method->invokeArgs($logic, [
                new AddPatternRequest(['activity_id' => 1]),
                new AdminLoginUser(['id' => 2])
            ]);
            $this->fail('预期会抛出异常，实际没有。');
        } catch (RuntimeException $e) {
            $this->assertSame(RuntimeException::CodeBusinessException, $e->getCode());
            $this->assertSame('无效活动ID', $e->getMessage());
        }

        // 参数不合法的例子2 - 对应的活动已经被删除
        $couponActivityModel = $this->createStub(CouponActivity::class);
        $couponActivityModel->method('getActivity')->willReturn([
            'is_del' => CouponActivity::IsDelYes
        ]);
        bind(CouponActivity::class, $couponActivityModel);
        try {
            $method->invokeArgs($logic, [
                new AddPatternRequest(['activity_id' => 1]),
                new AdminLoginUser(['id' => 2])
            ]);
            $this->fail('预期会抛出异常，实际没有。');
        } catch (RuntimeException $e) {
            $this->assertSame(RuntimeException::CodeBusinessException, $e->getCode());
            $this->assertSame('无效活动ID', $e->getMessage());
        }

        // 参数不合法的例子3 - 对应的活动是别人创建的
        $couponActivityModel = $this->createStub(CouponActivity::class);
        $couponActivityModel->method('getActivity')->willReturn([
            'is_del' => CouponActivity::IsDelNot,
            'user_id' => 3
        ]);
        bind(CouponActivity::class, $couponActivityModel);
        try {
            $method->invokeArgs($logic, [
                new AddPatternRequest(['activity_id' => 1]),
                new AdminLoginUser(['id' => 2])
            ]);
            $this->fail('预期会抛出异常，实际没有。');
        } catch (RuntimeException $e) {
            $this->assertSame(RuntimeException::CodeBusinessException, $e->getCode());
            $this->assertSame('无效活动ID', $e->getMessage());
        }

        // 参数合法的例子
        $couponActivityModel = $this->createStub(CouponActivity::class);
        $couponActivityModel->method('getActivity')->willReturn([
            'is_del' => CouponActivity::IsDelNot,
            'user_id' => 2
        ]);
        bind(CouponActivity::class, $couponActivityModel);
        try {
            $method->invokeArgs($logic, [
                new AddPatternRequest(['activity_id' => 1]),
                new AdminLoginUser(['id' => 2])
            ]);
            $this->assertTrue(true, '预期不会抛出异常');
        } catch (RuntimeException $e) {
            $this->fail('预期不会抛出异常，实际抛出异常了');
        }
    }

    public function testVerifyActivityIsCanOperation()
    {
        $request = new AddPatternRequest([]);
        $logic = new AddPattern(new AdminLoginUser([]), $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyActivityIsCanOperation');
        $method->setAccessible(true);

        // 验证不通过的例子，
        try {
            $method->invokeArgs($logic, [CouponActivity::StatusReview]);
            $this->fail('预期会抛出异常，实际没有。');
        } catch (RuntimeException $e) {
            $this->assertSame(RuntimeException::CodeBusinessException, $e->getCode());
            $this->assertSame('活动只有当状态处于"创建中"时才允许添加现金券!', $e->getMessage());
        }

        // 通过的例子
        try {
            $method->invokeArgs($logic, [CouponActivity::StatusCreating]);
            $this->assertTrue(true, '预期不会抛出异常');
        } catch (RuntimeException $e) {
            $this->fail('预期不会抛出异常，实际抛出了。');
        }
    }

    /**
     * @testdox 记录操作日志
     */
    public function testAddOperationLog()
    {
        $request = new AddPatternRequest([]);
        $logic = new AddPattern(new AdminLoginUser([]), $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('addOperationLog');
        $method->setAccessible(true);

        $params = [
            'corp_id' => 10001,
            'user_id' => 2,
            'op_interface' => '/admin/coupon_activity/add_pattern',
            'op_content' => [
                'activity_id' => 1,
                'pattern_name' => '现金券名称',
                'par_value' => 100,
                'usage_amount' => 50
            ]
        ];

        // 执行方法
        Db::startTrans();
        // int $corp_id, int $user_id, int $user_type, string $op_interface, string $op_content
        $method->invokeArgs($logic, [
            $params['corp_id'], $params['user_id'],
            $params['op_interface'], $params['op_content']
        ]);
        Db::rollback();
        $sql = Db::getLastSql();

        // 断言
        $expected_sql = sprintf(
            "INSERT INTO `coupon_oplog` SET `corp_id` = '%d' , `user_id` = '%d' , `user_type` = 0 , `op_interface` = '%s' , `op_content` = '%s' , `op_time` = '%s'",
            $params['corp_id'], $params['user_id'], $params['op_interface'], addslashes(json_encode($params['op_content'])), date('Y-m-d H:i:s')
        );

        $this->assertSame($expected_sql, $sql);
    }
}