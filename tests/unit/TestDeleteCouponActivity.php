<?php

namespace tests\unit;

use app\common\cache\redis\entity\AdminLoginUser;
use app\common\lib\exception\RuntimeException;
use app\common\lib\VerifyData;
use app\common\logic\admin\coupon_activity\ActivityList;
use app\common\logic\admin\coupon_activity\CreateActivity;
use app\common\logic\admin\coupon_activity\DeleteActivity;
use app\common\logic\admin\coupon_activity\UpdateActivity;
use app\common\logic\admin\entity\ActivityListRequest;
use app\common\logic\admin\entity\CreateActivityRequest;
use app\common\logic\admin\entity\DeleteActivityRequest;
use app\common\logic\admin\entity\UpdateActivityRequest;
use app\common\model\CouponActivity;
use app\common\model\Stations;
use PHPUnit\Framework\TestCase;
use ReflectionClass;
use ReflectionException;
use Respect\Validation\Validator as v;
use think\facade\Db;
use think\Request;
use Throwable;

class TestDeleteCouponActivity extends TestCase
{
    /**
     * @testdox 验证参数 - (activity_id)活动ID - 不传递时会验证不通过。
     */
    public function testVerifyParamsActivityIdScene1()
    {
        $params = [];

        try {
            v::input($params, VerifyData::coupon_activity([
                'activity_id',
            ]));
            $this->fail();
        } catch (Throwable $e) {
            $this->assertSame('活动ID 不能是可选的', $e->getMessage());
        }
    }

    /**
     * @testdox 验证参数 - (activity_id)活动ID - 传递非数值时验证不通过。
     */
    public function testVerifyParamsActivityIdScene2()
    {
        $params = [
            'activity_id' => '1'
        ];

        try {
            v::input($params, VerifyData::coupon_activity([
                'activity_id',
            ]));
            $this->fail();
        } catch (Throwable $e) {
            $this->assertSame('活动ID 必须是integer类型', $e->getMessage());
        }
    }

    /**
     * @testdox 验证参数 - (activity_id)活动ID - 传递低于1的数值时会验证不通过。
     */
    public function testVerifyParamsActivityIdScene3()
    {
        $params = [
            'activity_id' => -1
        ];

        try {
            v::input($params, VerifyData::coupon_activity([
                'activity_id',
            ]));
            $this->fail();
        } catch (Throwable $e) {
            $this->assertSame('活动ID 必须大于或等于 1', $e->getMessage());
        }
    }

    /**
     * @testdox 验证参数 - (activity_id)活动ID - 传递符合预期的数值，验证通过。
     */
    public function testVerifyParamsActivityIdScene4()
    {
        $params = [
            'activity_id' => 1
        ];

        try {
            $verify_params = v::input($params, VerifyData::coupon_activity([
                'activity_id',
            ]));

            $this->assertSame($params, $verify_params);
        } catch (Throwable $e) {
            $this->fail('不应该会抛出异常的');
        }
    }

    /**
     * @testdox 创建删除活动请求对象
     * @return void
     */
    public function testCreateDeleteActivityRequest()
    {
        $params = [
            'activity_id' => 1
        ];
        $request = new DeleteActivityRequest($params);
        $this->assertSame($params['activity_id'], $request->activity_id);
        $this->assertSame($params, $request->toArray());
    }

    /**
     * @testdox 验证操作权限 - 以超级管理员的身份操作，会验证不通过。
     */
    public function testVerifyOperationPermissionScene1()
    {
        $request = new DeleteActivityRequest([]);
        $logic = new DeleteActivity(new AdminLoginUser([]), $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyOperationPermission');
        $method->setAccessible(true);

        // 断言执行方法后会抛出异常
        $this->expectException(RuntimeException::class);
        $this->expectExceptionCode(RuntimeException::CodeBusinessException);
        $this->expectExceptionMessage("只允许运营商主账号操作");

        // 执行方法
        $method->invokeArgs($logic, [new AdminLoginUser(['pid' => 0, 'corp_id' => 0])]);
    }

    /**
     * @testdox 验证操作权限 - 以运营商主账号的身份操作，验证会通过。
     */
    public function testVerifyOperationPermissionScene2()
    {
        $request = new DeleteActivityRequest([]);
        $logic = new DeleteActivity(new AdminLoginUser([]), $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyOperationPermission');
        $method->setAccessible(true);

        // 执行方法
        $result = $method->invokeArgs($logic, [new AdminLoginUser(['pid' => 0, 'corp_id' => 10001])]);
        $this->assertNull($result);
    }

    /**
     * @testdox 验证操作权限 - 以运营商子账号的身份操作，验证会不通过。
     */
    public function testVerifyOperationPermissionScene3()
    {
        $request = new DeleteActivityRequest([]);
        $logic = new DeleteActivity(new AdminLoginUser([]), $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyOperationPermission');
        $method->setAccessible(true);

        // 断言执行方法后会抛出异常
        $this->expectException(RuntimeException::class);
        $this->expectExceptionCode(RuntimeException::CodeBusinessException);
        $this->expectExceptionMessage("只允许运营商主账号操作");

        // 执行方法
        $method->invokeArgs($logic, [new AdminLoginUser(['pid' => 2, 'corp_id' => 10001])]);
    }


    /**
     * @testdox 验证数据权限 - 当没有查询到活动数据时，会验证不通过。
     */
    public function testVerifyDataPermissionsScene1()
    {
        $request = new DeleteActivityRequest([]);
        $logic = new DeleteActivity(new AdminLoginUser([]), $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyDataPermissions');
        $method->setAccessible(true);

        // 断言执行方法后会抛出异常
        $this->expectException(RuntimeException::class);
        $this->expectExceptionCode(RuntimeException::CodeBusinessException);
        $this->expectExceptionMessage("无效的活动ID");

        // 执行方法
        $method->invokeArgs($logic, [new AdminLoginUser([]), null]);
    }

    /**
     * @testdox 验证数据权限 - 当查询到的活动数据已被删除时，会验证不通过。
     */
    public function testVerifyDataPermissionsScene2()
    {
        $request = new DeleteActivityRequest([]);
        $logic = new DeleteActivity(new AdminLoginUser([]), $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyDataPermissions');
        $method->setAccessible(true);

        // 断言执行方法后会抛出异常
        $this->expectException(RuntimeException::class);
        $this->expectExceptionCode(RuntimeException::CodeBusinessException);
        $this->expectExceptionMessage("无效的活动ID");

        // 执行方法
        $method->invokeArgs($logic, [new AdminLoginUser([]), ['is_del' => CouponActivity::IsDelYes]]);
    }

    /**
     * @testdox 验证数据权限 - 当活动的创建人与操作删除的人不是同一账号时，会验证不通过。
     */
    public function testVerifyDataPermissionsScene3()
    {
        $request = new DeleteActivityRequest([]);
        $logic = new DeleteActivity(new AdminLoginUser([]), $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyDataPermissions');
        $method->setAccessible(true);

        // 断言执行方法后会抛出异常
        $this->expectException(RuntimeException::class);
        $this->expectExceptionCode(RuntimeException::CodeBusinessException);
        $this->expectExceptionMessage("无效的活动ID");

        // 执行方法
        $method->invokeArgs($logic, [new AdminLoginUser(['id' => 5]), ['is_del' => CouponActivity::IsDelNot, 'user_id' => 2]]);
    }

    /**
     * @testdox 验证数据权限 - 当活动存在且未被删除，还有创建的人与操作删除的人是同一个时，验证才会通过。
     */
    public function testVerifyDataPermissionsScene4()
    {
        $request = new DeleteActivityRequest([]);
        $logic = new DeleteActivity(new AdminLoginUser([]), $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyDataPermissions');
        $method->setAccessible(true);

        // 执行方法
        $result = $method->invokeArgs($logic, [new AdminLoginUser(['id' => 2]), ['is_del' => CouponActivity::IsDelNot, 'user_id' => 2]]);

        $this->assertNull($result);
    }

    /**
     * @testdox 当活动状态处于"创建中"时，允许删除。
     * @return void
     * @throws ReflectionException
     */
    public function testVerifyIsCanOperationScene1()
    {
        $request = new DeleteActivityRequest([]);
        $logic = new DeleteActivity(new AdminLoginUser([]), $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyIsCanOperation');
        $method->setAccessible(true);

        // 执行方法
        $result = $method->invokeArgs($logic, [['status' => CouponActivity::StatusCreating]]);

        $this->assertNull($result);
    }

    /**
     * @testdox 当活动状态处于"审核中"时，不允许删除。
     * @return void
     * @throws ReflectionException
     */
    public function testVerifyIsCanOperationScene2()
    {
        $request = new DeleteActivityRequest([]);
        $logic = new DeleteActivity(new AdminLoginUser([]), $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyIsCanOperation');
        $method->setAccessible(true);

        $this->expectException(RuntimeException::class);
        $this->expectExceptionCode(RuntimeException::CodeBusinessException);
        $this->expectExceptionMessage('只有活动状态处于"创建中"或"已停用"时，才允许删除。');

        // 执行方法
        $method->invokeArgs($logic, [['status' => CouponActivity::StatusReview]]);
    }

    /**
     * @testdox 当活动状态处于"发放中"时，不允许删除。
     * @return void
     * @throws ReflectionException
     */
    public function testVerifyIsCanOperationScene3()
    {
        $request = new DeleteActivityRequest([]);
        $logic = new DeleteActivity(new AdminLoginUser([]), $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyIsCanOperation');
        $method->setAccessible(true);

        $this->expectException(RuntimeException::class);
        $this->expectExceptionCode(RuntimeException::CodeBusinessException);
        $this->expectExceptionMessage('只有活动状态处于"创建中"或"已停用"时，才允许删除。');

        // 执行方法
        $method->invokeArgs($logic, [['status' => CouponActivity::StatusGrant]]);
    }

    /**
     * @testdox 当活动状态处于"已结束"时，不允许删除。
     * @return void
     * @throws ReflectionException
     */
    public function testVerifyIsCanOperationScene4()
    {
        $request = new DeleteActivityRequest([]);
        $logic = new DeleteActivity(new AdminLoginUser([]), $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyIsCanOperation');
        $method->setAccessible(true);

        $this->expectException(RuntimeException::class);
        $this->expectExceptionCode(RuntimeException::CodeBusinessException);
        $this->expectExceptionMessage('只有活动状态处于"创建中"或"已停用"时，才允许删除。');

        // 执行方法
        $method->invokeArgs($logic, [['status' => CouponActivity::StatusEnd]]);
    }

    /**
     * @testdox 当活动状态处于"已停用"时，允许删除。
     * @return void
     * @throws ReflectionException
     */
    public function testVerifyIsCanOperationScene5()
    {
        $request = new DeleteActivityRequest([]);
        $logic = new DeleteActivity(new AdminLoginUser([]), $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyIsCanOperation');
        $method->setAccessible(true);

        // 执行方法
        $result = $method->invokeArgs($logic, [['status' => CouponActivity::StatusStop]]);
        $this->assertNull($result);
    }

    /**
     * @testdox 当活动状态处于"停用审核中"时，允许删除。
     * @return void
     * @throws ReflectionException
     */
    public function testVerifyIsCanOperationScene6()
    {
        $request = new DeleteActivityRequest([]);
        $logic = new DeleteActivity(new AdminLoginUser([]), $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyIsCanOperation');
        $method->setAccessible(true);

        $this->expectException(RuntimeException::class);
        $this->expectExceptionCode(RuntimeException::CodeBusinessException);
        $this->expectExceptionMessage('只有活动状态处于"创建中"或"已停用"时，才允许删除。');

        // 执行方法
        $method->invokeArgs($logic, [['status' => CouponActivity::StatusReviewStop]]);
    }

    /**
     * @testdox 记录操作日志
     */
    public function testAddOperationLog()
    {
        $request = new DeleteActivityRequest([]);
        $logic = new DeleteActivity(new AdminLoginUser([]), $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('addOperationLog');
        $method->setAccessible(true);

        $params = [
            'corp_id' => 10001,
            'user_id' => 2,
            'op_interface' => '/admin/coupon_activity/delete_activity',
            'op_content' => [
                'id' => 1,
            ]
        ];

        // 执行方法
        Db::startTrans();
        // int $corp_id, int $user_id, int $user_type, string $op_interface, string $op_content
        $method->invokeArgs($logic, [
            $params['corp_id'], $params['user_id'],
            $params['op_interface'], $params['op_content']
        ]);
        Db::rollback();
        $sql = Db::getLastSql();

        // 断言
        $expected_sql = sprintf(
            "INSERT INTO `coupon_oplog` SET `corp_id` = '%d' , `user_id` = '%d' , `user_type` = 0 , `op_interface` = '%s' , `op_content` = '%s' , `op_time` = '%s'",
            $params['corp_id'], $params['user_id'], $params['op_interface'], addslashes(json_encode($params['op_content'])), date('Y-m-d H:i:s')
        );

        $this->assertSame($expected_sql, $sql);
    }


}