<?php

namespace tests\unit;

use app\common\cache\redis\entity\AdminLoginUser;
use app\common\lib\exception\RuntimeException;
use app\common\lib\VerifyData;
use app\common\logic\admin\coupon_activity\SubmitReview;
use app\common\logic\admin\entity\SubmitReviewRequest;
use app\common\model\CouponActivity;
use app\common\model\CouponActivityStation;
use app\common\model\CouponPattern;
use PHPUnit\Framework\TestCase;
use ReflectionClass;
use ReflectionException;
use Respect\Validation\Validator as v;
use think\Request;
use Throwable;

class TestSubmitReview extends TestCase
{


    /**
     * @testdox 验证参数 - (id)活动ID - 不传递时会验证不通过。
     */
    public function testVerifyParamsActivityIdScene1()
    {
        $params = [];

        try {
            v::input($params, VerifyData::coupon_activity([
                'id',
            ]));
            $this->fail();
        } catch (Throwable $e) {
            $this->assertSame('活动ID 不能是可选的', $e->getMessage());
        }
    }

    /**
     * @testdox 验证参数 - (id)活动ID - 传递非数值时验证不通过。
     */
    public function testVerifyParamsActivityIdScene2()
    {
        $params = [
            'id' => '1'
        ];

        $this->expectExceptionMessage('活动ID 必须是integer类型');

        v::input($params, VerifyData::coupon_activity([
            'id',
        ]));
    }

    /**
     * @testdox 验证参数 - (id)活动ID - 传递低于1的数值时会验证不通过。
     */
    public function testVerifyParamsActivityIdScene3()
    {
        $params = [
            'id' => -1
        ];

        $this->expectExceptionMessage('活动ID 必须大于或等于 1');

        v::input($params, VerifyData::coupon_activity([
            'id',
        ]));
    }

    /**
     * @testdox 验证参数 - (id)活动ID - 传递符合预期的数值，验证通过。
     */
    public function testVerifyParamsActivityIdScene4()
    {
        $params = [
            'id' => 1
        ];

        $verify_params = v::input($params, VerifyData::coupon_activity([
            'id',
        ]));

        $this->assertSame($params, $verify_params);
    }


    /**
     * @testdox 创建提交审核请求对象
     */
    public function testCreateSubmitReviewRequest()
    {
        $params = [
            'id' => 1
        ];
        $request = new SubmitReviewRequest($params);

        $this->assertSame($params['id'], $request->id);
    }

    protected function getLogic(): SubmitReview
    {
        // 准备一个空的用户对象
        $loginUser = new AdminLoginUser([]);

        // 创建业务对象实例
        $request = new SubmitReviewRequest([
            'id' => 1,
        ]);
        return (new SubmitReview($loginUser, $request, new Request()));
    }


    /**
     * @testdox 验证操作权限 - 以超级管理员 或 非运营商账号的身份操作时，会验证不通过。
     * @return void
     * @throws ReflectionException
     */
    public function testVerifyOperationPermissionsScene1()
    {
        // 创建一个业务实例
        $loginUser = new AdminLoginUser(['pid' => 0, 'corp_id' => 0]);
        $request = new SubmitReviewRequest([]);
        $logic = (new SubmitReview($loginUser, $request, new Request()));

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyOperationPermissions');
        $method->setAccessible(true);


        $this->expectExceptionMessage('只有运营商主账号才允许提交审核');

        // 执行方法
        $method->invokeArgs($logic, [$loginUser]);
    }

    /**
     * @testdox 验证操作权限 - 以运营商子账号的身份操作时，会验证不通过。
     * @return void
     * @throws ReflectionException
     */
    public function testVerifyOperationPermissionsScene2()
    {
        // 创建一个业务实例
        $loginUser = new AdminLoginUser(['pid' => 2, 'corp_id' => 10001]);
        $request = new SubmitReviewRequest([]);
        $logic = (new SubmitReview($loginUser, $request, new Request()));

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyOperationPermissions');
        $method->setAccessible(true);

        $this->expectExceptionMessage('只有运营商主账号才允许提交审核');

        // 执行方法
        $method->invokeArgs($logic, [$loginUser]);
    }

    /**
     * @testdox 验证操作权限 - 以运营商主账号的身份操作时，验证才会通过。
     * @return void
     * @throws ReflectionException
     */
    public function testVerifyOperationPermissionsScene3()
    {
        // 创建一个业务实例
        $loginUser = new AdminLoginUser(['pid' => 0, 'corp_id' => 10001]);
        $request = new SubmitReviewRequest([]);
        $logic = (new SubmitReview($loginUser, $request, new Request()));

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyOperationPermissions');
        $method->setAccessible(true);

        // 执行方法
        $result = $method->invokeArgs($logic, [$loginUser]);

        $this->assertNull($result);
    }

    /**
     * @testdox 验证数据权限 - 当优惠券活动没有查询到时，验证不会通过。
     * @return void
     * @throws ReflectionException
     */
    public function testVerifyDataPermissionsScene1()
    {
        // 创建一个业务实例
        $loginUser = new AdminLoginUser(['pid' => 0, 'corp_id' => 10001]);
        $request = new SubmitReviewRequest([]);
        $logic = (new SubmitReview($loginUser, $request, new Request()));

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyDataPermissions');
        $method->setAccessible(true);

        // 断言
        $this->expectExceptionMessage('无效优惠券活动ID');

        // 执行方法
        $method->invokeArgs($logic, [$loginUser, null]);
    }

    /**
     * @testdox 验证数据权限 - 当优惠券活动已经被删除时，验证不会通过。
     * @return void
     * @throws ReflectionException
     */
    public function testVerifyDataPermissionsScene2()
    {
        // 创建一个业务实例
        $loginUser = new AdminLoginUser(['pid' => 0, 'corp_id' => 10001]);
        $request = new SubmitReviewRequest([]);
        $logic = (new SubmitReview($loginUser, $request, new Request()));

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyDataPermissions');
        $method->setAccessible(true);

        // 断言
        $this->expectExceptionMessage('无效优惠券活动ID');

        // 执行方法
        $method->invokeArgs($logic, [$loginUser, ['is_del' => CouponActivity::IsDelYes]]);
    }

    /**
     * @testdox 验证数据权限 - 当优惠券活动不是当前用户创建的时，验证不会通过。
     * @return void
     * @throws ReflectionException
     */
    public function testVerifyDataPermissionsScene3()
    {
        // 创建一个业务实例
        $loginUser = new AdminLoginUser(['id' => 2, 'pid' => 0, 'corp_id' => 10001]);
        $request = new SubmitReviewRequest([]);
        $logic = (new SubmitReview($loginUser, $request, new Request()));

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyDataPermissions');
        $method->setAccessible(true);

        // 断言
        $this->expectExceptionMessage('无效优惠券活动ID');

        // 执行方法
        $method->invokeArgs($logic, [$loginUser, [
            'is_del' => CouponActivity::IsDelNot,
            'user_id' => 3
        ]]);
    }


    /**
     * @testdox 验证是否能够提交审核 - 当状态不是处于"创建中"时，验证不会通过。
     * @return void
     * @throws ReflectionException
     */
    public function testVerifyIsCanSubmitReviewScene1()
    {
        // 创建一个业务实例
        $loginUser = new AdminLoginUser(['id' => 2, 'pid' => 0, 'corp_id' => 10001]);
        $request = new SubmitReviewRequest([]);
        $logic = (new SubmitReview($loginUser, $request, new Request()));

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyIsCanSubmitReview');
        $method->setAccessible(true);

        // 断言
        $this->expectExceptionMessage('只有当活动处于创建中才能够提交审核!');

        // 执行方法
        $method->invokeArgs($logic, [['status' => CouponActivity::StatusReview]]);
    }

    /**
     * @testdox 验证是否能够提交审核 - 当活动还没有关联场站时，验证不会通过。
     * @return void
     * @throws ReflectionException
     */
    public function testVerifyIsCanSubmitReviewScene2()
    {
        // 创建一个业务实例
        $loginUser = new AdminLoginUser(['id' => 2, 'pid' => 0, 'corp_id' => 10001]);
        $request = new SubmitReviewRequest([]);
        $logic = (new SubmitReview($loginUser, $request, new Request()));

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyIsCanSubmitReview');
        $method->setAccessible(true);

        // 模拟查询活动关联的场站数据，并返回结果为0。
        $model = $this->createStub(CouponActivityStation::class);
        $model->method('getActivityStationCount')->willReturn(0);
        bind(CouponActivityStation::class, $model);

        // 断言
        $this->expectExceptionMessage('请先选择与活动关联的场站后再提交审核！');

        // 执行方法
        $method->invokeArgs($logic, [['id' => 1, 'status' => CouponActivity::StatusCreating]]);
    }

    /**
     * @testdox 验证是否能够提交审核 - 当活动还没有添加优惠券时，验证不会通过。
     * @return void
     * @throws ReflectionException
     */
    public function testVerifyIsCanSubmitReviewScene3()
    {
        // 创建一个业务实例
        $loginUser = new AdminLoginUser(['id' => 2, 'pid' => 0, 'corp_id' => 10001]);
        $request = new SubmitReviewRequest([]);
        $logic = (new SubmitReview($loginUser, $request, new Request()));

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyIsCanSubmitReview');
        $method->setAccessible(true);

        // 模拟查询活动关联的场站数据，并返回结果为1。
        $model = $this->createStub(CouponActivityStation::class);
        $model->method('getActivityStationCount')->willReturn(1);
        bind(CouponActivityStation::class, $model);

        // 模拟查询活动关联的优惠券数量，并返回结果为0。
        $model = $this->createStub(CouponPattern::class);
        $model->method('getActivityPatternCount')->willReturn(0);
        bind(CouponPattern::class, $model);

        // 断言
        $this->expectExceptionMessage('请先为活动添加关联优惠券再提交审核！');

        // 执行方法
        $method->invokeArgs($logic, [['id' => 1, 'status' => CouponActivity::StatusCreating]]);
    }

    /**
     * @testdox 验证是否能够提交审核 - 最理想的情况，活动状态处于"创建中"，有关联的场站与优惠券，这样验证就会通过。
     * @return void
     * @throws ReflectionException
     */
    public function testVerifyIsCanSubmitReviewScene4()
    {
        // 创建一个业务实例
        $loginUser = new AdminLoginUser(['id' => 2, 'pid' => 0, 'corp_id' => 10001]);
        $request = new SubmitReviewRequest([]);
        $logic = (new SubmitReview($loginUser, $request, new Request()));

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyIsCanSubmitReview');
        $method->setAccessible(true);

        // 模拟查询活动关联的场站数据，并返回结果为1。
        $model = $this->createStub(CouponActivityStation::class);
        $model->method('getActivityStationCount')->willReturn(1);
        bind(CouponActivityStation::class, $model);

        // 模拟查询活动关联的优惠券数量，并返回结果为3。
        $model = $this->createStub(CouponPattern::class);
        $model->method('getActivityPatternCount')->willReturn(3);
        bind(CouponPattern::class, $model);

        // 执行方法
        $result = $method->invokeArgs($logic, [['id' => 1, 'status' => CouponActivity::StatusCreating]]);

        $this->assertNull($result);
    }



}