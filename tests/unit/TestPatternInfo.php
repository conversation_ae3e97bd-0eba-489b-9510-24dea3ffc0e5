<?php

namespace tests\unit;

use app\common\cache\redis\entity\AdminLoginUser;
use app\common\lib\exception\RuntimeException;
use app\common\lib\VerifyData;
use app\common\logic\admin\coupon_activity\ActivityList;
use app\common\logic\admin\coupon_activity\CreateActivity;
use app\common\logic\admin\coupon_activity\PatternInfo;
use app\common\logic\admin\coupon_activity\PatternList;
use app\common\logic\admin\entity\ActivityListRequest;
use app\common\logic\admin\entity\CreateActivityRequest;
use app\common\logic\admin\entity\PatternInfoRequest;
use app\common\logic\admin\entity\PatternListRequest;
use app\common\model\CouponActivity;
use app\common\model\CouponActivity as CouponActivityModel;
use app\common\model\CouponPattern;
use app\common\model\Stations;
use PHPUnit\Framework\TestCase;
use ReflectionClass;
use ReflectionException;
use Respect\Validation\Validator as v;
use think\db\exception\DbException;
use Throwable;
use PHPUnit\Util\TestDox\TextResultPrinter;

/**
 * @testdox 运营后台现金券详情
 */
class TestPatternInfo extends TestCase
{
    /**
     * @testdox 验证参数 - (pattern_id)现金券ID - 不传递时会验证不通过。
     */
    public function testVerifyParamsActivityIdScene1()
    {
        $params = [];

        try {
            v::input($params, VerifyData::coupon_activity([
                'pattern_id',
            ]));
            $this->fail();
        } catch (Throwable $e) {
            $this->assertSame('现金券ID 不能是可选的', $e->getMessage());
        }
    }

    /**
     * @testdox 验证参数 - (pattern_id)现金券ID - 传递非数值时验证不通过。
     */
    public function testVerifyParamsActivityIdScene2()
    {
        $params = [
            'pattern_id' => '1'
        ];

        try {
            v::input($params, VerifyData::coupon_activity([
                'pattern_id',
            ]));
            $this->fail();
        } catch (Throwable $e) {
            $this->assertSame('现金券ID 必须是integer类型', $e->getMessage());
        }
    }

    /**
     * @testdox 验证参数 - (pattern_id)现金券ID - 传递低于1的数值时会验证不通过。
     */
    public function testVerifyParamsActivityIdScene3()
    {
        $params = [
            'pattern_id' => -1
        ];

        $this->expectExceptionMessage('现金券ID 必须大于或等于 1');

        v::input($params, VerifyData::coupon_activity([
            'pattern_id',
        ]));
    }

    /**
     * @testdox 验证参数 - (pattern_id)现金券ID - 传递符合预期的数值，验证通过。
     */
    public function testVerifyParamsActivityIdScene4()
    {
        $params = [
            'pattern_id' => 1
        ];

        $verify_params = v::input($params, VerifyData::coupon_activity([
            'pattern_id',
        ]));

        $this->assertSame($params, $verify_params);
    }

    /**
     * @testdox 验证参数 - 所有参数
     */
    public function testVerifyParamsAll()
    {
        $params = [
            'pattern_id' => 1,
        ];

        $verify_params = v::input($params, VerifyData::coupon_activity([
            'pattern_id',
        ]));

        $this->assertSame($params, $verify_params);
    }

    /**
     * @testdox 创建现金券详情请求对象
     */
    public function testCreatePatternInfoRequest()
    {
        $params = [
            'pattern_id' => 1,
        ];
        $request = new PatternInfoRequest($params);
        $this->assertSame($params, $request->toArray());
    }


    /**
     * @testdox 添加数据权限筛选条件 - 当操作员是超级管理员 或 非运营商账号时，预期不会增加额外的过滤条件。
     * @return void
     * @throws ReflectionException
     */
    public function testAddDataPermissionsFilterConditionsScene1()
    {
        $loginUser = new AdminLoginUser(['pid' => 0, 'corp_id' => 0]);
        $request = new PatternInfoRequest([]);
        $logic = new PatternInfo($loginUser, $request);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('addDataPermissionsFilterConditions');
        $method->setAccessible(true);

        // 传递的参数
        $params = [
            'pattern_id' => 1
        ];
        $expect_params = [
            'pattern_id' => 1
        ];

        $new_params = $method->invokeArgs($logic, [$loginUser, $params]);
        $this->assertIsArray($new_params);
        $this->assertSame($expect_params, $new_params);
    }

    /**
     * @testdox 添加数据权限筛选条件 - 当操作员是运营商主账号 或 运营商子账号时，预期会额外增加一个过滤条件：filter_corp_id。
     * @return void
     * @throws ReflectionException
     */
    public function testAddDataPermissionsFilterConditionsScene2()
    {
        $loginUser = new AdminLoginUser(['pid' => 0, 'corp_id' => 10001]);
        $request = new PatternInfoRequest([]);
        $logic = new PatternInfo($loginUser, $request);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('addDataPermissionsFilterConditions');
        $method->setAccessible(true);

        // 传递的参数
        $params = [
            'pattern_id' => 1
        ];
        $expect_params = [
            'pattern_id' => 1,
            'filter_corp_id' => $loginUser->corp_id
        ];

        $new_params = $method->invokeArgs($logic, [$loginUser, $params]);
        $this->assertIsArray($new_params);
        $this->assertSame($expect_params, $new_params);
    }

    /**
     * @testdox 整理筛选条件 - 只有现金券ID作为诗选条件时
     * @return void
     * @throws ReflectionException
     */
    public function testArrangeFiltersScene1()
    {
        $loginUser = new AdminLoginUser(['pid' => 0, 'corp_id' => 10001]);
        $request = new PatternInfoRequest([]);
        $logic = new PatternInfo($loginUser, $request);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('arrangeFilters');
        $method->setAccessible(true);

        // 传递的参数
        $params = [
            'pattern_id' => 1,
        ];
        $expect_filters = [
            ['cp.id', '=', 1],
            ['ca.is_del', '=', CouponActivity::IsDelNot],
        ];

        $filters = $method->invokeArgs($logic, [$params]);
        $this->assertIsArray($filters);
        $this->assertSame($expect_filters, $filters);
    }

    /**
     * @testdox 整理筛选条件 - 含有运营商ID过滤条件的情况
     * @return void
     * @throws ReflectionException
     */
    public function testArrangeFiltersScene2()
    {
        $loginUser = new AdminLoginUser(['pid' => 0, 'corp_id' => 10001]);
        $request = new PatternInfoRequest([]);
        $logic = new PatternInfo($loginUser, $request);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('arrangeFilters');
        $method->setAccessible(true);

        // 传递的参数
        $params = [
            'pattern_id' => 1,
            'filter_corp_id' => $loginUser->corp_id
        ];
        $expect_filters = [
            ['cp.id', '=', 1],
            ['ca.is_del', '=', CouponActivity::IsDelNot],
            ['ca.corp_id', '=', $loginUser->corp_id]
        ];

        $filters = $method->invokeArgs($logic, [$params]);
        $this->assertIsArray($filters);
        $this->assertSame($expect_filters, $filters);
    }
}