<?php

namespace tests\unit;

use app\common\cache\redis\entity\AdminLoginUser;
use app\common\lib\exception\RuntimeException;
use app\common\lib\VerifyData;
use app\common\logic\admin\coupon_activity\ActivityList;
use app\common\logic\admin\coupon_activity\CreateActivity;
use app\common\logic\admin\coupon_activity\PatternList;
use app\common\logic\admin\entity\ActivityListRequest;
use app\common\logic\admin\entity\CreateActivityRequest;
use app\common\logic\admin\entity\PatternListRequest;
use app\common\model\CouponActivity;
use app\common\model\CouponActivity as CouponActivityModel;
use app\common\model\CouponPattern;
use app\common\model\Stations;
use PHPUnit\Framework\TestCase;
use ReflectionClass;
use ReflectionException;
use Respect\Validation\Validator as v;
use think\db\exception\DbException;
use Throwable;
use PHPUnit\Util\TestDox\TextResultPrinter;

/**
 * @testdox 运营后台现金券列表
 */
class TestPatternAdminList extends TestCase
{
    /**
     * @testdox 验证参数 - (activity_id)活动ID - 不传递时会验证不通过。
     */
    public function testVerifyParamsActivityIdScene1()
    {
        $params = [];

        try {
            v::input($params, VerifyData::coupon_activity([
                'activity_id',
            ]));
            $this->fail();
        } catch (Throwable $e) {
            $this->assertSame('活动ID 不能是可选的', $e->getMessage());
        }
    }

    /**
     * @testdox 验证参数 - (activity_id)活动ID - 传递非数值时验证不通过。
     */
    public function testVerifyParamsActivityIdScene2()
    {
        $params = [
            'activity_id' => '1'
        ];

        try {
            v::input($params, VerifyData::coupon_activity([
                'activity_id',
            ]));
            $this->fail();
        } catch (Throwable $e) {
            $this->assertSame('活动ID 必须是integer类型', $e->getMessage());
        }
    }

    /**
     * @testdox 验证参数 - (activity_id)活动ID - 传递低于1的数值时会验证不通过。
     */
    public function testVerifyParamsActivityIdScene3()
    {
        $params = [
            'activity_id' => -1
        ];

        try {
            v::input($params, VerifyData::coupon_activity([
                'activity_id',
            ]));
            $this->fail();
        } catch (Throwable $e) {
            $this->assertSame('活动ID 必须大于或等于 1', $e->getMessage());
        }
    }

    /**
     * @testdox 验证参数 - (activity_id)活动ID - 传递符合预期的数值，验证通过。
     */
    public function testVerifyParamsActivityIdScene4()
    {
        $params = [
            'activity_id' => 1
        ];

        try {
            $verify_params = v::input($params, VerifyData::coupon_activity([
                'activity_id',
            ]));

            $this->assertSame($params, $verify_params);
        } catch (Throwable $e) {
            $this->fail('不应该会抛出异常的');
        }
    }

    /**
     * @testdox 验证参数 - (page)页码 - 不传递时会验证不通过。
     */
    public function testVerifyParamsPageScene1()
    {
        $params = [];

        try {
            v::input($params, VerifyData::coupon_activity([
                'page',
            ]));
            $this->fail();
        } catch (Throwable $e) {
            $this->assertSame('页码 不能为空', $e->getMessage());
        }
    }

    /**
     * @testdox 验证参数 - (page)页码 - 传递非数值时验证不通过。
     */
    public function testVerifyParamsPageScene2()
    {
        $params = [
            'page' => '1'
        ];

        try {
            v::input($params, VerifyData::coupon_activity([
                'page',
            ]));
            $this->fail();
        } catch (Throwable $e) {
            $this->assertSame('页码 必须是integer类型', $e->getMessage());
        }
    }

    /**
     * @testdox 验证参数 - (page)页码 - 传递低于1的数值时会验证不通过。
     */
    public function testVerifyParamsPageScene3()
    {
        $params = [
            'page' => -1
        ];

        try {
            v::input($params, VerifyData::coupon_activity([
                'page',
            ]));
            $this->fail();
        } catch (Throwable $e) {
            $this->assertSame('页码 必须大于或等于 1', $e->getMessage());
        }
    }

    /**
     * @testdox 验证参数 - (page)页码 - 传递符合预期的数值，验证通过。
     */
    public function testVerifyParamsPageScene4()
    {
        $params = [
            'page' => 1
        ];

        try {
            $verify_params = v::input($params, VerifyData::coupon_activity([
                'page',
            ]));

            $this->assertSame($params, $verify_params);
        } catch (Throwable $e) {
            $this->fail('不应该会抛出异常的');
        }
    }


    /**
     * @testdox 验证参数 - (limit)显示条数 - 不传递时会验证不通过。
     */
    public function testVerifyParamsLimitScene1()
    {
        $params = [];

        try {
            v::input($params, VerifyData::coupon_activity([
                'limit',
            ]));
            $this->fail();
        } catch (Throwable $e) {
            $this->assertSame('显示条数 不能为空', $e->getMessage());
        }
    }

    /**
     * @testdox 验证参数 - (limit)显示条数 - 传递非数值时验证不通过。
     */
    public function testVerifyParamsLimitScene2()
    {
        $params = [
            'limit' => '1'
        ];

        try {
            v::input($params, VerifyData::coupon_activity([
                'limit',
            ]));
            $this->fail();
        } catch (Throwable $e) {
            $this->assertSame('显示条数 必须是integer类型', $e->getMessage());
        }
    }

    /**
     * @testdox 验证参数 - (limit)显示条数 - 传递低于1的数值时会验证不通过。
     */
    public function testVerifyParamsLimitScene3()
    {
        $params = [
            'limit' => -1
        ];

        try {
            v::input($params, VerifyData::coupon_activity([
                'limit',
            ]));
            $this->fail();
        } catch (Throwable $e) {
            $this->assertSame('显示条数 必须大于或等于 1', $e->getMessage());
        }
    }

    /**
     * @testdox 验证参数 - (limit)显示条数 - 传递符合预期的数值，验证通过。
     */
    public function testVerifyParamsLimitScene4()
    {
        $params = [
            'limit' => 10
        ];

        try {
            $verify_params = v::input($params, VerifyData::coupon_activity([
                'limit',
            ]));

            $this->assertSame($params, $verify_params);
        } catch (Throwable $e) {
            $this->fail('不应该会抛出异常的');
        }
    }


    /**
     * @testdox 验证参数 - (sort_field)排序字段 - 不传递时会验证不通过。
     */
    public function testVerifyParamsSortFieldScene1()
    {
        $params = [];

        try {
            v::input($params, VerifyData::coupon_activity([
                'sort_field',
            ]));
            $this->fail();
        } catch (Throwable $e) {
            $this->assertSame('排序字段 不能为空', $e->getMessage());
        }
    }

    /**
     * @testdox 验证参数 - (sort_field)排序字段 - 传递非数值时验证不通过。
     */
    public function testVerifyParamsSortFieldScene2()
    {
        $params = [
            'sort_field' => '1'
        ];

        try {
            v::input($params, VerifyData::coupon_activity([
                'sort_field',
            ]));
            $this->fail();
        } catch (Throwable $e) {
            $this->assertSame('排序字段 必须是integer类型', $e->getMessage());
        }
    }

    /**
     * @testdox 验证参数 - (sort_field)排序字段 - 传递低于1的数值时会验证不通过。
     */
    public function testVerifyParamsSortFieldScene3()
    {
        $params = [
            'sort_field' => -1
        ];

        try {
            v::input($params, VerifyData::coupon_activity([
                'sort_field',
            ]));
            $this->fail();
        } catch (Throwable $e) {
            $this->assertSame('排序字段 必须大于或等于 1', $e->getMessage());
        }
    }

    /**
     * @testdox 验证参数 - (sort_field)排序字段 - 传递符合预期的数值，验证通过。
     */
    public function testVerifyParamsSortFieldScene4()
    {
        $params = [
            'sort_field' => 1
        ];

        try {
            $verify_params = v::input($params, VerifyData::coupon_activity([
                'sort_field',
            ]));

            $this->assertSame($params, $verify_params);
        } catch (Throwable $e) {
            $this->fail('不应该会抛出异常的');
        }
    }


    /**
     * @testdox 验证参数 - (sort_type)排序类型 - 不传递时，会验证不通过。
     */
    public function testVerifyParamsSortTypeScene1()
    {
        $params = [];

        try {
            v::input($params, VerifyData::coupon_activity([
                'sort_type',
            ]));
            $this->fail();
        } catch (Throwable $e) {
            $this->assertSame('排序类型 不能为空', $e->getMessage());
        }
    }

    /**
     * @testdox 验证参数 - (sort_type)排序类型 - 传递非数值时，验证不通过。
     */
    public function testVerifyParamsSortTypeScene2()
    {
        $params = [
            'sort_type' => '1'
        ];

        try {
            v::input($params, VerifyData::coupon_activity([
                'sort_type',
            ]));
            $this->fail();
        } catch (Throwable $e) {
            $this->assertSame('排序类型 必须是integer类型', $e->getMessage());
        }
    }

    /**
     * @testdox 验证参数 - (sort_type)排序类型 - 传递的数值不是1或2时，验证会不通过。
     */
    public function testVerifyParamsSortTypeScene3()
    {
        $params = [
            'sort_type' => -1
        ];

        try {
            v::input($params, VerifyData::coupon_activity([
                'sort_type',
            ]));
            $this->fail();
        } catch (Throwable $e) {
            $this->assertSame('排序类型 必须在 `{ 1, 2 }` 中', $e->getMessage());
        }
    }

    /**
     * @testdox 验证参数 - (sort_type)排序类型 - 传递符合预期的数值，验证通过。
     */
    public function testVerifyParamsSortTypeScene4()
    {
        $params = [
            'sort_type' => 1
        ];

        try {
            $verify_params = v::input($params, VerifyData::coupon_activity([
                'sort_type',
            ]));

            $this->assertSame($params, $verify_params);
        } catch (Throwable $e) {
            $this->fail('不应该会抛出异常的');
        }
    }

    /**
     * @testdox 验证参数 - 所有参数
     */
    public function testVerifyParamsAll()
    {
        $params = [
            'page' => 1,
            'limit' => 10,
            'sort_field' => 1,
            'sort_type' => 1,
            'activity_id' => 1,
        ];

        $verify_params = v::input($params, VerifyData::coupon_activity([
            'activity_id',
            'page', 'limit',
            'sort_field', 'sort_type',
        ]));

        $this->assertSame($params, $verify_params);
    }

    /**
     * @testdox 创建现金券列表请求对象
     */
    public function testCreatePatternListRequest()
    {
        $params = [
            'activity_id' => 1,
            'page' => 1,
            'limit' => 10,
            'sort_field' => 1,
            'sort_type' => 1
        ];
        $request = new PatternListRequest($params);
        $this->assertSame($params, $request->toArray());
    }

    /**
     * @testdox 获取排序类型
     */
    public function testGetSortType()
    {
        $request = new PatternListRequest([]);
        $logic = new PatternList(new AdminLoginUser([]), $request);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('getSortType');
        $method->setAccessible(true);

        $result = $method->invokeArgs($logic, [ActivityList::SortTypeDesc]);
        $this->assertSame('DESC', $result);
        $result = $method->invokeArgs($logic, [ActivityList::SortTypeAsc]);
        $this->assertSame('ASC', $result);
    }

    /**
     * @testdox 获取排序字段名
     */
    public function testGetSortField()
    {
        $request = new PatternListRequest([]);
        $logic = new PatternList(new AdminLoginUser([]), $request);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('getSortField');
        $method->setAccessible(true);

        foreach (PatternList::SortFieldsMap as $value => $field) {
            $result = $method->invokeArgs($logic, [$value]);
            $this->assertIsString($result);
            $this->assertSame($field, $result);
        }
    }


    /**
     * @testdox 转换排序字段
     */
    public function testTransformSortParams()
    {
        $request = new PatternListRequest([]);
        $logic = new PatternList(new AdminLoginUser([]), $request);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('transformSortParams');
        $method->setAccessible(true);

        foreach (PatternList::SortFieldsMap as $value => $field) {
            foreach (PatternList::SortTypeMap as $type_value => $type_name) {
                $params = [
                    'sort_field' => $value,
                    'sort_type' => $type_value
                ];
                $result = $method->invokeArgs($logic, [$params]);
                // 返回值是数组
                $this->assertIsArray($result);
                // 有这两个Key
                $this->assertArrayHasKey('sort_field', $result);
                $this->assertArrayHasKey('sort_type', $result);
                // 都是字符床类型
                $this->assertIsString($result['sort_field']);
                $this->assertIsString($result['sort_type']);
                // 与预期的一致
                $this->assertSame($field, $result['sort_field']);
                $this->assertSame($type_name, $result['sort_type']);
            }
        }
    }

    /**
     * @testdox 整理过滤条件 - 传递活动ID
     * @return void
     * @throws ReflectionException
     */
    public function testArrangeFiltersScene1()
    {
        $request = new PatternListRequest([]);
        $logic = new PatternList(new AdminLoginUser([]), $request);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('arrangeFilters');
        $method->setAccessible(true);

        // 预期的结果
        $expect_filters = [
            ['cp.activity_id', '=', 1],
            ['ca.is_del', '=', CouponActivity::IsDelNot],
        ];
        // 传递的参数
        $params = [
            'activity_id' => 1
        ];

        $filters = $method->invokeArgs($logic, [$params]);
        $this->assertIsArray($filters);
        $this->assertSame($expect_filters, $filters);
    }


    /**
     * @testdox 验证查询列表数据的SQL语句 - 含有所有过滤条件
     * @return void
     */
    public function testGetListDataSql()
    {
        $filters = [
            ['ca.is_del', '=', CouponActivity::IsDelNot],
            ['ca.id', '=', 1],
            ['ca.name', 'like', '%0926%'],
            ['ca.status', '=', CouponActivity::StatusReview],
            ['ca.corp_id', '=', 10001]
        ];
        $result = (new CouponPattern())->getListData(
            $filters, 1, 10,
            'ca.id', 'DESC', true
        );
        $expect_sql = "( SELECT `cp`.`id`,`cp`.`name`,`cp`.`par_value`,`cp`.`usage_amount`,`cp`.`create_time` FROM `coupon_pattern` `cp` LEFT JOIN `coupon_activity` `ca` ON `ca`.`id`=`cp`.`activity_id` WHERE  `ca`.`is_del` = '0'  AND `ca`.`id` = '1'  AND `ca`.`name` LIKE '%0926%'  AND `ca`.`status` = '2'  AND `ca`.`corp_id` = '10001' ORDER BY `ca`.`id` DESC )";
        $this->assertSame($expect_sql, $result['sql']);
    }
}