<?php

namespace tests\unit;

use app\common\cache\redis\entity\AdminLoginUser;
use app\common\lib\VerifyData;
use app\common\logic\admin\coupon_activity\StopActivity;
use app\common\logic\admin\entity\StopActivityRequest;
use app\common\model\CouponActivity;
use PHPUnit\Framework\TestCase;
use ReflectionClass;
use ReflectionException;
use Respect\Validation\Validator as v;
use Throwable;

/**
 * @testdox 停用活动
 */
class TestStopActivity extends TestCase
{
    /**
     * @testdox 验证参数 - (id)活动ID - 不传递时会验证不通过。
     */
    public function testVerifyParamsActivityIdScene1()
    {
        $params = [];

        try {
            v::input($params, VerifyData::coupon_activity([
                'id',
            ]));
            $this->fail();
        } catch (Throwable $e) {
            $this->assertSame('活动ID 不能是可选的', $e->getMessage());
        }
    }

    /**
     * @testdox 验证参数 - (id)活动ID - 传递非数值时验证不通过。
     */
    public function testVerifyParamsActivityIdScene2()
    {
        $params = [
            'id' => '1'
        ];

        $this->expectExceptionMessage('活动ID 必须是integer类型');

        v::input($params, VerifyData::coupon_activity([
            'id',
        ]));
    }

    /**
     * @testdox 验证参数 - (id)活动ID - 传递低于1的数值时会验证不通过。
     */
    public function testVerifyParamsActivityIdScene3()
    {
        $params = [
            'id' => -1
        ];

        $this->expectExceptionMessage('活动ID 必须大于或等于 1');

        v::input($params, VerifyData::coupon_activity([
            'id',
        ]));
    }

    /**
     * @testdox 验证参数 - (id)活动ID - 传递符合预期的数值，验证通过。
     */
    public function testVerifyParamsActivityIdScene4()
    {
        $params = [
            'id' => 1
        ];

        $verify_params = v::input($params, VerifyData::coupon_activity([
            'id',
        ]));

        $this->assertSame($params, $verify_params);
    }


    /**
     * @testdox 创建停用活动请求对象
     */
    public function testCreateStopActivityRequest()
    {
        $params = [
            'id' => 1
        ];
        $request = new StopActivityRequest($params);

        $this->assertSame($params['id'], $request->id);
    }


    /**
     * @testdox 验证操作权限 - 以超级管理员 或 非运营商账号的身份操作时，会验证不通过。
     * @return void
     * @throws ReflectionException
     */
    public function testVerifyOperationPermissionsScene1()
    {
        // 创建一个业务实例
        $loginUser = new AdminLoginUser(['pid' => 0, 'corp_id' => 0]);
        $request = new StopActivityRequest([]);
        $logic = (new StopActivity($loginUser, $request));

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyOperationPermissions');
        $method->setAccessible(true);


        $this->expectExceptionMessage('只有运营商主账号才允许停用活动');

        // 执行方法
        $method->invokeArgs($logic, [$loginUser]);
    }

    /**
     * @testdox 验证操作权限 - 以运营商子账号的身份操作时，会验证不通过。
     * @return void
     * @throws ReflectionException
     */
    public function testVerifyOperationPermissionsScene2()
    {
        // 创建一个业务实例
        $loginUser = new AdminLoginUser(['pid' => 2, 'corp_id' => 10001]);
        $request = new StopActivityRequest([]);
        $logic = (new StopActivity($loginUser, $request));

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyOperationPermissions');
        $method->setAccessible(true);

        $this->expectExceptionMessage('只有运营商主账号才允许停用活动');

        // 执行方法
        $method->invokeArgs($logic, [$loginUser]);
    }

    /**
     * @testdox 验证操作权限 - 以运营商主账号的身份操作时，验证才会通过。
     * @return void
     * @throws ReflectionException
     */
    public function testVerifyOperationPermissionsScene3()
    {
        // 创建一个业务实例
        $loginUser = new AdminLoginUser(['pid' => 0, 'corp_id' => 10001]);
        $request = new StopActivityRequest([]);
        $logic = (new StopActivity($loginUser, $request));

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyOperationPermissions');
        $method->setAccessible(true);

        // 执行方法
        $result = $method->invokeArgs($logic, [$loginUser]);

        $this->assertNull($result);
    }

    /**
     * @testdox 验证数据权限 - 当优惠券活动没有查询到时，验证不会通过。
     * @return void
     * @throws ReflectionException
     */
    public function testVerifyDataPermissionsScene1()
    {
        // 创建一个业务实例
        $loginUser = new AdminLoginUser(['pid' => 0, 'corp_id' => 10001]);
        $request = new StopActivityRequest([]);
        $logic = (new StopActivity($loginUser, $request));

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyDataPermissions');
        $method->setAccessible(true);

        // 断言
        $this->expectExceptionMessage('无效优惠券活动ID');

        // 执行方法
        $method->invokeArgs($logic, [$loginUser, null]);
    }

    /**
     * @testdox 验证数据权限 - 当优惠券活动已经被删除时，验证不会通过。
     * @return void
     * @throws ReflectionException
     */
    public function testVerifyDataPermissionsScene2()
    {
        // 创建一个业务实例
        $loginUser = new AdminLoginUser(['pid' => 0, 'corp_id' => 10001]);
        $request = new StopActivityRequest([]);
        $logic = (new StopActivity($loginUser, $request));

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyDataPermissions');
        $method->setAccessible(true);

        // 断言
        $this->expectExceptionMessage('无效优惠券活动ID');

        // 执行方法
        $method->invokeArgs($logic, [$loginUser, ['is_del' => CouponActivity::IsDelYes]]);
    }

    /**
     * @testdox 验证数据权限 - 当优惠券活动不是当前用户创建的时，验证不会通过。
     * @return void
     * @throws ReflectionException
     */
    public function testVerifyDataPermissionsScene3()
    {
        // 创建一个业务实例
        $loginUser = new AdminLoginUser(['id' => 2, 'pid' => 0, 'corp_id' => 10001]);
        $request = new StopActivityRequest([]);
        $logic = (new StopActivity($loginUser, $request));

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyDataPermissions');
        $method->setAccessible(true);

        // 断言
        $this->expectExceptionMessage('无效优惠券活动ID');

        // 执行方法
        $method->invokeArgs($logic, [$loginUser, [
            'is_del' => CouponActivity::IsDelNot,
            'user_id' => 3
        ]]);
    }


    /**
     * @testdox 验证是否能够提交审核 - 当状态不是处于"发放中"时，验证不会通过。
     * @return void
     * @throws ReflectionException
     */
    public function testVerifyIsCanSubmitReviewScene1()
    {
        // 创建一个业务实例
        $loginUser = new AdminLoginUser(['id' => 2, 'pid' => 0, 'corp_id' => 10001]);
        $request = new StopActivityRequest([]);
        $logic = (new StopActivity($loginUser, $request));

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyIsCanOperation');
        $method->setAccessible(true);

        // 断言
        $this->expectExceptionMessage('只有当活动状态处于"发放中"才能够停用活动!');

        // 执行方法
        $method->invokeArgs($logic, [['status' => CouponActivity::StatusReview]]);
    }


    /**
     * @testdox 验证是否能够提交审核 - 当状态处于"发放中"时，验证才会通过。
     * @return void
     * @throws ReflectionException
     */
    public function testVerifyIsCanSubmitReviewScene4()
    {
        // 创建一个业务实例
        $loginUser = new AdminLoginUser(['id' => 2, 'pid' => 0, 'corp_id' => 10001]);
        $request = new StopActivityRequest([]);
        $logic = (new StopActivity($loginUser, $request));

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyIsCanOperation');
        $method->setAccessible(true);


        // 执行方法
        $result = $method->invokeArgs($logic, [['id' => 1, 'status' => CouponActivity::StatusGrant]]);

        $this->assertNull($result);
    }


}