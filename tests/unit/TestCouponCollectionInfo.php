<?php


use app\common\cache\redis\entity\AdminLoginUser;
use app\common\lib\exception\RuntimeException;
use app\common\lib\VerifyData;
use app\common\logic\admin\coupon_activity\ActivityInfo;
use app\common\logic\admin\coupon_collection\CouponCollectionInfo;
use app\common\logic\admin\coupon_collection\entity\CouponCollectionInfoRequest;
use app\common\logic\admin\entity\ActivityInfoRequest;
use app\common\model\CouponActivity;
use app\common\model\CouponActivityStation;
use app\common\model\CouponCollection;
use PHPUnit\Framework\TestCase;
use Respect\Validation\Validator as v;

/**
 * @testdox 用户优惠券详情
 */
class TestCouponCollectionInfo extends TestCase
{
    /**
     * @testdox 验证参数 - (id)用户优惠券ID - 不传递时会验证不通过。
     */
    public function testVerifyParamsActivityIdScene1()
    {
        $params = [];

        $this->expectExceptionMessage('用户优惠券ID 不能为空');

        v::input($params, VerifyData::admin_coupon_collection([
            'id',
        ]));
    }

    /**
     * @testdox 验证参数 - (id)用户优惠券ID - 传递非数值时验证不通过。
     */
    public function testVerifyParamsActivityIdScene2()
    {
        $params = [
            'id' => '1'
        ];

        $this->expectExceptionMessage('用户优惠券ID 必须是integer类型');

        v::input($params, VerifyData::admin_coupon_collection([
            'id',
        ]));
    }

    /**
     * @testdox 验证参数 - (id)用户优惠券ID - 传递低于1的数值时会验证不通过。
     */
    public function testVerifyParamsActivityIdScene3()
    {
        $params = [
            'id' => -1
        ];

        $this->expectExceptionMessage('用户优惠券ID 必须大于或等于 1');

        v::input($params, VerifyData::admin_coupon_collection([
            'id',
        ]));
    }

    /**
     * @testdox 验证参数 - (id)用户优惠券ID - 传递符合预期的数值，验证通过。
     */
    public function testVerifyParamsActivityIdScene4()
    {
        $params = [
            'id' => 1
        ];

        $verify_params = v::input($params, VerifyData::admin_coupon_collection([
            'id',
        ]));

        $this->assertSame($params, $verify_params);
    }

    /**
     * @testdox 创建详情的请求对象
     * @return void
     */
    public function testCreateRequest()
    {
        $params = [
            'id' => 1
        ];
        $request = new CouponCollectionInfoRequest($params);
        $this->assertSame($params['id'], $request->id);
        $this->assertSame($params, $request->toArray());
    }

    /**
     * @testdox 增加数据权限过滤条件 - 超级管理员身份 或 其他非运营商账号身份
     */
    public function testAddDataPermissionsFilterConditionsScene1()
    {
        $request = new CouponCollectionInfoRequest([]);
        $logic = new CouponCollectionInfo(new AdminLoginUser([]), $request);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('addDataPermissionsFilterConditions');
        $method->setAccessible(true);

        $result = $method->invokeArgs($logic, [new AdminLoginUser(['pid' => 0, 'corp_id' => 0]), []]);
        $this->assertIsArray($result);
        $this->assertSame([], $result);
    }

    /**
     * @testdox 增加数据权限过滤条件 - 运营商主账号身份 或 运营商子账号身份
     */
    public function testAddDataPermissionsFilterConditionsScene2()
    {
        $request = new CouponCollectionInfoRequest([]);
        $logic = new CouponCollectionInfo(new AdminLoginUser([]), $request);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('addDataPermissionsFilterConditions');
        $method->setAccessible(true);

        $result = $method->invokeArgs($logic, [new AdminLoginUser(['pid' => 0, 'corp_id' => 10001]), []]);
        $this->assertIsArray($result);
        $this->assertSame(['filter_corp_id' => 10001], $result);
    }

    /**
     * @testdox 整理过滤条件 - 没有传递任何过滤条件
     * @return void
     * @throws ReflectionException
     */
    public function testArrangeFiltersScene1()
    {
        $request = new CouponCollectionInfoRequest([]);
        $logic = new CouponCollectionInfo(new AdminLoginUser([]), $request);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('arrangeFilters');
        $method->setAccessible(true);

        // 预期的结果
        $expect_filters = [];
        // 传递的参数
        $params = [];

        $filters = $method->invokeArgs($logic, [$params]);
        $this->assertIsArray($filters);
        $this->assertSame($expect_filters, $filters);
    }

    /**
     * @testdox 整理过滤条件 - 只传递了运营商ID
     * @return void
     * @throws ReflectionException
     */
    public function testArrangeFiltersScene2()
    {
        $request = new CouponCollectionInfoRequest([]);
        $logic = new CouponCollectionInfo(new AdminLoginUser([]), $request);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('arrangeFilters');
        $method->setAccessible(true);

        // 预期的结果
        $expect_filters = [
            ['ca.corp_id', '=', 10001]
        ];
        // 传递的参数
        $params = [
            'filter_corp_id' => 10001
        ];

        $filters = $method->invokeArgs($logic, [$params]);
        $this->assertIsArray($filters);
        $this->assertSame($expect_filters, $filters);
    }


    /**
     * @testdox 整理过滤条件 - 只传递了用户优惠券ID
     * @return void
     * @throws ReflectionException
     */
    public function testArrangeFiltersScene3()
    {
        $request = new CouponCollectionInfoRequest([]);
        $logic = new CouponCollectionInfo(new AdminLoginUser([]), $request);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('arrangeFilters');
        $method->setAccessible(true);

        // 预期的结果
        $expect_filters = [
            ['cc.id', '=', 1]
        ];
        // 传递的参数
        $params = [
            'id' => 1
        ];

        $filters = $method->invokeArgs($logic, [$params]);
        $this->assertIsArray($filters);
        $this->assertSame($expect_filters, $filters);
    }

    /**
     * @testdox 整理过滤条件 - 传递了所有的过滤参数
     * @return void
     * @throws ReflectionException
     */
    public function testArrangeFiltersScene4()
    {
        $request = new CouponCollectionInfoRequest([]);
        $logic = new CouponCollectionInfo(new AdminLoginUser([]), $request);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('arrangeFilters');
        $method->setAccessible(true);

        // 预期的结果
        $expect_filters = [
            ['ca.corp_id', '=', 10001],
            ['cc.id', '=', 1],
        ];
        // 传递的参数
        $params = [
            'id' => 1,
            'filter_corp_id' => 10001
        ];

        $filters = $method->invokeArgs($logic, [$params]);
        $this->assertIsArray($filters);
        $this->assertSame($expect_filters, $filters);
    }


    /**
     * @testdox 获取用户优惠券数据 - 没有查询到符合条件的数据
     * @return void
     * @throws ReflectionException
     */
    public function testGetInfoScene1()
    {
        $request = new CouponCollectionInfoRequest([]);
        $logic = new CouponCollectionInfo(new AdminLoginUser([]), $request);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('getInfo');
        $method->setAccessible(true);

        // 模拟场景：没有查询出活动信息
        $couponCollectionModel = $this->createStub(CouponCollection::class);
        $couponCollectionModel->method('getInfoData')->willReturn(null);
        bind(CouponCollection::class, $couponCollectionModel);

        $this->expectException(RuntimeException::class);
        $this->expectExceptionCode(RuntimeException::CodeBusinessException);
        $this->expectExceptionMessage("无效优惠券ID");

        $method->invokeArgs($logic, [[]]);
    }

    /**
     * @testdox 获取用户优惠券详情数据 - 查询到了符合条件的数据
     * @return void
     * @throws ReflectionException
     */
    public function testGetInfoScene2()
    {
        $request = new CouponCollectionInfoRequest([]);
        $logic = new CouponCollectionInfo(new AdminLoginUser([]), $request);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('getInfo');
        $method->setAccessible(true);

        // 模拟场景：没有查询出活动信息
        $return = [
            'id' => 1,
            'activity_id' => 82,
            'phone' => NULL,
            'status' => 1,
            'name' => '服务费满10元减5元',
            'par_value' => 50,
            'usage_amount' => 100,
            'order_id' => NULL,
            'create_time' => '2024-09-30 12:02:22',
            'use_time' => '2024-09-30 15:29:47',
            'expire_time' => '2024-10-06 12:01:36',
        ];
        $couponCollectionModel = $this->createStub(CouponCollection::class);
        $couponCollectionModel->method('getInfoData')->willReturn($return);
        bind(CouponCollection::class, $couponCollectionModel);

        $result = $method->invokeArgs($logic, [[['cc.id', '=', 1]]]);
        $this->assertSame($return, $result);
    }

    /**
     * @testdox 补充关联的场站数据 - 验证查询的SQL
     * @return void
     * @throws ReflectionException
     */
    public function testFillRelationStationsScene1()
    {
        $request = new CouponCollectionInfoRequest([]);
        $logic = new CouponCollectionInfo(new AdminLoginUser([]), $request);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('fillRelationStations');
        $method->setAccessible(true);

        $params = [
            'activity_id' => 1,
        ];
        $expected_sql = sprintf(
            "SELECT `s`.`id`,`s`.`name` FROM `coupon_activity_station` `cas` LEFT JOIN `stations` `s` ON `s`.`id`=`cas`.`station_id` WHERE  `cas`.`activity_id` = '%d'",
            $params['activity_id']
        );

        $method->invokeArgs($logic, [$params]);

        $sql = CouponActivityStation::getLastSql();

        $this->assertSame($expected_sql, $sql);
    }


    /**
     * @testdox 补充关联的场站数据 - 验证返回结果
     * @return void
     * @throws ReflectionException
     */
    public function testFillRelationStationIdsScene2()
    {
        $request = new CouponCollectionInfoRequest([]);
        $logic = new CouponCollectionInfo(new AdminLoginUser([]), $request);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('fillRelationStations');
        $method->setAccessible(true);

        $couponActivityStationModel = $this->createStub(CouponActivityStation::class);
        $couponActivityStationModel->method('getActivityRelationStationData')->willReturn([
            ['id' => 1, 'name' => '测试场站1'],
            ['id' => 2, 'name' => '测试场站2']
        ]);
        bind(CouponActivityStation::class, $couponActivityStationModel);

        $params = [
            'id' => 1,
            'activity_id' => 82,
            'phone' => 15914040778,
            'status' => 1,
            'name' => '服务费满10元减5元',
            'par_value' => 50,
            'usage_amount' => 100,
            'order_id' => NULL,
            'create_time' => '2024-09-30 12:02:22',
            'use_time' => '2024-09-30 15:29:47',
            'expire_time' => '2024-10-06 12:01:36',
        ];
        $expected_result = [
            'id' => 1,
            'activity_id' => 82,
            'phone' => 15914040778,
            'status' => 1,
            'name' => '服务费满10元减5元',
            'par_value' => 50,
            'usage_amount' => 100,
            'order_id' => NULL,
            'create_time' => '2024-09-30 12:02:22',
            'use_time' => '2024-09-30 15:29:47',
            'expire_time' => '2024-10-06 12:01:36',
            'stations' => [
                ['id' => 1, 'name' => '测试场站1'],
                ['id' => 2, 'name' => '测试场站2']
            ]
        ];

        $result = $method->invokeArgs($logic, [$params]);

        $this->assertSame($expected_result, $result);
    }
}