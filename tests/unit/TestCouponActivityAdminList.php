<?php

namespace tests\unit;

use app\common\cache\redis\entity\AdminLoginUser;
use app\common\lib\exception\RuntimeException;
use app\common\lib\VerifyData;
use app\common\logic\admin\coupon_activity\ActivityList;
use app\common\logic\admin\coupon_activity\CreateActivity;
use app\common\logic\admin\entity\ActivityListRequest;
use app\common\logic\admin\entity\CreateActivityRequest;
use app\common\model\CouponActivity;
use app\common\model\CouponActivity as CouponActivityModel;
use app\common\model\Stations;
use PHPUnit\Framework\TestCase;
use ReflectionClass;
use ReflectionException;
use Respect\Validation\Validator as v;
use Throwable;
use PHPUnit\Util\TestDox\TextResultPrinter;

/**
 * @testdox 优惠券活动列表
 */
class TestCouponActivityAdminList extends TestCase
{

    /**
     * @testdox 验证参数 - (filter_id)过滤活动ID - 预期不传递这个字段不会抛出异常。
     */
    public function testVerifyParamsFilterIdScene1()
    {
        $params = [];

        try {
            v::input($params, VerifyData::coupon_activity([
                'filter_id',
            ]));

            $this->assertTrue(true, '预期不会抛出异常');
        } catch (Throwable $e) {
            $this->fail('预期不会抛出异常，实际抛出了。');
        }
    }

    /**
     * @testdox 验证参数 - (filter_id)过滤活动ID - 传递非数值类型时，预期验证会不通过。
     */
    public function testVerifyParamsFilterIdScene2()
    {
        $params = [
            'filter_id' => '1'
        ];

        try {
            $re = v::input($params, VerifyData::coupon_activity([
                'filter_id',
            ]));
            var_export($re);

            $this->fail('预期会抛出异常，实际没有抛出了。');
        } catch (Throwable $e) {
            $this->assertSame('活动ID 必须是integer类型', $e->getMessage());
        }
    }

    /**
     * @testdox 验证参数 - (filter_status)过滤活动状态 - 传递数值类型时，预期验证会通过。
     */
    public function testVerifyParamsFilterIdScene3()
    {
        $params = [
            'filter_status' => 1
        ];

        try {
            v::input($params, VerifyData::coupon_activity([
                'filter_status',
            ]));
            $this->assertTrue(true);
        } catch (Throwable $e) {
            $this->fail('预期不会抛出异常，实际抛出了。');
        }
    }

    /**
     * @testdox 验证参数 - (filter_id)过滤活动ID - 传递空字符串时，预期验证会不通过。
     */
    public function testVerifyParamsFilterIdScene4()
    {
        $params = [
            'filter_id' => ''
        ];

        $this->expectExceptionMessage('活动ID 必须有效');

        v::input($params, VerifyData::coupon_activity([
            'filter_id',
        ]));
    }


    /**
     * @testdox 验证参数 - (filter_name)过滤名称 - 没有传递该参数时，期望的结果是不会抛出异常。
     */
    public function testVerifyParamsFilterNameScene1()
    {
        // 参数要求：
        // filter_name: 可选的 字符串类型

        $params = [];

        try {
            v::input($params, VerifyData::coupon_activity([
                'filter_name',
            ]));

            $this->assertTrue(true, '预期不会抛出异常');
        } catch (Throwable $e) {
            $this->fail('预期不会抛出异常，实际抛出了。');
        }
    }

    /**
     * @testdox 验证参数 - (filter_name)过滤名称 - 传递非字符串类型时，期望的结果是会抛出异常，提示必须是字符串类型。
     */
    public function testVerifyParamsFilterNameScene2()
    {
        // 场景：传递非字符串类型时，期望的结果是会抛出异常，提示必须是字符串类型。
        $params['filter_name'] = 926;
        try {
            v::input($params, VerifyData::coupon_activity([
                'filter_name',
            ]));
            $this->fail('预期会抛出异常，实际没有抛出验证异常');
        } catch (Throwable $e) {
            $this->assertSame('活动名称 必须是string类型', $e->getMessage());
        }
    }

    /**
     * @testdox 验证参数 - (filter_name)过滤名称 - 传递了字段，不过字段值是空字符串，期望的结果是不会抛出异常。
     */
    public function testVerifyParamsFilterNameScene3()
    {
        $params['filter_name'] = '';
        try {
            $verifyData = v::input($params, VerifyData::coupon_activity([
                'filter_name',
            ]));
            $this->assertSame($params, $verifyData);
        } catch (Throwable $e) {
            $this->fail('预期不会抛出异常，实际抛出了异常，');
        }
    }

    /**
     * @testdox 验证参数 - (filter_name)过滤名称 - 传递了理想的字符串。
     */
    public function testVerifyParamsFilterNameScene4()
    {
        // 验证合法的名称
        $params['filter_name'] = '新站优惠';
        try {
            $verifyData = v::input($params, VerifyData::coupon_activity([
                'filter_name',
            ]));
            $this->assertSame($params, $verifyData);
        } catch (Throwable $e) {
            $this->fail('验证失败');
        }
    }


    /**
     * @testdox 验证参数 - (filter_status)过滤活动状态 - 预期不传递这个字段不会抛出异常。
     */
    public function testVerifyParamsFilterStatusScene1()
    {
        $params = [];

        try {
            v::input($params, VerifyData::coupon_activity([
                'filter_status',
            ]));

            $this->assertTrue(true, '预期不会抛出异常');
        } catch (Throwable $e) {
            $this->fail('预期不会抛出异常，实际抛出了。');
        }
    }

    /**
     * @testdox 验证参数 - (filter_status)过滤活动状态 - 传递非数值类型时，预期验证会不通过。
     */
    public function testVerifyParamsFilterStatusScene2()
    {
        $params = [
            'filter_status' => '1'
        ];

        try {
            v::input($params, VerifyData::coupon_activity([
                'filter_status',
            ]));

            $this->fail('预期会抛出异常，实际没有抛出了。');
        } catch (Throwable $e) {
            $this->assertSame('活动状态 必须是integer类型', $e->getMessage());
        }
    }

    /**
     * @testdox 验证参数 - (filter_status)过滤活动状态 - 传递数值类型时，预期验证会通过。
     */
    public function testVerifyParamsFilterStatusScene3()
    {
        $params = [
            'filter_status' => 1
        ];

        try {
            v::input($params, VerifyData::coupon_activity([
                'filter_status',
            ]));
            $this->assertTrue(true);
        } catch (Throwable $e) {
            $this->fail('预期不会抛出异常，实际抛出了。');
        }
    }

    /**
     * @testdox 验证参数 - (filter_status)过滤活动状态 - 传递空字符串时，预期验证不会通过。
     */
    public function testVerifyParamsFilterStatusScene4()
    {
        $params = [
            'filter_status' => ''
        ];

        $this->expectExceptionMessage('活动状态 必须有效');

        v::input($params, VerifyData::coupon_activity([
            'filter_status',
        ]));
    }


    /**
     * @testdox 验证参数 - (page)页码 - 不传递时会验证不通过。
     */
    public function testVerifyParamsPageScene1()
    {
        $params = [];

        try {
            v::input($params, VerifyData::coupon_activity([
                'page',
            ]));
            $this->fail();
        } catch (Throwable $e) {
            $this->assertSame('页码 不能为空', $e->getMessage());
        }
    }

    /**
     * @testdox 验证参数 - (page)页码 - 传递非数值时验证不通过。
     */
    public function testVerifyParamsPageScene2()
    {
        $params = [
            'page' => '1'
        ];

        try {
            v::input($params, VerifyData::coupon_activity([
                'page',
            ]));
            $this->fail();
        } catch (Throwable $e) {
            $this->assertSame('页码 必须是integer类型', $e->getMessage());
        }
    }

    /**
     * @testdox 验证参数 - (page)页码 - 传递低于1的数值时会验证不通过。
     */
    public function testVerifyParamsPageScene3()
    {
        $params = [
            'page' => -1
        ];

        try {
            v::input($params, VerifyData::coupon_activity([
                'page',
            ]));
            $this->fail();
        } catch (Throwable $e) {
            $this->assertSame('页码 必须大于或等于 1', $e->getMessage());
        }
    }

    /**
     * @testdox 验证参数 - (page)页码 - 传递符合预期的数值，验证通过。
     */
    public function testVerifyParamsPageScene4()
    {
        $params = [
            'page' => 1
        ];

        try {
            $verify_params = v::input($params, VerifyData::coupon_activity([
                'page',
            ]));

            $this->assertSame($params, $verify_params);
        } catch (Throwable $e) {
            $this->fail('不应该会抛出异常的');
        }
    }


    /**
     * @testdox 验证参数 - (limit)显示条数 - 不传递时会验证不通过。
     */
    public function testVerifyParamsLimitScene1()
    {
        $params = [];

        try {
            v::input($params, VerifyData::coupon_activity([
                'limit',
            ]));
            $this->fail();
        } catch (Throwable $e) {
            $this->assertSame('显示条数 不能为空', $e->getMessage());
        }
    }

    /**
     * @testdox 验证参数 - (limit)显示条数 - 传递非数值时验证不通过。
     */
    public function testVerifyParamsLimitScene2()
    {
        $params = [
            'limit' => '1'
        ];

        try {
            v::input($params, VerifyData::coupon_activity([
                'limit',
            ]));
            $this->fail();
        } catch (Throwable $e) {
            $this->assertSame('显示条数 必须是integer类型', $e->getMessage());
        }
    }

    /**
     * @testdox 验证参数 - (limit)显示条数 - 传递低于1的数值时会验证不通过。
     */
    public function testVerifyParamsLimitScene3()
    {
        $params = [
            'limit' => -1
        ];

        try {
            v::input($params, VerifyData::coupon_activity([
                'limit',
            ]));
            $this->fail();
        } catch (Throwable $e) {
            $this->assertSame('显示条数 必须大于或等于 1', $e->getMessage());
        }
    }

    /**
     * @testdox 验证参数 - (limit)显示条数 - 传递符合预期的数值，验证通过。
     */
    public function testVerifyParamsLimitScene4()
    {
        $params = [
            'limit' => 10
        ];

        try {
            $verify_params = v::input($params, VerifyData::coupon_activity([
                'limit',
            ]));

            $this->assertSame($params, $verify_params);
        } catch (Throwable $e) {
            $this->fail('不应该会抛出异常的');
        }
    }


    /**
     * @testdox 验证参数 - (sort_field)排序字段 - 不传递时会验证不通过。
     */
    public function testVerifyParamsSortFieldScene1()
    {
        $params = [];

        try {
            v::input($params, VerifyData::coupon_activity([
                'sort_field',
            ]));
            $this->fail();
        } catch (Throwable $e) {
            $this->assertSame('排序字段 不能为空', $e->getMessage());
        }
    }

    /**
     * @testdox 验证参数 - (sort_field)排序字段 - 传递非数值时验证不通过。
     */
    public function testVerifyParamsSortFieldScene2()
    {
        $params = [
            'sort_field' => '1'
        ];

        try {
            v::input($params, VerifyData::coupon_activity([
                'sort_field',
            ]));
            $this->fail();
        } catch (Throwable $e) {
            $this->assertSame('排序字段 必须是integer类型', $e->getMessage());
        }
    }

    /**
     * @testdox 验证参数 - (sort_field)排序字段 - 传递低于1的数值时会验证不通过。
     */
    public function testVerifyParamsSortFieldScene3()
    {
        $params = [
            'sort_field' => -1
        ];

        try {
            v::input($params, VerifyData::coupon_activity([
                'sort_field',
            ]));
            $this->fail();
        } catch (Throwable $e) {
            $this->assertSame('排序字段 必须大于或等于 1', $e->getMessage());
        }
    }

    /**
     * @testdox 验证参数 - (sort_field)排序字段 - 传递符合预期的数值，验证通过。
     */
    public function testVerifyParamsSortFieldScene4()
    {
        $params = [
            'sort_field' => 1
        ];

        try {
            $verify_params = v::input($params, VerifyData::coupon_activity([
                'sort_field',
            ]));

            $this->assertSame($params, $verify_params);
        } catch (Throwable $e) {
            $this->fail('不应该会抛出异常的');
        }
    }


    /**
     * @testdox 验证参数 - (sort_type)排序类型 - 不传递时，会验证不通过。
     */
    public function testVerifyParamsSortTypeScene1()
    {
        $params = [];

        try {
            v::input($params, VerifyData::coupon_activity([
                'sort_type',
            ]));
            $this->fail();
        } catch (Throwable $e) {
            $this->assertSame('排序类型 不能为空', $e->getMessage());
        }
    }

    /**
     * @testdox 验证参数 - (sort_type)排序类型 - 传递非数值时，验证不通过。
     */
    public function testVerifyParamsSortTypeScene2()
    {
        $params = [
            'sort_type' => '1'
        ];

        try {
            v::input($params, VerifyData::coupon_activity([
                'sort_type',
            ]));
            $this->fail();
        } catch (Throwable $e) {
            $this->assertSame('排序类型 必须是integer类型', $e->getMessage());
        }
    }

    /**
     * @testdox 验证参数 - (sort_type)排序类型 - 传递的数值不是1或2时，验证会不通过。
     */
    public function testVerifyParamsSortTypeScene3()
    {
        $params = [
            'sort_type' => -1
        ];

        try {
            v::input($params, VerifyData::coupon_activity([
                'sort_type',
            ]));
            $this->fail();
        } catch (Throwable $e) {
            $this->assertSame('排序类型 必须在 `{ 1, 2 }` 中', $e->getMessage());
        }
    }

    /**
     * @testdox 验证参数 - (sort_type)排序类型 - 传递符合预期的数值，验证通过。
     */
    public function testVerifyParamsSortTypeScene4()
    {
        $params = [
            'sort_type' => 1
        ];

        try {
            $verify_params = v::input($params, VerifyData::coupon_activity([
                'sort_type',
            ]));

            $this->assertSame($params, $verify_params);
        } catch (Throwable $e) {
            $this->fail('不应该会抛出异常的');
        }
    }

    /**
     * @testdox 验证参数 - 所有参数
     */
    public function testVerifyParamsAll()
    {
        $params = [
            'filter_id' => 1,
            'filter_name' => '0926',
            'filter_status' => 1,
            'page' => 1,
            'limit' => 10,
            'sort_field' => 1,
            'sort_type' => 1
        ];

        try {
            $verify_params = v::input($params, VerifyData::coupon_activity([
                'filter_id', 'filter_name', 'filter_status',
                'page', 'limit',
                'sort_field', 'sort_type'
            ]));

            $this->assertSame($params, $verify_params);
        } catch (Throwable $e) {
            $this->fail('不应该会抛出异常的');
        }
    }

    /**
     * @testdox 创建活动列表请求对象
     */
    public function testCreateActivityListRequest()
    {
        $params = [
            'filter_id' => 1,
            'filter_name' => '0926',
            'filter_status' => 1,
            'page' => 1,
            'limit' => 10,
            'sort_field' => 1,
            'sort_type' => 1
        ];
        $request = new ActivityListRequest($params);
        $this->assertSame($params, $request->toArray());

        $params = [
            'filter_id' => 1,
            'filter_status' => 1,
            'page' => 1,
            'limit' => 10,
            'sort_field' => 1,
            'sort_type' => 1
        ];
        $request = new ActivityListRequest($params);
        $this->assertSame($params, $request->toArray());
    }

    /**
     * @testdox 获取排序类型
     */
    public function testGetSortType()
    {
        $request = new ActivityListRequest([]);
        $logic = new ActivityList(new AdminLoginUser([]), $request);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('getSortType');
        $method->setAccessible(true);

        $result = $method->invokeArgs($logic, [ActivityList::SortTypeDesc]);
        $this->assertSame('DESC', $result);
        $result = $method->invokeArgs($logic, [ActivityList::SortTypeAsc]);
        $this->assertSame('ASC', $result);
    }

    /**
     * @testdox 获取排序字段名
     */
    public function testGetSortField()
    {
        $request = new ActivityListRequest([]);
        $logic = new ActivityList(new AdminLoginUser([]), $request);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('getSortField');
        $method->setAccessible(true);

        foreach (ActivityList::SortFieldsMap as $value => $field) {
            $result = $method->invokeArgs($logic, [$value]);
            $this->assertIsString($result);
            $this->assertSame($field, $result);
        }
    }


    /**
     * @testdox 转换排序字段
     */
    public function testTransformSortParams()
    {
        $request = new ActivityListRequest([]);
        $logic = new ActivityList(new AdminLoginUser([]), $request);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('transformSortParams');
        $method->setAccessible(true);

        foreach (ActivityList::SortFieldsMap as $value => $field) {
            foreach (ActivityList::SortTypeMap as $type_value => $type_name) {
                $params = [
                    'sort_field' => $value,
                    'sort_type' => $type_value
                ];
                $result = $method->invokeArgs($logic, [$params]);
                // 返回值是数组
                $this->assertIsArray($result);
                // 有这两个Key
                $this->assertArrayHasKey('sort_field', $result);
                $this->assertArrayHasKey('sort_type', $result);
                // 都是字符床类型
                $this->assertIsString($result['sort_field']);
                $this->assertIsString($result['sort_type']);
                // 与预期的一致
                $this->assertSame($field, $result['sort_field']);
                $this->assertSame($type_name, $result['sort_type']);
            }
        }
    }

    /**
     * @testdox 增加数据权限过滤条件 - 超级管理员身份 或 其他非运营商账号身份
     */
    public function testAddDataPermissionsFilterConditionsScene1()
    {
        $request = new ActivityListRequest([]);
        $logic = new ActivityList(new AdminLoginUser([]), $request);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('addDataPermissionsFilterConditions');
        $method->setAccessible(true);

        $result = $method->invokeArgs($logic, [new AdminLoginUser(['pid' => 0, 'corp_id' => 0]), []]);
        $this->assertIsArray($result);
        $this->assertSame([], $result);
    }

    /**
     * @testdox 增加数据权限过滤条件 - 运营商主账号身份 或 运营商子账号身份
     */
    public function testAddDataPermissionsFilterConditionsScene2()
    {
        $request = new ActivityListRequest([]);
        $logic = new ActivityList(new AdminLoginUser([]), $request);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('addDataPermissionsFilterConditions');
        $method->setAccessible(true);

        $result = $method->invokeArgs($logic, [new AdminLoginUser(['pid' => 0, 'corp_id' => 10001]), []]);
        $this->assertIsArray($result);
        $this->assertSame(['filter_corp_id' => 10001], $result);
    }

    /**
     * @testdox 整理过滤条件 - 没有传递任何过滤条件
     * @return void
     * @throws ReflectionException
     */
    public function testArrangeFiltersScene1()
    {
        $request = new ActivityListRequest([]);
        $logic = new ActivityList(new AdminLoginUser([]), $request);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('arrangeFilters');
        $method->setAccessible(true);

        // 预期的结果
        $expect_filters = [
            ['ca.is_del', '=', CouponActivity::IsDelNot]
        ];
        // 传递的参数
        $params = [];

        $filters = $method->invokeArgs($logic, [$params]);
        $this->assertIsArray($filters);
        $this->assertSame($expect_filters, $filters);
    }

    /**
     * @testdox 整理过滤条件 - 只传递活动ID作为过滤条件
     * @return void
     * @throws ReflectionException
     */
    public function testArrangeFiltersScene2()
    {
        $request = new ActivityListRequest([]);
        $logic = new ActivityList(new AdminLoginUser([]), $request);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('arrangeFilters');
        $method->setAccessible(true);

        // 预期的结果
        $expect_filters = [
            ['ca.is_del', '=', CouponActivity::IsDelNot],
            ['ca.id', '=', 1]
        ];
        // 传递的参数
        $params = [
            'filter_id' => 1
        ];

        $filters = $method->invokeArgs($logic, [$params]);
        $this->assertIsArray($filters);
        $this->assertSame($expect_filters, $filters);
    }

    /**
     * @testdox 整理过滤条件 - 只传递活动名称作为过滤条件
     * @return void
     * @throws ReflectionException
     */
    public function testArrangeFiltersScene3()
    {
        $request = new ActivityListRequest([]);
        $logic = new ActivityList(new AdminLoginUser([]), $request);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('arrangeFilters');
        $method->setAccessible(true);

        // 预期的结果
        $expect_filters = [
            ['ca.is_del', '=', CouponActivity::IsDelNot],
            ['ca.name', 'like', "%0926%"]
        ];
        // 传递的参数
        $params = [
            'filter_name' => "0926"
        ];

        $filters = $method->invokeArgs($logic, [$params]);
        $this->assertIsArray($filters);
        $this->assertSame($expect_filters, $filters);
    }

    /**
     * @testdox 整理过滤条件 - 只传递活动状态作为过滤条件
     * @return void
     * @throws ReflectionException
     */
    public function testArrangeFiltersScene4()
    {
        $request = new ActivityListRequest([]);
        $logic = new ActivityList(new AdminLoginUser([]), $request);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('arrangeFilters');
        $method->setAccessible(true);

        // 预期的结果
        $expect_filters = [
            ['ca.is_del', '=', CouponActivity::IsDelNot],
            ['ca.status', '=', CouponActivity::StatusCreating]
        ];
        // 传递的参数
        $params = [
            'filter_status' => CouponActivity::StatusCreating
        ];

        $filters = $method->invokeArgs($logic, [$params]);
        $this->assertIsArray($filters);
        $this->assertSame($expect_filters, $filters);
    }

    /**
     * @testdox 整理过滤条件 - 只传递运营商ID作为过滤条件
     * @return void
     * @throws ReflectionException
     */
    public function testArrangeFiltersScene5()
    {
        $request = new ActivityListRequest([]);
        $logic = new ActivityList(new AdminLoginUser([]), $request);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('arrangeFilters');
        $method->setAccessible(true);

        // 预期的结果
        $expect_filters = [
            ['ca.is_del', '=', CouponActivity::IsDelNot],
            ['ca.corp_id', '=', 10001]
        ];
        // 传递的参数
        $params = [
            'filter_corp_id' => 10001
        ];

        $filters = $method->invokeArgs($logic, [$params]);
        $this->assertIsArray($filters);
        $this->assertSame($expect_filters, $filters);
    }

    /**
     * @testdox 整理过滤条件 - 传递所有过滤条件
     * @return void
     * @throws ReflectionException
     */
    public function testArrangeFiltersScene6()
    {
        $request = new ActivityListRequest([]);
        $logic = new ActivityList(new AdminLoginUser([]), $request);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('arrangeFilters');
        $method->setAccessible(true);

        // 预期的结果
        $expect_filters = [
            ['ca.is_del', '=', CouponActivity::IsDelNot],
            ['ca.id', '=', 1],
            ['ca.name', 'like', '%0926%'],
            ['ca.status', '=', CouponActivity::StatusReview],
            ['ca.corp_id', '=', 10001]
        ];
        // 传递的参数
        $params = [
            'filter_id' => 1,
            'filter_name' => '0926',
            'filter_status' => CouponActivity::StatusReview,
            'filter_corp_id' => 10001
        ];

        $filters = $method->invokeArgs($logic, [$params]);
        $this->assertIsArray($filters);
        $this->assertSame($expect_filters, $filters);
    }

    /**
     * @testdox 验证查询列表数据的SQL语句 - 含有所有过滤条件
     * @return void
     */
    public function testGetListDataSql()
    {
        $filters = [
            ['ca.is_del', '=', CouponActivity::IsDelNot],
            ['ca.id', '=', 1],
            ['ca.name', 'like', '%0926%'],
            ['ca.status', '=', CouponActivity::StatusReview],
            ['ca.corp_id', '=', 10001]
        ];
        $result = (new CouponActivityModel())->getListData(
            $filters, 1, 10,
            'ca.id', 'DESC', true
        );
        $expect_sql = "( SELECT `ca`.`id`,`ca`.`name`,`ca`.`status`,`ca`.`grant_max_count`,`ca`.`grant_num`,`ca`.`qcode_url`,`ca`.`start_time`,`ca`.`end_time`,`ca`.`create_time`,`ca`.`update_time` FROM `coupon_activity` `ca` LEFT JOIN `admin_users` `au` ON `au`.`id`=`ca`.`user_id` WHERE  `ca`.`is_del` = '0'  AND `ca`.`id` = '1'  AND `ca`.`name` LIKE '%0926%'  AND `ca`.`status` = '2'  AND `ca`.`corp_id` = '10001' ORDER BY `ca`.`id` DESC )";
        $this->assertSame($expect_sql, $result['sql']);
    }
}