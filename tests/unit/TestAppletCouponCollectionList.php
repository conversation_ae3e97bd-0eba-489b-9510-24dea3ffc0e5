<?php

namespace tests\unit;

use app\common\cache\redis\entity\AppletLoginUser;
use app\common\lib\VerifyData;
use app\common\logic\applet\coupon_collection\CouponCollectionList;
use app\common\logic\applet\entity\CouponCollectionListRequest;
use app\common\model\CouponActivityStation;
use app\common\model\CouponCollection;
use PHPUnit\Framework\TestCase;
use ReflectionException;
use Respect\Validation\Validator as v;
use Throwable;

/**
 * @testdox 小程序用户优惠券列表
 */
class TestAppletCouponCollectionList extends TestCase
{

    /**
     * @testdox 验证参数 - (filter_type)过滤类型 - 不传递参数，预期不会验证不通过。
     */
    public function testVerifyParamsFilterTypeScene1()
    {
        $params = [];


        $verify = v::input($params, VerifyData::coupon_collection([
            'filter_type',
        ]));

        $this->assertSame(['filter_type' => null], $verify);
    }

    /**
     * @testdox 验证参数 - (filter_type)过滤类型 - 传递非数值类型时，预期验证会不通过。
     */
    public function testVerifyParamsFilterTypeScene2()
    {
        $params = [
            'filter_type' => '1'
        ];

        $this->expectExceptionMessage('优惠券类型 必须是integer类型');

        v::input($params, VerifyData::coupon_collection([
            'filter_type',
        ]));
    }

    /**
     * @testdox 验证参数 - (filter_type)过滤类型 - 传递数值类型时，预期验证会通过。
     */
    public function testVerifyParamsFilterTypeScene3()
    {
        $params = [
            'filter_type' => 1
        ];

        $verify = v::input($params, VerifyData::coupon_collection([
            'filter_type',
        ]));

        $this->assertSame($params, $verify);
    }

    /**
     * @testdox 验证参数 - (filter_type)过滤类型 - 传递空字符串时，预期验证会不通过。
     */
    public function testVerifyParamsFilterTypeScene4()
    {
        $params = [
            'filter_type' => ''
        ];

        $this->expectExceptionMessage('优惠券类型 必须有效');

        v::input($params, VerifyData::coupon_collection([
            'filter_type',
        ]));
    }

    /**
     * @testdox 验证参数 - (filter_type)过滤类型 - 传递非1、2、3范围内的值时，会验证不通过。
     */
    public function testVerifyParamsFilterTypeScene5()
    {
        $params = [
            'filter_type' => 4
        ];

        $this->expectExceptionMessage('优惠券类型 必须在 `{ 1, 2, 3 }` 中');

        v::input($params, VerifyData::coupon_collection([
            'filter_type',
        ]));
    }


    /**
     * @testdox 验证参数 - (page)页码 - 不传递时会验证不通过。
     */
    public function testVerifyParamsPageScene1()
    {
        $params = [];

        try {
            v::input($params, VerifyData::coupon_collection([
                'page',
            ]));
            $this->fail();
        } catch (Throwable $e) {
            $this->assertSame('页码 不能为空', $e->getMessage());
        }
    }

    /**
     * @testdox 验证参数 - (page)页码 - 传递非数值时验证不通过。
     */
    public function testVerifyParamsPageScene2()
    {
        $params = [
            'page' => '1'
        ];

        try {
            v::input($params, VerifyData::coupon_collection([
                'page',
            ]));
            $this->fail();
        } catch (Throwable $e) {
            $this->assertSame('页码 必须是integer类型', $e->getMessage());
        }
    }

    /**
     * @testdox 验证参数 - (page)页码 - 传递低于1的数值时会验证不通过。
     */
    public function testVerifyParamsPageScene3()
    {
        $params = [
            'page' => -1
        ];

        try {
            v::input($params, VerifyData::coupon_collection([
                'page',
            ]));
            $this->fail();
        } catch (Throwable $e) {
            $this->assertSame('页码 必须大于或等于 1', $e->getMessage());
        }
    }

    /**
     * @testdox 验证参数 - (page)页码 - 传递符合预期的数值，验证通过。
     */
    public function testVerifyParamsPageScene4()
    {
        $params = [
            'page' => 1
        ];

        try {
            $verify_params = v::input($params, VerifyData::coupon_collection([
                'page',
            ]));

            $this->assertSame($params, $verify_params);
        } catch (Throwable $e) {
            $this->fail('不应该会抛出异常的');
        }
    }


    /**
     * @testdox 验证参数 - (limit)显示条数 - 不传递时会验证不通过。
     */
    public function testVerifyParamsLimitScene1()
    {
        $params = [];

        try {
            v::input($params, VerifyData::coupon_collection([
                'limit',
            ]));
            $this->fail();
        } catch (Throwable $e) {
            $this->assertSame('显示条数 不能为空', $e->getMessage());
        }
    }

    /**
     * @testdox 验证参数 - (limit)显示条数 - 传递非数值时验证不通过。
     */
    public function testVerifyParamsLimitScene2()
    {
        $params = [
            'limit' => '1'
        ];

        try {
            v::input($params, VerifyData::coupon_collection([
                'limit',
            ]));
            $this->fail();
        } catch (Throwable $e) {
            $this->assertSame('显示条数 必须是integer类型', $e->getMessage());
        }
    }

    /**
     * @testdox 验证参数 - (limit)显示条数 - 传递低于1的数值时会验证不通过。
     */
    public function testVerifyParamsLimitScene3()
    {
        $params = [
            'limit' => -1
        ];

        try {
            v::input($params, VerifyData::coupon_collection([
                'limit',
            ]));
            $this->fail();
        } catch (Throwable $e) {
            $this->assertSame('显示条数 必须大于或等于 1', $e->getMessage());
        }
    }

    /**
     * @testdox 验证参数 - (limit)显示条数 - 传递符合预期的数值，验证通过。
     */
    public function testVerifyParamsLimitScene4()
    {
        $params = [
            'limit' => 10
        ];

        try {
            $verify_params = v::input($params, VerifyData::coupon_collection([
                'limit',
            ]));

            $this->assertSame($params, $verify_params);
        } catch (Throwable $e) {
            $this->fail('不应该会抛出异常的');
        }
    }


    /**
     * @testdox 验证参数 - 所有参数
     */
    public function testVerifyParamsAll()
    {
        $params = [
            'filter_type' => 1,
            'page' => 1,
            'limit' => 10,
        ];

        $verify_params = v::input($params, VerifyData::coupon_collection([
            'filter_type', 'page', 'limit',
        ]));

        $this->assertSame($params, $verify_params);
    }

    /**
     * @testdox 创建活动列表请求对象
     */
    public function testCreateCouponCollectionListRequest()
    {
        $params = [
            'filter_type' => CouponCollectionListRequest::FilterTypeNotUsed,
            'page' => 1,
            'limit' => 10,
        ];
        $request = new CouponCollectionListRequest($params);
        $this->assertSame($params, $request->toArray());

        $params = [
            'page' => 1,
            'limit' => 10,
        ];
        $request = new CouponCollectionListRequest($params);
        $this->assertSame($params, $request->toArray());
    }

    /**
     * @testdox 增加数据权限过滤条件
     */
    public function testAddDataPermissionsFilterConditionsScene1()
    {
        $request = new CouponCollectionListRequest([]);
        $loginUser = new AppletLoginUser(['id' => 1000000001]);
        $logic = new CouponCollectionList($loginUser, $request);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('addDataPermissionsFilterConditions');
        $method->setAccessible(true);

        // 预期的结果
        $expected_params = [
            'user_id' => $loginUser->id
        ];

        $result = $method->invokeArgs($logic, [$loginUser, []]);
        $this->assertIsArray($result);
        $this->assertSame($expected_params, $result);
    }

    /**
     * @testdox 整理过滤条件 - 没有传递任何过滤条件
     * @return void
     * @throws ReflectionException
     */
    public function testArrangeFiltersScene1()
    {
        $request = new CouponCollectionListRequest([]);
        $loginUser = new AppletLoginUser(['id' => 1000000001]);
        $logic = new CouponCollectionList($loginUser, $request);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('arrangeFilters');
        $method->setAccessible(true);

        // 预期的结果
        $expect_filters = [];
        // 传递的参数
        $params = [];

        $filters = $method->invokeArgs($logic, [$params]);
        $this->assertIsArray($filters);
        $this->assertSame($expect_filters, $filters);
    }

    /**
     * @testdox 整理过滤条件 - 只传递用户ID作为过滤条件
     * @return void
     * @throws ReflectionException
     */
    public function testArrangeFiltersScene2()
    {
        $request = new CouponCollectionListRequest([]);
        $loginUser = new AppletLoginUser(['id' => 1000000001]);
        $logic = new CouponCollectionList($loginUser, $request);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('arrangeFilters');
        $method->setAccessible(true);

        // 预期的结果
        $expect_filters = [
            ['cc.user_id', '=', $loginUser->id]
        ];
        // 传递的参数
        $params = [
            'user_id' => $loginUser->id
        ];

        $filters = $method->invokeArgs($logic, [$params]);
        $this->assertIsArray($filters);
        $this->assertSame($expect_filters, $filters);
    }

    /**
     * @testdox 整理过滤条件 - 只传递活动类型作为过滤条件 - 未使用的优惠券
     * @return void
     * @throws ReflectionException
     */
    public function testArrangeFiltersScene3()
    {
        $request = new CouponCollectionListRequest([]);
        $loginUser = new AppletLoginUser(['id' => 1000000001]);
        $logic = new CouponCollectionList($loginUser, $request);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('arrangeFilters');
        $method->setAccessible(true);

        // 预期的结果
        $expect_filters = [
            ['cc.status', '=', CouponCollection::StatusNotUsed],
            ['cc.expire_time', '<', date('Y-m-d H:i:s')]
        ];
        // 传递的参数
        $params = [
            'filter_type' => CouponCollectionListRequest::FilterTypeNotUsed
        ];

        $filters = $method->invokeArgs($logic, [$params]);
        $this->assertIsArray($filters);
        $this->assertSame($expect_filters, $filters);
    }

    /**
     * @testdox 整理过滤条件 - 只传递活动类型作为过滤条件 - 已使用的优惠券
     * @return void
     * @throws ReflectionException
     */
    public function testArrangeFiltersScene4()
    {
        $request = new CouponCollectionListRequest([]);
        $loginUser = new AppletLoginUser(['id' => 1000000001]);
        $logic = new CouponCollectionList($loginUser, $request);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('arrangeFilters');
        $method->setAccessible(true);

        // 预期的结果
        $expect_filters = [
            ['cc.status', '=', CouponCollection::StatusUsed]
        ];
        // 传递的参数
        $params = [
            'filter_type' => CouponCollectionListRequest::FilterTypeUsed
        ];

        $filters = $method->invokeArgs($logic, [$params]);
        $this->assertIsArray($filters);
        $this->assertSame($expect_filters, $filters);
    }

    /**
     * @testdox 整理过滤条件 - 只传递活动类型作为过滤条件 - 已过期的优惠券
     * @return void
     * @throws ReflectionException
     */
    public function testArrangeFiltersScene5()
    {
        $request = new CouponCollectionListRequest([]);
        $loginUser = new AppletLoginUser(['id' => 1000000001]);
        $logic = new CouponCollectionList($loginUser, $request);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('arrangeFilters');
        $method->setAccessible(true);

        // 预期的结果
        $expect_filters = [
            ['cc.expire_time', '<', date('Y-m-d H:i:s')]
        ];
        // 传递的参数
        $params = [
            'filter_type' => CouponCollectionListRequest::FilterTypeExpire
        ];

        $filters = $method->invokeArgs($logic, [$params]);
        $this->assertIsArray($filters);
        $this->assertSame($expect_filters, $filters);
    }

    /**
     * @testdox 整理过滤条件 - 传递所有过滤条件
     * @return void
     * @throws ReflectionException
     */
    public function testArrangeFiltersScene6()
    {
        $request = new CouponCollectionListRequest([]);
        $loginUser = new AppletLoginUser(['id' => 1000000001]);
        $logic = new CouponCollectionList($loginUser, $request);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('arrangeFilters');
        $method->setAccessible(true);

        // 预期的结果
        $expect_filters = [
            ['cc.user_id', '=', $loginUser->id],
            ['cc.status', '=', CouponCollection::StatusUsed],
        ];
        // 传递的参数
        $params = [
            'user_id' => $loginUser->id,
            'filter_type' => CouponCollectionListRequest::FilterTypeUsed,
        ];

        $filters = $method->invokeArgs($logic, [$params]);
        $this->assertIsArray($filters);
        $this->assertSame($expect_filters, $filters);
    }

    /**
     * @testdox 获取本次查询的列表中的活动ID
     * @return void
     * @throws ReflectionException
     */
    public function testGetListActivityIds()
    {
        $request = new CouponCollectionListRequest([]);
        $loginUser = new AppletLoginUser(['id' => 1000000001]);
        $logic = new CouponCollectionList($loginUser, $request);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('getListActivityIds');
        $method->setAccessible(true);

        // 预期的结果
        $expect_result = [
            1, 5, 3
        ];
        // 传递的参数
        $params = [
            'data' => [
                [
                    'activity_id' => 1,
                ],
                [
                    'activity_id' => 5,
                ],
                [
                    'activity_id' => 3,
                ],
                [
                    'activity_id' => 1,
                ],
                [
                    'activity_id' => 3,
                ]
            ]
        ];

        $result = $method->invokeArgs($logic, [$params]);
        $this->assertIsArray($result);
        $this->assertSame($expect_result, $result);
    }

    /**
     * @testdox 查询关联的场站数据 - 如果传递空的活动ID集合
     * @return void
     * @throws ReflectionException
     */
    public function testQueryRelationStationDataScene1()
    {
        $request = new CouponCollectionListRequest([]);
        $loginUser = new AppletLoginUser(['id' => 1000000001]);
        $logic = new CouponCollectionList($loginUser, $request);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('queryRelationStationData');
        $method->setAccessible(true);

        // 预期的结果
        $expect_result = [];
        // 传递的参数
        $params = [];

        $result = $method->invokeArgs($logic, [$params]);
        $this->assertIsArray($result);
        $this->assertSame($expect_result, $result);
    }

    /**
     * @testdox 查询关联的场站数据 - 如果传递非空的活动ID集合
     * @return void
     * @throws ReflectionException
     */
    public function testQueryRelationStationDataScene2()
    {
        $request = new CouponCollectionListRequest([]);
        $loginUser = new AppletLoginUser(['id' => 1000000001]);
        $logic = new CouponCollectionList($loginUser, $request);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('queryRelationStationData');
        $method->setAccessible(true);

        // 创建存根来替换真实的数据库查询
        $couponActivityStationModel = $this->createStub(CouponActivityStation::class);
        $couponActivityStationModel->method('batchGetActivityRelationStationData')->willReturn([
            ['activity_id' => 1, 'id' => 1, 'name' => '场站1'],
            ['activity_id' => 1, 'id' => 2, 'name' => '场站2'],
            ['activity_id' => 2, 'id' => 2, 'name' => '场站2'],
            ['activity_id' => 3, 'id' => 3, 'name' => '场站3'],
            ['activity_id' => 3, 'id' => 4, 'name' => '场站4'],
        ]);
        bind(CouponActivityStation::class, $couponActivityStationModel);

        // 预期的结果
        $expect_result = [
            1 => [
                ['id' => 1, 'name' => '场站1'],
                ['id' => 2, 'name' => '场站2'],
            ],
            2 => [
                ['id' => 2, 'name' => '场站2'],
            ],
            3 => [
                ['id' => 3, 'name' => '场站3'],
                ['id' => 4, 'name' => '场站4'],
            ],
        ];
        // 传递的参数
        $params = [1, 2, 3];

        $result = $method->invokeArgs($logic, [$params]);
        $this->assertIsArray($result);
        $this->assertSame($expect_result, $result);
    }

    /**
     * @testdox 查询关联的场站数据 - 验证查询的SQL语句
     * @return void
     * @throws ReflectionException
     */
    public function testQueryRelationStationDataScene3()
    {
        $request = new CouponCollectionListRequest([]);
        $loginUser = new AppletLoginUser(['id' => 1000000001]);
        $logic = new CouponCollectionList($loginUser, $request);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('queryRelationStationData');
        $method->setAccessible(true);

        $couponActivityStationModel = new CouponActivityStation();
        bind(CouponActivityStation::class, $couponActivityStationModel);

        // 传递的参数
        $params = [1, 2, 3];

        // 执行方法
        $method->invokeArgs($logic, [$params]);

        foreach ($params as &$param) {
            $param = sprintf("'%d'", $param);
        }
        // 预期的SQL
        $expect_sql = sprintf(
            "SELECT `cas`.`activity_id`,`s`.`id`,`s`.`name` FROM `coupon_activity_station` `cas` LEFT JOIN `stations` `s` ON `s`.`id`=`cas`.`station_id` WHERE  `cas`.`activity_id` IN (%s)",
            implode(",", $params)
        );

        $sql = CouponActivityStation::getLastSql();
        $this->assertSame($expect_sql, $sql);
    }

    /**
     * @testdox 填充场站数据
     * @return void
     * @throws ReflectionException
     */
    public function testFillStationData()
    {
        $request = new CouponCollectionListRequest([]);
        $loginUser = new AppletLoginUser(['id' => 1000000001]);
        $logic = new CouponCollectionList($loginUser, $request);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('fillStationData');
        $method->setAccessible(true);

        // 传递的参数
        $params = [
            'list' => [
                'data' => [
                    [
                        'id' => 1,
                        'name' => '',
                        'status' => CouponCollection::StatusNotUsed,
                        'expire_time' => date('Y-m-d H:i:s', strtotime('+1 day')),
                        'par_value' => 30,
                        'usage_amount' => 60,
                        'activity_id' => 1
                    ],
                    [
                        'id' => 2,
                        'name' => '',
                        'status' => CouponCollection::StatusNotUsed,
                        'expire_time' => date('Y-m-d H:i:s', strtotime('+1 day')),
                        'par_value' => 30,
                        'usage_amount' => 60,
                        'activity_id' => 2
                    ]
                ]
            ],
            'stationData' => [
                1 => [['id' => 1, 'name' => '场站1']],
                2 => [['id' => 2, 'name' => '场站2']]
            ],
        ];

        // 执行方法
        $result = $method->invokeArgs($logic, [$params['list'], $params['stationData']]);

        $expected_result = [
            'data' => [
                [
                    'id' => 1,
                    'name' => '',
                    'status' => CouponCollection::StatusNotUsed,
                    'expire_time' => date('Y-m-d H:i:s', strtotime('+1 day')),
                    'par_value' => 30,
                    'usage_amount' => 60,
                    'stations' => [['id' => 1, 'name' => '场站1']]
                ],
                [
                    'id' => 2,
                    'name' => '',
                    'status' => CouponCollection::StatusNotUsed,
                    'expire_time' => date('Y-m-d H:i:s', strtotime('+1 day')),
                    'par_value' => 30,
                    'usage_amount' => 60,
                    'stations' => [['id' => 2, 'name' => '场站2']]
                ]
            ]
        ];

        $this->assertSame($expected_result, $result);
    }

    /**
     * @testdox 验证查询列表数据的SQL语句 - 包含所有过滤条件
     * @return void
     * @throws ReflectionException
     */
    public function testGetListDataScene1()
    {
        $request = new CouponCollectionListRequest([]);
        $loginUser = new AppletLoginUser(['id' => 1000000001]);
        $logic = new CouponCollectionList($loginUser, $request);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('getListData');
        $method->setAccessible(true);

        $params = [
            'filters' => [
                ['cc.user_id', '=', $loginUser->id],
                ['cc.status', '=', CouponCollection::StatusUsed],
                ['cc.expire_time', '<', date('Y-m-d H:i:s')]
            ],
            'page' => 2,
            'limit' => 102
        ];

        $method->invokeArgs($logic, [$params['filters'], $params['page'], $params['limit']]);

        $sql = CouponCollection::getLastSql();

        $expected_sql = sprintf(
            "SELECT COUNT(*) AS think_count FROM `coupon_collection` `cc` LEFT JOIN `coupon_pattern` `cp` ON `cp`.`id`=`cc`.`pattern_id` WHERE  `cc`.`user_id` = '%d'  AND `cc`.`status` = '%d'  AND `cc`.`expire_time` < '%s'",
            $params['filters'][0][2],
            $params['filters'][1][2],
            $params['filters'][2][2],
        );

        $this->assertSame($expected_sql, $sql);
    }
}