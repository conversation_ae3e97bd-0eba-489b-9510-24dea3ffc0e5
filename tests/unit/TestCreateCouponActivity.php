<?php

namespace tests\unit;

use app\common\cache\redis\entity\AdminLoginUser;
use app\common\lib\exception\RuntimeException;
use app\common\lib\VerifyData;
use app\common\logic\admin\coupon_activity\CreateActivity;
use app\common\logic\admin\entity\CreateActivityRequest;
use app\common\model\Stations;
use PHPUnit\Framework\TestCase;
use ReflectionClass;
use ReflectionException;
use Respect\Validation\Validator as v;
use think\Request;
use Throwable;
use think\facade\Db;

class TestCreateCouponActivity extends TestCase
{
    public function testVerifyParamsName()
    {
        // 参数要求：
        // name: 字符串类型 长度范围: 1~128


        $params = [
            'name' => ['0925'],
        ];

        // 非字符串类型
        try {
            v::input($params, VerifyData::coupon_activity([
                'name',
            ]));

            $this->fail('没有抛出验证异常');
        } catch (Throwable $e) {
            $this->assertSame('活动名称 必须是string类型', $e->getMessage());
        }


        // 验证名称超出最大长度
        $params['name'] = str_repeat('测', 129);
        try {
            v::input($params, VerifyData::coupon_activity([
                'name',
            ]));
            $this->fail('没有抛出验证异常');
        } catch (Throwable $e) {
            $this->assertSame('活动名称 长度必须在 1 与 128 之间', $e->getMessage());
        }

        // 验证名称低于最小长度
        $params['name'] = '';
        try {
            v::input($params, VerifyData::coupon_activity([
                'name',
            ]));
            $this->fail('没有抛出验证异常');
        } catch (Throwable $e) {
            $this->assertSame('活动名称 不能为空', $e->getMessage());
        }

        // 验证合法的名称
        $params['name'] = '新站优惠';
        try {
            $verifyData = v::input($params, VerifyData::coupon_activity([
                'name',
            ]));
            $this->assertSame($params, $verifyData);
        } catch (Throwable $e) {
            $this->fail('验证失败');
        }
    }

    public function testVerifyParamsGrantMaxCount()
    {
        // 参数要求：
        // grant_max_count: 数值类型 取值范围: 1~10000


        $params = [
            'grant_max_count' => "10000",
        ];
        try {
            v::input($params, VerifyData::coupon_activity([
                'grant_max_count',
            ]));

            $this->fail('没有抛出验证异常');
        } catch (Throwable $e) {
            $this->assertSame('活动中优惠券包发放上限 必须是integer类型', $e->getMessage());
        }


        $params['grant_max_count'] = 10001;
        try {
            v::input($params, VerifyData::coupon_activity([
                'grant_max_count',
            ]));
            $this->fail('没有抛出验证异常');
        } catch (Throwable $e) {
            $this->assertSame('活动中优惠券包发放上限 必须小于或等于 10000', $e->getMessage());
        }

        $params['grant_max_count'] = 0;
        try {
            v::input($params, VerifyData::coupon_activity([
                'grant_max_count',
            ]));
            $this->fail('没有抛出验证异常');
        } catch (Throwable $e) {
            $this->assertSame('活动中优惠券包发放上限 必须大于或等于 1', $e->getMessage());
        }

        $params['grant_max_count'] = 1000;
        try {
            $verifyData = v::input($params, VerifyData::coupon_activity([
                'grant_max_count',
            ]));
            $this->assertSame($params, $verifyData);
        } catch (Throwable $e) {
            $this->fail('验证失败');
        }
    }

    public function testVerifyParamsEffectiveDays()
    {
        // 参数要求：
        // effective_days: 数值类型 最小值: 1

        $params = [
            'effective_days' => "10000",
        ];
        try {
            v::input($params, VerifyData::coupon_activity([
                'effective_days',
            ]));

            $this->fail('没有抛出验证异常');
        } catch (Throwable $e) {
            $this->assertSame('领取后的有效天数 必须是integer类型', $e->getMessage());
        }


        $params['effective_days'] = 0;
        try {
            v::input($params, VerifyData::coupon_activity([
                'effective_days',
            ]));
            $this->fail('没有抛出验证异常');
        } catch (Throwable $e) {
            $this->assertSame('领取后的有效天数 必须大于或等于 1', $e->getMessage());
        }

        $params['effective_days'] = 30;
        try {
            $verifyData = v::input($params, VerifyData::coupon_activity([
                'effective_days',
            ]));
            $this->assertSame($params, $verifyData);
        } catch (Throwable $e) {
            $this->fail('验证失败');
        }
    }

    public function testVerifyParamsStartTime()
    {
        // 参数要求：
        // start_time: 日期时间类型 长度限制：19~19

        $params = [
            'start_time' => strtotime("2024-09-25 08:00:00"),
        ];
        try {
            v::input($params, VerifyData::coupon_activity([
                'start_time',
            ]));

            $this->fail('没有抛出验证异常');
        } catch (Throwable $e) {
            $this->assertSame('活动开始时间 必须是有效的日期/时间', $e->getMessage());
        }


        $params['start_time'] = "2024-09-25";
        try {
            v::input($params, VerifyData::coupon_activity([
                'start_time',
            ]));
            $this->fail('没有抛出验证异常');
        } catch (Throwable $e) {
            $this->assertSame('活动开始时间 长度必须是 19', $e->getMessage());
        }

        $params['start_time'] = "2024-09-25 08:00:00";
        try {
            $verifyData = v::input($params, VerifyData::coupon_activity([
                'start_time',
            ]));
            $this->assertSame($params, $verifyData);
        } catch (Throwable $e) {
            $this->fail('验证失败');
        }
    }

    public function testVerifyParamsEndTime()
    {
        // 参数要求：
        // end_time: 日期时间类型 长度限制：19~19

        $params = [
            'end_time' => strtotime("2024-10-25 08:00:00"),
        ];
        try {
            v::input($params, VerifyData::coupon_activity([
                'end_time',
            ]));

            $this->fail('没有抛出验证异常');
        } catch (Throwable $e) {
            $this->assertSame('活动结束时间 必须是有效的日期/时间', $e->getMessage());
        }


        $params['end_time'] = "2024-09-25";
        try {
            v::input($params, VerifyData::coupon_activity([
                'end_time',
            ]));
            $this->fail('没有抛出验证异常');
        } catch (Throwable $e) {
            $this->assertSame('活动结束时间 长度必须是 19', $e->getMessage());
        }

        $params['end_time'] = "2024-10-25 08:00:00";
        try {
            $verifyData = v::input($params, VerifyData::coupon_activity([
                'end_time',
            ]));
            $this->assertSame($params, $verifyData);
        } catch (Throwable $e) {
            $this->fail('验证失败');
        }
    }

    public function testVerifyParamsStationIds()
    {
        // 参数要求：
        // station_ids: 数组类型 元素类型必须是整数

        $params = [
            'station_ids' => strtotime("2024-10-25 08:00:00"),
        ];
        try {
            v::input($params, VerifyData::coupon_activity([
                'station_ids',
            ]));

            $this->fail('没有抛出验证异常');
        } catch (Throwable $e) {
            $this->assertSame('场站ID集合 必须是array类型', $e->getMessage());
        }


        $params['station_ids'] = [];
        try {
            $verifyData = v::input($params, VerifyData::coupon_activity([
                'station_ids',
            ]));
            $this->assertSame($params, $verifyData, '允许传递空数组');
        } catch (Throwable $e) {
            $this->fail('抛出验证异常');

        }

        $params['station_ids'] = [10001, 10002];
        try {
            $verifyData = v::input($params, VerifyData::coupon_activity([
                'station_ids',
            ]));
            $this->assertSame($params, $verifyData);
        } catch (Throwable $e) {
            var_export($e);
            $this->fail('验证失败');
        }
    }

    /**
     * @testdox 测试所有的参数校验
     *
     * @return void
     */
    public function testVerifyParamsAll()
    {
        $params = [
            'name' => '活动名称',
            'grant_max_count' => 10000,
            'effective_days' => 30,
            'start_time' => '2024-09-25 08:00:00',
            'end_time' => '2024-09-30 08:00:00',
            'station_ids' => [1001]
        ];
        try {
            $verifyData = v::input($params, VerifyData::coupon_activity([
                'name',
                'grant_max_count',
                'effective_days',
                'start_time',
                'end_time',
                'station_ids'
            ]));

            $this->assertSame($params, $verifyData);
        } catch (Throwable $e) {
            $this->fail('抛出验证异常');
        }
    }

    /**
     * @testdox 测试将参数导入请求对象后，参数值是否不变。
     *
     * @return void
     */
    public function testCreateActivityRequest()
    {
        $params = [
            'name' => '活动名称',
            'grant_max_count' => 10000,
            'effective_days' => 30,
            'start_time' => '2024-09-25 08:00:00',
            'end_time' => '2024-09-30 08:00:00',
            'station_ids' => [1001]
        ];
        $request = new CreateActivityRequest($params);

        $this->assertSame($params['name'], $request->name);
        $this->assertSame($params['grant_max_count'], $request->grant_max_count);
        $this->assertSame($params['effective_days'], $request->effective_days);
        $this->assertSame($params['start_time'], $request->start_time);
        $this->assertSame($params['end_time'], $request->end_time);
        $this->assertSame($params['station_ids'], $request->station_ids);
    }

    /**
     * @testdox 验证活动时间不通过的场景
     *
     * @return void
     * @throws ReflectionException
     */
    public function testVerifyActivityTimeNoPass()
    {
        $params = [
            'name' => '活动名称',
            'grant_max_count' => 10000,
            'effective_days' => 30,
            'start_time' => '2024-10-25 08:00:00',
            'end_time' => '2024-09-30 08:00:00',
            'station_ids' => [1001]
        ];
        $request = new CreateActivityRequest($params);

        $logic = new CreateActivity(
            new AdminLoginUser([]),
            $request,
            new Request()
        );

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyActivityTime');
        $method->setAccessible(true);

        try {
            // 执行方法
            $method->invokeArgs($logic, [$params['start_time'], $params['end_time']]);

            $this->fail('没有抛出异常');
        } catch (RuntimeException $e) {
            $this->assertSame(RuntimeException::CodeBusinessException, $e->getCode());
            $this->assertSame("活动结束时间必须大于活动开始时间", $e->getMessage());
        }
    }

    /**
     * @testdox 验证活动时间通过的场景
     *
     * @return void
     * @throws ReflectionException
     */
    public function testVerifyActivityTimePass()
    {
        $params = [
            'name' => '活动名称',
            'grant_max_count' => 10000,
            'effective_days' => 30,
            'start_time' => '2024-09-25 08:00:00',
            'end_time' => '2024-09-30 08:00:00',
            'station_ids' => [1001]
        ];
        $request = new CreateActivityRequest($params);

        $logic = new CreateActivity(
            new AdminLoginUser([]),
            $request,
            new Request()
        );

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyActivityTime');
        $method->setAccessible(true);

        try {
            // 执行方法
            $method->invokeArgs($logic, [$params['start_time'], $params['end_time']]);

            $this->assertTrue(true, '验证通过');
        } catch (RuntimeException $e) {
            $this->fail('抛出异常');
        }
    }


    /**
     * @testdox 测试验证具有创建优惠券活动的权限
     */
    public function testVerifyCreatePermission()
    {
        // 准备
        $params = [
            'name' => '活动名称',
            'grant_max_count' => 10000,
            'effective_days' => 30,
            'start_time' => '2024-09-25 08:00:00',
            'end_time' => '2024-09-30 08:00:00',
            'station_ids' => [1001, 1002, 1003, 1004]
        ];
        $request = new CreateActivityRequest($params);

        // 模拟一个运营商子账号的信息(pid > 0 && corp_id > 0)
        $logic = new CreateActivity(
            new AdminLoginUser(['id' => 3, 'corp_id' => 1001, 'pid' => 2]),
            $request,
            new Request()
        );

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyCreatePermission');
        $method->setAccessible(true);

        try {
            // 执行方法
            $method->invokeArgs($logic, [new AdminLoginUser(['id' => 3, 'corp_id' => 1001, 'pid' => 2])]);

            $this->fail('没有抛出异常');
        } catch (RuntimeException $e) {
            $this->assertSame(RuntimeException::CodeBusinessException, $e->getCode());
            $this->assertSame("只有运营商主账号才支持创建优惠券活动", $e->getMessage());
        }


        // 模拟一个超级管理员的信息(pid == 0 && corp_id == 0)
        $logic = new CreateActivity(
            new AdminLoginUser(['id' => 1, 'corp_id' => 0, 'pid' => 0]),
            $request,
            new Request()
        );

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyCreatePermission');
        $method->setAccessible(true);

        try {
            // 执行方法
            $method->invokeArgs($logic, [new AdminLoginUser(['id' => 1, 'corp_id' => 0, 'pid' => 0])]);

            $this->fail('没有抛出异常');
        } catch (RuntimeException $e) {
            $this->assertSame(RuntimeException::CodeBusinessException, $e->getCode());
            $this->assertSame("只有运营商主账号才支持创建优惠券活动", $e->getMessage());
        }


        // 模拟一个运营商主账号的信息(pid == 0 && corp_id > 0)
        $logic = new CreateActivity(
            new AdminLoginUser(['id' => 2, 'corp_id' => 10002, 'pid' => 0]),
            $request,
            new Request()
        );

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyCreatePermission');
        $method->setAccessible(true);

        try {
            // 执行方法
            $method->invokeArgs($logic, [new AdminLoginUser(['id' => 2, 'corp_id' => 10002, 'pid' => 0])]);

            $this->assertTrue(true, '有创建的权限');
        } catch (RuntimeException $e) {
            $this->fail('抛出异常');
        }
    }


    /**
     * @testdox 测试验证场站ID集合合法性 - 验证不通过场景
     */
    public function testVerifyStationIdsLegitimacyNoPass()
    {
        // 准备
        $params = [
            'name' => '活动名称',
            'grant_max_count' => 10000,
            'effective_days' => 30,
            'start_time' => '2024-09-25 08:00:00',
            'end_time' => '2024-09-30 08:00:00',
            'station_ids' => [1001, 1002, 1003, 1004]
        ];
        $request = new CreateActivityRequest($params);

        // 模拟一个运营商主账号的信息(pid == 0 && corp_id > 0)
        $loginUser = new AdminLoginUser(['id' => 1, 'corp_id' => 1001, 'pid' => 0]);
        $logic = new CreateActivity(
            $loginUser,
            $request,
            new Request()
        );

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyStationIdsLegitimacy');
        $method->setAccessible(true);


        // 创建存根来替换真实的数据库查询
        $stationsModel = $this->createStub(Stations::class);
        $stationsModel->method('getStationIds')->willReturn([1002, 1004]);
        bind(Stations::class, $stationsModel);


        try {
            // 执行方法
            $method->invokeArgs($logic, [$loginUser->corp_id, $params['station_ids']]);

            $this->fail('没有抛出异常');
        } catch (RuntimeException $e) {
            $this->assertSame(RuntimeException::CodeBusinessException, $e->getCode());
            $this->assertSame("场站ID集合中存在无效的ID：1001,1003", $e->getMessage());
        }
    }

    /**
     * @testdox 测试验证场站ID集合合法性 - 验证通过场景
     */
    public function testVerifyStationIdsLegitimacyPass()
    {
        // 准备
        $params = [
            'name' => '活动名称',
            'grant_max_count' => 10000,
            'effective_days' => 30,
            'start_time' => '2024-09-25 08:00:00',
            'end_time' => '2024-09-30 08:00:00',
            'station_ids' => [1001, 1002, 1003, 1004]
        ];
        $request = new CreateActivityRequest($params);

        // 模拟一个运营商主账号的信息(pid == 0 && corp_id > 0)
        $loginUser = new AdminLoginUser(['id' => 1, 'corp_id' => 1001, 'pid' => 0]);
        $logic = new CreateActivity(
            $loginUser,
            $request,
            new Request()
        );

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyStationIdsLegitimacy');
        $method->setAccessible(true);


        // 创建存根来替换真实的数据库查询
        $stationsModel = $this->createStub(Stations::class);
        $stationsModel->method('getStationIds')->willReturn([1001, 1002, 1003, 1004]);
        bind(Stations::class, $stationsModel);


        try {
            // 执行方法
            $method->invokeArgs($logic, [$loginUser->corp_id, $params['station_ids']]);

            $this->assertTrue(true, '验证通过');
        } catch (RuntimeException $e) {
            $this->fail('抛出异常');
        }
    }

    /**
     * @testdox 验证写入活动数据
     */
    public function testCreateActivity()
    {
        // 准备
        $params = [
            'name' => '活动名称',
            'grant_max_count' => 10000,
            'effective_days' => 30,
            'start_time' => '2024-09-25 08:00:00',
            'end_time' => '2024-09-30 08:00:00',
            'station_ids' => [1001, 1002, 1003, 1004]
        ];
        $request = new CreateActivityRequest($params);

        // 模拟一个运营商主账号的信息(pid == 0 && corp_id > 0)
        $loginUser = new AdminLoginUser(['id' => 1, 'corp_id' => 1001, 'pid' => 0]);
        $logic = new CreateActivity(
            $loginUser,
            $request,
            new Request()
        );

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('createActivity');
        $method->setAccessible(true);

        // 执行方法
        Db::startTrans();
        $new_id = $method->invokeArgs($logic, [
            $request->name, $loginUser->corp_id, $loginUser->id,
            $request->grant_max_count, $request->effective_days,
            $request->start_time, $request->end_time
        ]);
        Db::rollback();
        $sql = Db::getLastSql();

        $this->assertIsInt($new_id);

        $expected_sql = sprintf(
            "INSERT INTO `coupon_activity` SET `name` = '%s' , `corp_id` = '%s' , `user_id` = %d , `status` = 1 , `grant_rule` = 0 , `grant_max_count` = %d , `grant_user_count` = 1 , `effective_days` = %d , `qcode_url` = '' , `grant_num` = 0 , `is_del` = 0 , `start_time` = '%s' , `end_time` = '%s' , `create_time` = '%s' , `update_time` = NULL",
            $request->name, $loginUser->corp_id, $loginUser->id,
            $request->grant_max_count, $request->effective_days,
            $request->start_time, $request->end_time, date('Y-m-d H:i:s')
        );

        $this->assertSame($expected_sql, $sql);
    }

    /**
     * @testdox 构建活动与场站的关系
     */
    public function testBuildActivityAndStationRelation()
    {
        // 准备
        $request = new CreateActivityRequest([]);

        // 模拟一个运营商主账号的信息(pid == 0 && corp_id > 0)
        $loginUser = new AdminLoginUser(['id' => 1, 'corp_id' => 1001, 'pid' => 0]);
        $logic = new CreateActivity(
            $loginUser,
            $request,
            new Request()
        );

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('buildActivityAndStationRelation');
        $method->setAccessible(true);

        $params = [
            'station_ids' => [1001, 1003],
            'activity_id' => 1
        ];

        // 执行方法
        Db::startTrans();
        $method->invokeArgs($logic, [$params['activity_id'], $params['station_ids']]);
        Db::rollback();
        $sql = Db::getLastSql();

        $expected_sql = "INSERT INTO `coupon_activity_station` (`activity_id` , `station_id` , `create_time`) VALUES ";
        foreach ($params['station_ids'] as $index => $station_id) {
            if ($index !== 0) {
                $expected_sql .= " , ";
            }
            $expected_sql .= sprintf("( %d,%d,'%s' )", $params['activity_id'], $station_id, date("Y-m-d H:i:s"));
        }

        $this->assertSame($expected_sql, $sql);
    }

    /**
     * @testdox 记录操作日志
     */
    public function testAddOperationLog()
    {
        // 准备
        $request = new CreateActivityRequest([]);

        // 模拟一个运营商主账号的信息(pid == 0 && corp_id > 0)
        $loginUser = new AdminLoginUser(['id' => 1, 'corp_id' => 1001, 'pid' => 0]);
        $logic = new CreateActivity(
            $loginUser,
            $request,
            new Request()
        );

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('addOperationLog');
        $method->setAccessible(true);

        $params = [
            'corp_id' => 10001,
            'user_id' => 2,
            'op_interface' => '/admin/coupon_activity/create_activity',
            'op_content' => [
                'name' => '活动名称',
                'grant_max_count' => 1002,
                'effective_days' => 30,
                'start_time' => date('Y-m-d H:i:s', strtotime('+1 day')),
                'end_time' => date('Y-m-d H:i:s', strtotime('+10 day')),
                'station_ids' => [1001, 1003, 1004]
            ]
        ];

        // 执行方法
        Db::startTrans();
        // int $corp_id, int $user_id, int $user_type, string $op_interface, string $op_content
        $method->invokeArgs($logic, [
            $params['corp_id'], $params['user_id'],
            $params['op_interface'], $params['op_content']
        ]);
        Db::rollback();
        $sql = Db::getLastSql();

        // 断言
        $expected_sql = sprintf(
            "INSERT INTO `coupon_oplog` SET `corp_id` = '%d' , `user_id` = '%d' , `user_type` = 0 , `op_interface` = '%s' , `op_content` = '%s' , `op_time` = '%s'",
            $params['corp_id'], $params['user_id'], $params['op_interface'], addslashes(json_encode($params['op_content'])), date('Y-m-d H:i:s')
        );

        $this->assertSame($expected_sql, $sql);
    }

    /**
     * @testdox 测试完整的逻辑
     */
    public function testAllLogic()
    {
        // 准备
        $params = [
            'name' => '活动名称',
            'grant_max_count' => 10000,
            'effective_days' => 30,
            'start_time' => '2024-09-25 08:00:00',
            'end_time' => '2024-09-30 08:00:00',
            'station_ids' => [1001, 1002, 1003, 1004]
        ];
        $request = new CreateActivityRequest($params);

        // 创建存根来替换真实的数据库查询
        $model = $this->createStub(Stations::class);
        $model->method('getStationIds')->willReturn([1001, 1002, 1003, 1004]);
        bind(Stations::class, $model);


        // 模拟一个运营商主账号的信息(pid == 0 && corp_id > 0)
        $loginUser = new AdminLoginUser(['id' => 1, 'corp_id' => 1001, 'pid' => 0]);
        $logic = new CreateActivity(
            $loginUser,
            $request,
            new Request()
        );

        // 执行方法
        Db::startTrans();
        $result = $logic->run();
        Db::rollback();

        $this->assertIsArray($result);
        $this->assertArrayHasKey('new_id', $result);
        $this->assertIsInt($result['new_id']);
        $this->assertGreaterThan(0, $result['new_id']);
    }


}