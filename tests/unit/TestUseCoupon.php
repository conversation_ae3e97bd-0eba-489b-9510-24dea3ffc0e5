<?php /** @noinspection PhpDynamicAsStaticMethodCallInspection */

namespace tests\unit;

use app\common\logic\common\coupon_activity\entity\UseCouponRequest;
use app\common\logic\common\coupon_activity\UseCoupon;
use app\common\model\CouponCollection;
use app\common\model\CouponCollectionOrder;
use PHPUnit\Framework\TestCase;
use ReflectionClass;
use think\facade\Db;

/**
 * @testdox 使用现金券
 */
class TestUseCoupon extends TestCase
{
    /**
     * @testdox 创建请求对象
     */
    public function testCreateRequest()
    {
        $params = [
            'user_id' => 1,
            'station_id' => 2,
            'order_id' => '10000000000063022407261539230002',
            'service_cost' => 55
        ];
        $request = new UseCouponRequest($params);

        $this->assertSame($params['user_id'], $request->user_id);
        $this->assertSame($params['station_id'], $request->station_id);
        $this->assertSame($params['order_id'], $request->order_id);
        $this->assertSame($params['service_cost'], $request->service_cost);
        $this->assertSame($params, $request->toArray());
    }

    /**
     * @testdox 给用户的优惠券加上排他锁
     */
    public function testLockUserCoupon()
    {
        $request = new UseCouponRequest([]);
        $logic = new UseCoupon($request);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('lockUserCoupon');
        $method->setAccessible(true);

        $params = [
            'user_id' => 1
        ];

        $method->invokeArgs($logic, [$params['user_id']]);

        $sql = CouponCollection::getLastSql();

        $expected_sql = sprintf(
            "SELECT `id` FROM `coupon_collection` WHERE  `user_id` = '%d'  FOR UPDATE",
            $params['user_id']

        );

        $this->assertSame($expected_sql, $sql);
    }

    /**
     * @testdox 获取指定场站有效的优惠券，验证查询语句。
     */
    public function testGetStationEffectiveCoupon()
    {
        $request = new UseCouponRequest([]);
        $logic = new UseCoupon($request);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('getStationEffectiveCoupon');
        $method->setAccessible(true);

        // 传递的参数
        $params = [
            'user_id' => 1,
            'station_id' => 2
        ];

        $method->invokeArgs($logic, [$params['user_id'], $params['station_id']]);

        $sql = CouponCollection::getLastSql();

        $query_fields = [
            '`cc`.`id`', '`cp`.`par_value`',
            '`cp`.`usage_amount`', '`cc`.`expire_time`'
        ];
        $table_name = '`coupon_collection` `cc`';
        $leftJoin1 = '`coupon_pattern` `cp` ON `cp`.`id`=`cc`.`pattern_id`';
        $leftJoin2 = '`coupon_activity_station` `cas` ON `cas`.`activity_id`=`cc`.`activity_id`';
        $where = [
            sprintf("`cc`.`user_id` = '%d'", $params['user_id']),
            sprintf("`cc`.`status` = '%d'", CouponCollection::StatusNotUsed),
            sprintf("`cas`.`station_id` = '%d'", $params['station_id']),
            sprintf("`cc`.`expire_time` >= '%s'", date('Y-m-d H:i:s'))
        ];
        $group = '`cc`.`id`';
        $expected_sql = sprintf(
            'SELECT %s FROM %s LEFT JOIN %s LEFT JOIN %s WHERE  %s GROUP BY %s',
            implode(',', $query_fields),
            $table_name,
            $leftJoin1,
            $leftJoin2,
            implode("  AND ", $where),
            $group
        );

        $this->assertSame($expected_sql, $sql);
    }

    /**
     * @testdox 对优惠券进行排序 - 没有一张优惠券
     */
    public function testSortByParValueDESCAndExpireTimeASCScene1()
    {
        $request = new UseCouponRequest([]);
        $logic = new UseCoupon($request);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('sortByParValueDESCAndExpireTimeASC');
        $method->setAccessible(true);

        // 传递的参数
        $params = [
            'coupons' => [
            ]
        ];

        $result = $method->invokeArgs($logic, [$params['coupons']]);

        $expected_result = [];

        $this->assertSame($expected_result, $result);
    }


    /**
     * @testdox 对优惠券进行排序 - 相同面值，过期时间不同
     */
    public function testSortByParValueDESCAndExpireTimeASCScene2()
    {
        $request = new UseCouponRequest([]);
        $logic = new UseCoupon($request);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('sortByParValueDESCAndExpireTimeASC');
        $method->setAccessible(true);

        // 传递的参数
        $params = [
            'coupons' => [
                ['par_value' => 100, 'expire_time' => date('Y-m-d H:i:s', strtotime('+3 day'))],
                ['par_value' => 100, 'expire_time' => date('Y-m-d H:i:s', strtotime('+2 day'))],
            ]
        ];

        $result = $method->invokeArgs($logic, [$params['coupons']]);

        $expected_result = [
            ['par_value' => 100, 'expire_time' => date('Y-m-d H:i:s', strtotime('+2 day'))],
            ['par_value' => 100, 'expire_time' => date('Y-m-d H:i:s', strtotime('+3 day'))],
        ];

        $this->assertSame($expected_result, $result);
    }


    /**
     * @testdox 对优惠券进行排序 - 不同面值，不同过期时间
     */
    public function testSortByParValueDESCAndExpireTimeASCScene3()
    {
        $request = new UseCouponRequest([]);
        $logic = new UseCoupon($request);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('sortByParValueDESCAndExpireTimeASC');
        $method->setAccessible(true);

        // 传递的参数
        $params = [
            'coupons' => [
                ['par_value' => 60, 'expire_time' => date('Y-m-d H:i:s', strtotime('+3 day'))],
                ['par_value' => 60, 'expire_time' => date('Y-m-d H:i:s', strtotime('+4 day'))],
                ['par_value' => 100, 'expire_time' => date('Y-m-d H:i:s', strtotime('+8 day'))],
                ['par_value' => 100, 'expire_time' => date('Y-m-d H:i:s', strtotime('+2 day'))],
            ]
        ];

        $result = $method->invokeArgs($logic, [$params['coupons']]);

        $expected_result = [
            ['par_value' => 100, 'expire_time' => date('Y-m-d H:i:s', strtotime('+2 day'))],
            ['par_value' => 100, 'expire_time' => date('Y-m-d H:i:s', strtotime('+8 day'))],
            ['par_value' => 60, 'expire_time' => date('Y-m-d H:i:s', strtotime('+3 day'))],
            ['par_value' => 60, 'expire_time' => date('Y-m-d H:i:s', strtotime('+4 day'))],
        ];

        $this->assertSame($expected_result, $result);
    }

    /**
     * @testdox 对优惠券进行排序: 面值大的靠前 并且 快要过期的靠前 - 相同面值，相同过期时间
     */
    public function testSortByParValueDESCAndExpireTimeASCScene4()
    {
        $request = new UseCouponRequest([]);
        $logic = new UseCoupon($request);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('sortByParValueDESCAndExpireTimeASC');
        $method->setAccessible(true);

        // 传递的参数
        $params = [
            'coupons' => [
                ['id' => 1, 'par_value' => 100, 'expire_time' => date('Y-m-d H:i:s', strtotime('+10 day'))],
                ['id' => 2, 'par_value' => 100, 'expire_time' => date('Y-m-d H:i:s', strtotime('+10 day'))],
                ['id' => 3, 'par_value' => 100, 'expire_time' => date('Y-m-d H:i:s', strtotime('+10 day'))],
                ['id' => 4, 'par_value' => 100, 'expire_time' => date('Y-m-d H:i:s', strtotime('+10 day'))],
            ]
        ];

        $result = $method->invokeArgs($logic, [$params['coupons']]);

        $expected_result = [
            ['id' => 1, 'par_value' => 100, 'expire_time' => date('Y-m-d H:i:s', strtotime('+10 day'))],
            ['id' => 2, 'par_value' => 100, 'expire_time' => date('Y-m-d H:i:s', strtotime('+10 day'))],
            ['id' => 3, 'par_value' => 100, 'expire_time' => date('Y-m-d H:i:s', strtotime('+10 day'))],
            ['id' => 4, 'par_value' => 100, 'expire_time' => date('Y-m-d H:i:s', strtotime('+10 day'))],
        ];

        $this->assertSame($expected_result, $result);
    }

    /**
     * @testdox 在这些优惠券里找出能使用的最大优惠的优惠券
     */
    public function testFindCanUseMaxDiscountCoupon()
    {
        $request = new UseCouponRequest([]);
        $logic = new UseCoupon($request);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('findCanUseMaxDiscountCoupon');
        $method->setAccessible(true);

        // usage_amount 的单位:角
        $coupons = [
            ['par_value' => 100, 'usage_amount' => 220, 'expire_time' => date('Y-m-d H:i:s', strtotime('+2 day'))],
            ['par_value' => 100, 'usage_amount' => 110, 'expire_time' => date('Y-m-d H:i:s', strtotime('+8 day'))],
            ['par_value' => 60, 'usage_amount' => 100, 'expire_time' => date('Y-m-d H:i:s', strtotime('+3 day'))],
            ['par_value' => 60, 'usage_amount' => 100, 'expire_time' => date('Y-m-d H:i:s', strtotime('+4 day'))],
        ];
        // 单位:分
        $service_cost = 2000;

        $selected_coupon = $method->invokeArgs($logic, [$coupons, $service_cost]);
        $expected_selected_coupon = ['par_value' => 100, 'usage_amount' => 110, 'expire_time' => date('Y-m-d H:i:s', strtotime('+8 day'))];
        $this->assertSame($expected_selected_coupon, $selected_coupon);
    }

    /**
     * @testdox 在原来充电费用的基础上，扣除优惠券减免的金额 | 优惠券减免金额低于服务费金额
     */
    public function testDiscountScene1()
    {
        $request = new UseCouponRequest([]);
        $logic = new UseCoupon($request);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('discount');
        $method->setAccessible(true);


        $use_coupon = [
            // 单位:角
            'par_value' => 100,
            // 单位:角
            'usage_amount' => 110,
            'expire_time' => date('Y-m-d H:i:s', strtotime('+8 day')),
        ];
        // 单位:分
        $service_cost = 2000;
        // 预期打完折扣后的服务费
        $expected_service_cost = 1000;

        $new_service_cost = $method->invokeArgs($logic, [$service_cost, $use_coupon]);

        $this->assertSame($expected_service_cost, $new_service_cost);
    }

    /**
     * @testdox 在原来充电费用的基础上，扣除优惠券减免的金额 | 优惠券减免金额高于服务费金额
     */
    public function testDiscountScene2()
    {
        $request = new UseCouponRequest([]);
        $logic = new UseCoupon($request);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('discount');
        $method->setAccessible(true);


        $use_coupon = [
            // 单位:角
            'par_value' => 100,
            // 单位:角
            'usage_amount' => 110,
            'expire_time' => date('Y-m-d H:i:s', strtotime('+8 day')),
        ];
        // 单位:分
        $service_cost = 500;
        // 预期打完折扣后的服务费
        $expected_service_cost = 0;

        $new_service_cost = $method->invokeArgs($logic, [$service_cost, $use_coupon]);

        $this->assertSame($expected_service_cost, $new_service_cost);
    }

    /**
     * @testdox 将指定优惠券标记为已使用
     */
    public function testCouponStatusUpdateUsed()
    {
        $request = new UseCouponRequest([]);
        $logic = new UseCoupon($request);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('couponStatusUpdateUsed');
        $method->setAccessible(true);
        $params = [
            'id' => 1
        ];
        $expected_sql = sprintf(
            "UPDATE `coupon_collection`  SET `status` = 1 , `use_time` = '%s'  WHERE  `id` = %d",
            date('Y-m-d H:i:s'),
            $params['id']
        );

        Db::startTrans();
        $method->invokeArgs($logic, [$params['id']]);
        Db::rollback();

        $sql = CouponCollection::getLastSql();

        $this->assertSame($expected_sql, $sql);
    }

    /**
     * @testdox 将指定优惠券与当前订单建立关系
     */
    public function testRecordUseRelation()
    {
        $request = new UseCouponRequest([]);
        $logic = new UseCoupon($request);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('recordUseRelation');
        $method->setAccessible(true);
        $params = [
            'order_id' => "10000000000063022407261542490003",
            'coupon_id' => 1
        ];
        $expected_sql = sprintf(
            "INSERT INTO `coupon_collection_order` SET `order_id` = '%s' , `collection_id` = %d , `create_time` = '%s'",
            $params['order_id'],
            $params['coupon_id'],
            date('Y-m-d H:i:s'),
        );

        Db::startTrans();
        $method->invokeArgs($logic, [$params['order_id'], $params['coupon_id']]);
        Db::rollback();

        $sql = CouponCollectionOrder::getLastSql();

        $this->assertSame($expected_sql, $sql);
    }


    /**
     * @testdox 测试使用优惠券的全逻辑
     */
    public function testAllLogicScene1()
    {
        // 准备
        $request = new UseCouponRequest([
            'user_id' => 1,
            'station_id' => 2,
            'order_id' => '10000000000063022407261542490003',
            'service_cost' => 2000
        ]);
        $logic = new UseCoupon($request);

        // 创建存根来替换真实的数据库查询
        $couponCollectionModel = $this->createStub(CouponCollection::class);
        $couponCollectionModel->method('lockUserCoupon')->willReturn(1);
        $couponCollectionModel->method('useCoupon')->willReturn(true);
        $couponCollectionModel->method('getStationEffectiveCoupon')->willReturn([
            [
                'id' => 1,
                'par_value' => 30,
                'usage_amount' => 100,
                'expire_time' => date('Y-m-d H:i:s', strtotime('+2 day'))
            ],
            [
                'id' => 2,
                'par_value' => 30,
                'usage_amount' => 100,
                'expire_time' => date('Y-m-d H:i:s', strtotime('+1 day'))
            ],
            [
                'id' => 3,
                'par_value' => 35,
                'usage_amount' => 120,
                'expire_time' => date('Y-m-d H:i:s', strtotime('+4 day'))
            ],
            [
                'id' => 4,
                'par_value' => 35,
                'usage_amount' => 120,
                'expire_time' => date('Y-m-d H:i:s', strtotime('+3 day'))
            ]
        ]);
        bind(CouponCollection::class, $couponCollectionModel);
        $couponCollectionOrder = $this->createStub(CouponCollectionOrder::class);
        $couponCollectionOrder->method('recordUseRelation')->willReturn(1);
        bind(CouponCollectionOrder::class, $couponCollectionOrder);

        // 执行方法
        $result = $logic->run();

        // 断言
        $this->assertIsArray($result);
        $this->assertArrayHasKey('service_cost', $result);
        $this->assertArrayHasKey('use_coupon', $result);
        $this->assertSame(1650, $result['service_cost']);
        $this->assertSame([
            'id' => 4,
            'par_value' => 35,
            'usage_amount' => 120,
            'expire_time' => date('Y-m-d H:i:s', strtotime('+3 day'))
        ], $result['use_coupon']);
    }

}