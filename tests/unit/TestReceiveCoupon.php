<?php /** @noinspection PhpDynamicAsStaticMethodCallInspection */

namespace tests\unit;

use app\common\cache\redis\entity\AppletLoginUser;
use app\common\lib\VerifyData;
use app\common\logic\applet\coupon_activity\ReceiveCoupon;
use app\common\logic\applet\entity\ReceiveCouponRequest;
use app\common\model\CouponActivity;
use app\common\model\CouponActivity as CouponActivityModel;
use app\common\model\CouponCollection;
use app\common\model\CouponPattern;
use PHPUnit\Framework\TestCase;
use ReflectionClass;
use Respect\Validation\Validator as v;
use think\App;
use think\facade\Db;
use think\Request;

/**
 * @testdox 领取优惠券
 */
class TestReceiveCoupon extends TestCase
{
    /**
     * @testdox 验证参数 - (activity_id)活动ID - 不传递时会验证不通过。
     */
    public function testVerifyParamsActivityIdScene1()
    {
        $params = [];

        $this->expectExceptionMessage('活动ID 不能是可选的');

        v::input($params, VerifyData::coupon_activity([
            'activity_id',
        ]));
    }

    /**
     * @testdox 验证参数 - (activity_id)活动ID - 传递非数值时验证不通过。
     */
    public function testVerifyParamsActivityIdScene2()
    {
        $params = [
            'activity_id' => '1'
        ];

        $this->expectExceptionMessage('活动ID 必须是integer类型');

        v::input($params, VerifyData::coupon_activity([
            'activity_id',
        ]));
    }

    /**
     * @testdox 验证参数 - (activity_id)活动ID - 传递低于1的数值时会验证不通过。
     */
    public function testVerifyParamsActivityIdScene3()
    {
        $params = [
            'activity_id' => -1
        ];

        $this->expectExceptionMessage('活动ID 必须大于或等于 1');

        v::input($params, VerifyData::coupon_activity([
            'activity_id',
        ]));
    }

    /**
     * @testdox 验证参数 - (id)活动ID - 传递符合预期的数值，验证通过。
     */
    public function testVerifyParamsActivityIdScene4()
    {
        $params = [
            'activity_id' => 1
        ];

        $verify_params = v::input($params, VerifyData::coupon_activity([
            'activity_id',
        ]));

        $this->assertSame($params, $verify_params);
    }

    /**
     * @testdox 查询是否已领取
     */
    public function testQueryIsReceive()
    {
        $logicUser = new AppletLoginUser([]);
        $request = new ReceiveCouponRequest([]);
        $logic = new ReceiveCoupon($logicUser, $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('queryIsReceive');
        $method->setAccessible(true);

        // 执行方法
        $params = [
            'user_id' => 1,
            'activity_id' => 2
        ];
        $method->invokeArgs($logic, [$params['user_id'], $params['activity_id']]);

        $sql = CouponCollection::getLastSql();

        // 预期执行的SQL
        $expected_sql = sprintf(
            "SELECT COUNT(*) AS think_count FROM `coupon_collection` WHERE  `user_id` = '%d'  AND `activity_id` = %d",
            $params['user_id'], $params['activity_id']
        );

        $this->assertSame($expected_sql, $sql);
    }

    /**
     * @testdox 验证是否已领取 - 未领取
     */
    public function testVerifyIsReceiveScene1()
    {
        $logicUser = new AppletLoginUser([]);
        $request = new ReceiveCouponRequest([]);
        $logic = new ReceiveCoupon($logicUser, $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyIsReceive');
        $method->setAccessible(true);

        // 执行方法
        $params = [
            'is_receive' => false,
        ];
        $result = $method->invokeArgs($logic, [$params['is_receive']]);
        $this->assertNull($result);
    }

    /**
     * @testdox 验证是否已领取 - 已领取
     */
    public function testVerifyIsReceiveScene2()
    {
        $logicUser = new AppletLoginUser([]);
        $request = new ReceiveCouponRequest([]);
        $logic = new ReceiveCoupon($logicUser, $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyIsReceive');
        $method->setAccessible(true);

        // 断言
        $this->expectExceptionMessage('当前活动的优惠券您已经领取过了！');

        // 执行方法
        $params = [
            'is_receive' => true,
        ];
        $method->invokeArgs($logic, [$params['is_receive']]);
    }


    /**
     * @testdox 获取优惠券活动数据
     */
    public function testGetActivityData()
    {
        $logicUser = new AppletLoginUser([]);
        $request = new ReceiveCouponRequest([]);
        $logic = new ReceiveCoupon($logicUser, $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('getActivityData');
        $method->setAccessible(true);

        // 执行方法
        $params = [
            'activity_id' => 1,
        ];
        $method->invokeArgs($logic, [$params['activity_id']]);

        // 查询最近执行的一条SQL
        $sql = CouponActivity::getLastSql();

        // 预期执行的SQL
        $query_fields = [
            'corp_id', 'status', 'is_del', 'grant_max_count', 'grant_num',
            'start_time', 'end_time', 'effective_days'
        ];
        foreach ($query_fields as &$field) {
            $field = '`' . $field . '`';
        }
        $query_fields_sql = implode(',', $query_fields);
        $expected_sql = sprintf(
            "SELECT %s FROM `coupon_activity` WHERE  `id` = %d LIMIT 1",
            $query_fields_sql,
            $params['activity_id']
        );
        $this->assertSame($expected_sql, $sql);
    }


    /**
     * @testdox 验证是否能够领取这个活动的优惠券 - 无效的活动ID
     */
    public function testVerifyIsCanReceiveScene1()
    {
        $logicUser = new AppletLoginUser([]);
        $request = new ReceiveCouponRequest([]);
        $logic = new ReceiveCoupon($logicUser, $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyIsCanReceive');
        $method->setAccessible(true);

        // 断言
        $this->expectExceptionMessage('无效活动ID');

        // 执行方法
        $params = [
            'activity_data' => null,
        ];
        $method->invokeArgs($logic, [$params['activity_data']]);
    }

    /**
     * @testdox 验证是否能够领取这个活动的优惠券 - 活动已被删除
     */
    public function testVerifyIsCanReceiveScene2()
    {
        $logicUser = new AppletLoginUser([]);
        $request = new ReceiveCouponRequest([]);
        $logic = new ReceiveCoupon($logicUser, $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyIsCanReceive');
        $method->setAccessible(true);

        // 断言
        $this->expectExceptionMessage('当前活动已被删除');

        // 执行方法
        $params = [
            'activity_data' => [
                'is_del' => CouponActivity::IsDelYes
            ],
        ];
        $method->invokeArgs($logic, [$params['activity_data']]);
    }

    /**
     * @testdox 验证是否能够领取这个活动的优惠券 - 活动还没有开始(活动状态处于“创建中”)
     */
    public function testVerifyIsCanReceiveScene3()
    {
        $logicUser = new AppletLoginUser([]);
        $request = new ReceiveCouponRequest([]);
        $logic = new ReceiveCoupon($logicUser, $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyIsCanReceive');
        $method->setAccessible(true);

        // 断言
        $this->expectExceptionMessage('活动未开始');

        // 执行方法
        $params = [
            'activity_data' => [
                'is_del' => CouponActivity::IsDelNot,
                'status' => CouponActivity::StatusCreating
            ],
        ];
        $method->invokeArgs($logic, [$params['activity_data']]);
    }

    /**
     * @testdox 验证是否能够领取这个活动的优惠券 - 活动还没有开始(活动状态处于“审核中”)
     */
    public function testVerifyIsCanReceiveScene4()
    {
        $logicUser = new AppletLoginUser([]);
        $request = new ReceiveCouponRequest([]);
        $logic = new ReceiveCoupon($logicUser, $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyIsCanReceive');
        $method->setAccessible(true);

        // 断言
        $this->expectExceptionMessage('活动未开始');

        // 执行方法
        $params = [
            'activity_data' => [
                'is_del' => CouponActivity::IsDelNot,
                'status' => CouponActivity::StatusReview
            ],
        ];
        $method->invokeArgs($logic, [$params['activity_data']]);
    }

    /**
     * @testdox 验证是否能够领取这个活动的优惠券 - 活动还没有开始(活动状态处于“发放中”，但是还没有到达活动开始时间)
     */
    public function testVerifyIsCanReceiveScene5()
    {
        $logicUser = new AppletLoginUser([]);
        $request = new ReceiveCouponRequest([]);
        $logic = new ReceiveCoupon($logicUser, $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyIsCanReceive');
        $method->setAccessible(true);

        // 断言
        $this->expectExceptionMessage('活动未开始');

        // 执行方法
        $params = [
            'activity_data' => [
                'is_del' => CouponActivity::IsDelNot,
                'status' => CouponActivity::StatusGrant,
                'start_time' => date('Y-m-d H:i:s', strtotime('+1 day'))
            ],
        ];
        $method->invokeArgs($logic, [$params['activity_data']]);
    }

    /**
     * @testdox 验证是否能够领取这个活动的优惠券 - 活动还没有开始(活动状态处于“停用审核中”，但是还没有到达活动开始时间)
     */
    public function testVerifyIsCanReceiveScene6()
    {
        $logicUser = new AppletLoginUser([]);
        $request = new ReceiveCouponRequest([]);
        $logic = new ReceiveCoupon($logicUser, $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyIsCanReceive');
        $method->setAccessible(true);

        // 断言
        $this->expectExceptionMessage('活动未开始');

        // 执行方法
        $params = [
            'activity_data' => [
                'is_del' => CouponActivity::IsDelNot,
                'status' => CouponActivity::StatusReviewStop,
                'start_time' => date('Y-m-d H:i:s', strtotime('+1 day'))
            ],
        ];
        $method->invokeArgs($logic, [$params['activity_data']]);
    }

    /**
     * @testdox 验证是否能够领取这个活动的优惠券 - 活动已经被停用
     */
    public function testVerifyIsCanReceiveScene7()
    {
        $logicUser = new AppletLoginUser([]);
        $request = new ReceiveCouponRequest([]);
        $logic = new ReceiveCoupon($logicUser, $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyIsCanReceive');
        $method->setAccessible(true);

        // 断言抛出的异常描述
        $this->expectExceptionMessage('当前活动已被停用');

        // 执行方法
        $params = [
            'activity_data' => [
                'is_del' => CouponActivity::IsDelNot,
                'status' => CouponActivity::StatusStop,
                'start_time' => date('Y-m-d H:i:s', strtotime('+1 day'))
            ],
        ];
        $method->invokeArgs($logic, [$params['activity_data']]);
    }

    /**
     * @testdox 验证是否能够领取这个活动的优惠券 - 活动已结束(活动状态处于"已结束")
     */
    public function testVerifyIsCanReceiveScene8()
    {
        $logicUser = new AppletLoginUser([]);
        $request = new ReceiveCouponRequest([]);
        $logic = new ReceiveCoupon($logicUser, $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyIsCanReceive');
        $method->setAccessible(true);

        // 断言抛出的异常描述
        $this->expectExceptionMessage('活动已结束');

        // 执行方法
        $params = [
            'activity_data' => [
                'is_del' => CouponActivity::IsDelNot,
                'status' => CouponActivity::StatusEnd,
            ],
        ];
        $method->invokeArgs($logic, [$params['activity_data']]);
    }

    /**
     * @testdox 验证是否能够领取这个活动的优惠券 - 活动已结束(已经超过了活动结束时间)
     */
    public function testVerifyIsCanReceiveScene9()
    {
        $logicUser = new AppletLoginUser([]);
        $request = new ReceiveCouponRequest([]);
        $logic = new ReceiveCoupon($logicUser, $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyIsCanReceive');
        $method->setAccessible(true);

        // 断言抛出的异常描述
        $this->expectExceptionMessage('活动已结束');

        // 执行方法
        $params = [
            'activity_data' => [
                'is_del' => CouponActivity::IsDelNot,
                'status' => CouponActivity::StatusGrant,
                'start_time' => date('Y-m-d H:i:s'),
                'end_time' => date('Y-m-d H:i:s', strtotime('-1 day'))
            ],
        ];
        $method->invokeArgs($logic, [$params['activity_data']]);
    }

    /**
     * @testdox 验证是否能够领取这个活动的优惠券 - 当前活动的优惠券已经被领取完了
     */
    public function testVerifyIsCanReceiveScene10()
    {
        $logicUser = new AppletLoginUser([]);
        $request = new ReceiveCouponRequest([]);
        $logic = new ReceiveCoupon($logicUser, $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyIsCanReceive');
        $method->setAccessible(true);

        // 断言抛出的异常描述
        $this->expectExceptionMessage('当前活动的优惠券已经被领取完了。');

        // 执行方法
        $params = [
            'activity_data' => [
                'is_del' => CouponActivity::IsDelNot,
                'status' => CouponActivity::StatusGrant,
                'start_time' => date('Y-m-d H:i:s'),
                'end_time' => date('Y-m-d H:i:s', strtotime('+1 day')),
                'grant_max_count' => 100,
                'grant_num' => 100
            ],
        ];
        $method->invokeArgs($logic, [$params['activity_data']]);
    }


    /**
     * @testdox 查询活动关联的所有优惠券的模板ID
     */
    public function testGetActivityAllPatternId()
    {
        $logicUser = new AppletLoginUser([]);
        $request = new ReceiveCouponRequest([]);
        $logic = new ReceiveCoupon($logicUser, $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('getActivityAllPatternId');
        $method->setAccessible(true);

        // 执行方法
        $params = [
            'activity_id' => 1,
        ];
        $method->invokeArgs($logic, [$params['activity_id']]);

        // 查询最近执行的一条SQL
        $sql = CouponPattern::getLastSql();

        // 预期执行的SQL
        $expected_sql = sprintf(
            "SELECT `cp`.`id` FROM `coupon_pattern` `cp` WHERE  `cp`.`activity_id` = '%d'",
            $params['activity_id']
        );
        $this->assertSame($expected_sql, $sql);
    }

    /**
     * @testdox 计算优惠券的过期时间
     */
    public function testCalculationExpireTime()
    {
        $logicUser = new AppletLoginUser([]);
        $request = new ReceiveCouponRequest([]);
        $logic = new ReceiveCoupon($logicUser, $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('calculationExpireTime');
        $method->setAccessible(true);

        // 执行方法
        $params = [
            // 有效期天数
            'effective_days' => 30,
        ];
        $expire_time = $method->invokeArgs($logic, [$params['effective_days']]);

        // 预期的到期时间
        $expected_expire_time = date('Y-m-d H:i:s', time() + $params['effective_days'] * 24 * 3600);
        $this->assertSame($expected_expire_time, $expire_time);
    }

    /**
     * @testdox 发放优惠券
     */
    public function testReceiveCoupon()
    {
        $logicUser = new AppletLoginUser([]);
        $request = new ReceiveCouponRequest([]);
        $logic = new ReceiveCoupon($logicUser, $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('receiveCoupon');
        $method->setAccessible(true);

        // 执行方法
        $params = [
            'user_id' => 1,
            'activity_id' => 2,
            'expire_time' => '2024-10-28 09:00:00',
            'pattern_ids' => [4, 6, 7]
        ];
        // 开启事务(为了写入后回滚事务，这样能不将测试的数据写入到数据库中)
        Db::startTrans();
        $method->invokeArgs($logic, [
            $params['user_id'],
            $params['activity_id'],
            $params['expire_time'],
            $params['pattern_ids']
        ]);
        $sql = CouponCollection::getLastSql();
        Db::rollback();


        // 预期执行的SQL
        $i = [];
        foreach ((array)$params['pattern_ids'] as $pattern_id) {
            $i[] = $pattern_id;
            $i[] = $params['activity_id'];
            $i[] = $params['user_id'];
            $i[] = $params['expire_time'];
            $i[] = date('Y-m-d H:i:s');
        }
        $expected_sql = sprintf(
            "INSERT INTO `coupon_collection` (`pattern_id` , `activity_id` , `user_id` , `status` , `expire_time` , `create_time`) " .
            "VALUES ( %d,%d,'%d',0,'%s','%s' ) , " .
            "( %d,%d,'%d',0,'%s','%s' ) ," .
            " ( %d,%d,'%d',0,'%s','%s' )",
            ...$i
        );
        $this->assertSame($expected_sql, $sql);
    }

    /**
     * @testdox 活动优惠券已领取次数 +1
     */
    public function testIncreaseGrantCount()
    {
        $logicUser = new AppletLoginUser([]);
        $request = new ReceiveCouponRequest([]);
        $logic = new ReceiveCoupon($logicUser, $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('increaseGrantCount');
        $method->setAccessible(true);

        // 执行方法
        $params = [
            'activity_id' => 2,
        ];
        // 开启事务(为了写入后回滚事务，这样能不将测试的数据写入到数据库中)
        Db::startTrans();
        $method->invokeArgs($logic, [$params['activity_id']]);
        $sql = CouponActivity::getLastSql();
        Db::rollback();


        // 预期执行的SQL
        $expected_sql = sprintf(
            "UPDATE `coupon_activity`  SET `grant_num` = grant_num+1 , `update_time` = '%s'  WHERE  `id` = %d",
            date('Y-m-d H:i:s'),
            $params['activity_id']
        );
        $this->assertSame($expected_sql, $sql);
    }

    /**
     * @testdox 测试完整的逻辑 - 这个活动的优惠券用户已经领取过了
     */
    public function testCompleteLogicScene1()
    {
        $params = [
            'activity_id' => 1
        ];
        $logicUser = new AppletLoginUser(['id' => 100000001]);
        $app = $this->init_post_request($params, $logicUser);

        // 设置没有领取过
        $couponCollectionModel = $this->createStub(CouponCollection::class);
        $couponCollectionModel->method('isReceive')->willReturn(true);
        // 防止将测试数据写入数据库
        $couponCollectionModel->method('batchInserts')->willReturn(3);
        bind(CouponCollection::class, $couponCollectionModel);

        // 预设活动数据
        $couponActivityModel = $this->createStub(CouponActivityModel::class);
        $couponActivityModel->method('getActivity')->willReturn([
            'status' => CouponActivityModel::StatusGrant,
            'is_del' => CouponActivityModel::IsDelNot,
            'grant_max_count' => 100,
            'grant_num' => 0,
            'start_time' => date('Y-m-d H:i:s'),
            'end_time' => date('Y-m-d H:i:s', strtotime('+1 day')),
            'effective_days' => 30
        ]);
        // 防止将测试数据写入数据库
        $couponActivityModel->method('increaseGrantCount')->willReturn(true);
        bind(CouponActivityModel::class, $couponActivityModel);
        // 预设活动关联的场站有3个。
        $couponPatternModel = $this->createStub(CouponPattern::class);
        $couponPatternModel->method('getActivityAllPatternId')->willReturn([
            1, 2, 3
        ]);
        bind(CouponPattern::class, $couponPatternModel);

        $controller = new \app\applet\controller\CouponActivity($app);
        $response = $controller->receive_coupon();

        $this->assertIsArray($response->getData());
        $responseBody = $response->getData();
        $this->assertArrayHasKey('code', $responseBody);
        $this->assertArrayHasKey('data', $responseBody);
        $this->assertArrayHasKey('msg', $responseBody);
        $this->assertIsInt($responseBody['code']);
        $this->assertIsArray($responseBody['data']);
        $this->assertIsString($responseBody['msg']);
        $this->assertSame("当前活动的优惠券您已经领取过了！", $responseBody['msg']);
        $this->assertSame(100, $responseBody['code']);
    }

    /**
     * @testdox 测试完整的逻辑 - 无效的活动ID(没有找到对应的活动数据)
     */
    public function testCompleteLogicScene2()
    {
        $params = [
            'activity_id' => 1
        ];
        $logicUser = new AppletLoginUser(['id' => 100000001]);
        $app = $this->init_post_request($params, $logicUser);

        // 设置没有领取过
        $couponCollectionModel = $this->createStub(CouponCollection::class);
        $couponCollectionModel->method('isReceive')->willReturn(false);
        // 防止将测试数据写入数据库
        $couponCollectionModel->method('batchInserts')->willReturn(3);
        bind(CouponCollection::class, $couponCollectionModel);

        // 预设活动数据
        $couponActivityModel = $this->createStub(CouponActivityModel::class);
        $couponActivityModel->method('getActivity')->willReturn(null);
//        $couponActivityModel->method('getActivity')->willReturn([
//            'status' => CouponActivityModel::StatusGrant,
//            'is_del' => CouponActivityModel::IsDelYes,
//            'grant_max_count' => 100,
//            'grant_num' => 0,
//            'start_time' => date('Y-m-d H:i:s'),
//            'end_time' => date('Y-m-d H:i:s', strtotime('+1 day')),
//            'effective_days' => 30
//        ]);
        // 防止将测试数据写入数据库
        $couponActivityModel->method('increaseGrantCount')->willReturn(true);
        bind(CouponActivityModel::class, $couponActivityModel);
        // 预设活动关联的场站有3个。
        $couponPatternModel = $this->createStub(CouponPattern::class);
        $couponPatternModel->method('getActivityAllPatternId')->willReturn([
            1, 2, 3
        ]);
        bind(CouponPattern::class, $couponPatternModel);

        $controller = new \app\applet\controller\CouponActivity($app);
        $response = $controller->receive_coupon();

        $this->assertIsArray($response->getData());
        $responseBody = $response->getData();
        $this->assertArrayHasKey('code', $responseBody);
        $this->assertArrayHasKey('data', $responseBody);
        $this->assertArrayHasKey('msg', $responseBody);
        $this->assertIsInt($responseBody['code']);
        $this->assertIsArray($responseBody['data']);
        $this->assertIsString($responseBody['msg']);

        $this->assertSame("无效活动ID", $responseBody['msg']);
        $this->assertSame(100, $responseBody['code']);
    }


    /**
     * @testdox 测试完整的逻辑 - 活动已经被删除
     */
    public function testCompleteLogicScene3()
    {
        $params = [
            'activity_id' => 1
        ];
        $logicUser = new AppletLoginUser(['id' => 100000001]);
        $app = $this->init_post_request($params, $logicUser);

        // 设置没有领取过
        $couponCollectionModel = $this->createStub(CouponCollection::class);
        $couponCollectionModel->method('isReceive')->willReturn(false);
        // 防止将测试数据写入数据库
        $couponCollectionModel->method('batchInserts')->willReturn(3);
        bind(CouponCollection::class, $couponCollectionModel);

        // 预设活动数据
        $couponActivityModel = $this->createStub(CouponActivityModel::class);
//        $couponActivityModel->method('getActivity')->willReturn(null);
        $couponActivityModel->method('getActivity')->willReturn([
            'status' => CouponActivityModel::StatusGrant,
            'is_del' => CouponActivityModel::IsDelYes,
            'grant_max_count' => 100,
            'grant_num' => 0,
            'start_time' => date('Y-m-d H:i:s'),
            'end_time' => date('Y-m-d H:i:s', strtotime('+1 day')),
            'effective_days' => 30
        ]);
        // 防止将测试数据写入数据库
        $couponActivityModel->method('increaseGrantCount')->willReturn(true);
        bind(CouponActivityModel::class, $couponActivityModel);
        // 预设活动关联的场站有3个。
        $couponPatternModel = $this->createStub(CouponPattern::class);
        $couponPatternModel->method('getActivityAllPatternId')->willReturn([
            1, 2, 3
        ]);
        bind(CouponPattern::class, $couponPatternModel);

        $controller = new \app\applet\controller\CouponActivity($app);
        $response = $controller->receive_coupon();

        $this->assertIsArray($response->getData());
        $responseBody = $response->getData();
        $this->assertArrayHasKey('code', $responseBody);
        $this->assertArrayHasKey('data', $responseBody);
        $this->assertArrayHasKey('msg', $responseBody);
        $this->assertIsInt($responseBody['code']);
        $this->assertIsArray($responseBody['data']);
        $this->assertIsString($responseBody['msg']);

        $this->assertSame("当前活动已被删除", $responseBody['msg']);
        $this->assertSame(100, $responseBody['code']);
    }

    /**
     * @testdox 测试完整的逻辑 - 活动已经被删除
     */
    public function testCompleteLogicScene4()
    {
        $params = [
            'activity_id' => 1
        ];
        $logicUser = new AppletLoginUser(['id' => 100000001]);
        $app = $this->init_post_request($params, $logicUser);

        // 设置没有领取过
        $couponCollectionModel = $this->createStub(CouponCollection::class);
        $couponCollectionModel->method('isReceive')->willReturn(false);
        // 防止将测试数据写入数据库
        $couponCollectionModel->method('batchInserts')->willReturn(3);
        bind(CouponCollection::class, $couponCollectionModel);

        // 预设活动数据
        $couponActivityModel = $this->createStub(CouponActivityModel::class);
//        $couponActivityModel->method('getActivity')->willReturn(null);
        $couponActivityModel->method('getActivity')->willReturn([
            'status' => CouponActivityModel::StatusGrant,
            'is_del' => CouponActivityModel::IsDelYes,
            'grant_max_count' => 100,
            'grant_num' => 0,
            'start_time' => date('Y-m-d H:i:s'),
            'end_time' => date('Y-m-d H:i:s', strtotime('+1 day')),
            'effective_days' => 30
        ]);
        // 防止将测试数据写入数据库
        $couponActivityModel->method('increaseGrantCount')->willReturn(true);
        bind(CouponActivityModel::class, $couponActivityModel);
        // 预设活动关联的场站有3个。
        $couponPatternModel = $this->createStub(CouponPattern::class);
        $couponPatternModel->method('getActivityAllPatternId')->willReturn([
            1, 2, 3
        ]);
        bind(CouponPattern::class, $couponPatternModel);

        $controller = new \app\applet\controller\CouponActivity($app);
        $response = $controller->receive_coupon();

        $this->assertIsArray($response->getData());
        $responseBody = $response->getData();
        $this->assertArrayHasKey('code', $responseBody);
        $this->assertArrayHasKey('data', $responseBody);
        $this->assertArrayHasKey('msg', $responseBody);
        $this->assertIsInt($responseBody['code']);
        $this->assertIsArray($responseBody['data']);
        $this->assertIsString($responseBody['msg']);

        $this->assertSame("当前活动已被删除", $responseBody['msg']);
        $this->assertSame(100, $responseBody['code']);
    }

    protected function init_post_request(array $params, AppletLoginUser $loginUser): App
    {
        // 初始化应用
        $app = new App();
        $app->initialize();

        // 初始化请求对象并设置请求参数
        $request = new Request();
        $request->setMethod('POST');
        $request->withPost($params);
        $request->appletLoginUser = $loginUser;
        $postParams = $request->post();
        $this->assertIsArray($postParams);
        $this->assertSame($params, $postParams);

        // 将请求对象绑定到应用中
        $app->bind(Request::class, $request);
        $this->assertInstanceOf(Request::class, $app->request);
        $this->assertIsArray($app->request->post());
        $this->assertSame($params, $app->request->post());

        return $app;
    }


}