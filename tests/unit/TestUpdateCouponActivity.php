<?php

namespace tests\unit;

use app\common\cache\redis\entity\AdminLoginUser;
use app\common\lib\exception\RuntimeException;
use app\common\lib\VerifyData;
use app\common\logic\admin\coupon_activity\UpdateActivity;
use app\common\logic\admin\entity\UpdateActivityRequest;
use app\common\model\CouponActivity;
use app\common\model\Stations;
use PHPUnit\Framework\TestCase;
use ReflectionClass;
use ReflectionException;
use Respect\Validation\Validator as v;
use think\Request;
use Throwable;
use think\facade\Db;

class TestUpdateCouponActivity extends TestCase
{
    public function testVerifyParamsID()
    {
        // 参数要求：
        // id: 数值类型 最小值: 1

        $params = [
            'id' => '1',
        ];

        // 非数值类型
        try {
            v::input($params, VerifyData::coupon_activity([
                'id',
            ]));

            $this->fail('没有抛出验证异常');
        } catch (Throwable $e) {
            $this->assertSame('活动ID 必须是integer类型', $e->getMessage());
        }

        $params['id'] = 0;
        try {
            v::input($params, VerifyData::coupon_activity([
                'id',
            ]));
            $this->fail('没有抛出验证异常');
        } catch (Throwable $e) {
            $this->assertSame('活动ID 必须大于或等于 1', $e->getMessage());
        }


        // 验证合法的名称
        $params['id'] = 1;
        try {
            $verifyData = v::input($params, VerifyData::coupon_activity([
                'id',
            ]));
            $this->assertSame($params, $verifyData);
        } catch (Throwable $e) {
            $this->fail('验证失败');
        }
    }

    public function testVerifyParamsName()
    {
        // 参数要求：
        // name: 字符串类型 长度范围: 1~128


        $params = [
            'name' => ['0925'],
        ];

        // 非字符串类型
        try {
            v::input($params, VerifyData::coupon_activity([
                'name',
            ]));

            $this->fail('没有抛出验证异常');
        } catch (Throwable $e) {
            $this->assertSame('活动名称 必须是string类型', $e->getMessage());
        }


        // 验证名称超出最大长度
        $params['name'] = str_repeat('测', 129);
        try {
            v::input($params, VerifyData::coupon_activity([
                'name',
            ]));
            $this->fail('没有抛出验证异常');
        } catch (Throwable $e) {
            $this->assertSame('活动名称 长度必须在 1 与 128 之间', $e->getMessage());
        }

        // 验证名称低于最小长度
        $params['name'] = '';
        try {
            v::input($params, VerifyData::coupon_activity([
                'name',
            ]));
            $this->fail('没有抛出验证异常');
        } catch (Throwable $e) {
            $this->assertSame('活动名称 不能为空', $e->getMessage());
        }

        // 验证合法的名称
        $params['name'] = '新站优惠';
        try {
            $verifyData = v::input($params, VerifyData::coupon_activity([
                'name',
            ]));
            $this->assertSame($params, $verifyData);
        } catch (Throwable $e) {
            $this->fail('验证失败');
        }
    }

    public function testVerifyParamsGrantMaxCount()
    {
        // 参数要求：
        // grant_max_count: 数值类型 取值范围: 1~10000


        $params = [
            'grant_max_count' => "10000",
        ];
        try {
            v::input($params, VerifyData::coupon_activity([
                'grant_max_count',
            ]));

            $this->fail('没有抛出验证异常');
        } catch (Throwable $e) {
            $this->assertSame('活动中优惠券包发放上限 必须是integer类型', $e->getMessage());
        }


        $params['grant_max_count'] = 10001;
        try {
            v::input($params, VerifyData::coupon_activity([
                'grant_max_count',
            ]));
            $this->fail('没有抛出验证异常');
        } catch (Throwable $e) {
            $this->assertSame('活动中优惠券包发放上限 必须小于或等于 10000', $e->getMessage());
        }

        $params['grant_max_count'] = 0;
        try {
            v::input($params, VerifyData::coupon_activity([
                'grant_max_count',
            ]));
            $this->fail('没有抛出验证异常');
        } catch (Throwable $e) {
            $this->assertSame('活动中优惠券包发放上限 必须大于或等于 1', $e->getMessage());
        }

        $params['grant_max_count'] = 1000;
        try {
            $verifyData = v::input($params, VerifyData::coupon_activity([
                'grant_max_count',
            ]));
            $this->assertSame($params, $verifyData);
        } catch (Throwable $e) {
            $this->fail('验证失败');
        }
    }

    public function testVerifyParamsEffectiveDays()
    {
        // 参数要求：
        // effective_days: 数值类型 最小值: 1

        $params = [
            'effective_days' => "10000",
        ];
        try {
            v::input($params, VerifyData::coupon_activity([
                'effective_days',
            ]));

            $this->fail('没有抛出验证异常');
        } catch (Throwable $e) {
            $this->assertSame('领取后的有效天数 必须是integer类型', $e->getMessage());
        }


        $params['effective_days'] = 0;
        try {
            v::input($params, VerifyData::coupon_activity([
                'effective_days',
            ]));
            $this->fail('没有抛出验证异常');
        } catch (Throwable $e) {
            $this->assertSame('领取后的有效天数 必须大于或等于 1', $e->getMessage());
        }

        $params['effective_days'] = 30;
        try {
            $verifyData = v::input($params, VerifyData::coupon_activity([
                'effective_days',
            ]));
            $this->assertSame($params, $verifyData);
        } catch (Throwable $e) {
            $this->fail('验证失败');
        }
    }

    public function testVerifyParamsStartTime()
    {
        // 参数要求：
        // start_time: 日期时间类型 长度限制：19~19

        $params = [
            'start_time' => strtotime("2024-09-25 08:00:00"),
        ];
        try {
            v::input($params, VerifyData::coupon_activity([
                'start_time',
            ]));

            $this->fail('没有抛出验证异常');
        } catch (Throwable $e) {
            $this->assertSame('活动开始时间 必须是有效的日期/时间', $e->getMessage());
        }


        $params['start_time'] = "2024-09-25";
        try {
            v::input($params, VerifyData::coupon_activity([
                'start_time',
            ]));
            $this->fail('没有抛出验证异常');
        } catch (Throwable $e) {
            $this->assertSame('活动开始时间 长度必须是 19', $e->getMessage());
        }

        $params['start_time'] = "2024-09-25 08:00:00";
        try {
            $verifyData = v::input($params, VerifyData::coupon_activity([
                'start_time',
            ]));
            $this->assertSame($params, $verifyData);
        } catch (Throwable $e) {
            $this->fail('验证失败');
        }
    }

    public function testVerifyParamsEndTime()
    {
        // 参数要求：
        // end_time: 日期时间类型 长度限制：19~19

        $params = [
            'end_time' => strtotime("2024-10-25 08:00:00"),
        ];
        try {
            v::input($params, VerifyData::coupon_activity([
                'end_time',
            ]));

            $this->fail('没有抛出验证异常');
        } catch (Throwable $e) {
            $this->assertSame('活动结束时间 必须是有效的日期/时间', $e->getMessage());
        }


        $params['end_time'] = "2024-09-25";
        try {
            v::input($params, VerifyData::coupon_activity([
                'end_time',
            ]));
            $this->fail('没有抛出验证异常');
        } catch (Throwable $e) {
            $this->assertSame('活动结束时间 长度必须是 19', $e->getMessage());
        }

        $params['end_time'] = "2024-10-25 08:00:00";
        try {
            $verifyData = v::input($params, VerifyData::coupon_activity([
                'end_time',
            ]));
            $this->assertSame($params, $verifyData);
        } catch (Throwable $e) {
            $this->fail('验证失败');
        }
    }

    public function testVerifyParamsStationIds()
    {
        // 参数要求：
        // station_ids: 数组类型 元素类型必须是整数

        $params = [
            'station_ids' => strtotime("2024-10-25 08:00:00"),
        ];
        try {
            v::input($params, VerifyData::coupon_activity([
                'station_ids',
            ]));

            $this->fail('没有抛出验证异常');
        } catch (Throwable $e) {
            $this->assertSame('场站ID集合 必须是array类型', $e->getMessage());
        }


        $params['station_ids'] = [];
        try {
            $verifyData = v::input($params, VerifyData::coupon_activity([
                'station_ids',
            ]));
            $this->assertSame($params, $verifyData, '允许传递空数组');
        } catch (Throwable $e) {
            $this->fail('抛出验证异常');

        }

        $params['station_ids'] = [10001, 10002];
        try {
            $verifyData = v::input($params, VerifyData::coupon_activity([
                'station_ids',
            ]));
            $this->assertSame($params, $verifyData);
        } catch (Throwable $e) {
            var_export($e);
            $this->fail('验证失败');
        }
    }

    /**
     * @testdox 测试所有的参数校验
     *
     * @return void
     */
    public function testVerifyParamsAll()
    {
        $params = [
            'name' => '活动名称',
            'grant_max_count' => 10000,
            'effective_days' => 30,
            'start_time' => '2024-09-25 08:00:00',
            'end_time' => '2024-09-30 08:00:00',
            'station_ids' => [1001],
            'id' => 1,
        ];
        try {
            $verifyData = v::input($params, VerifyData::coupon_activity([
                'id',
                'name',
                'grant_max_count',
                'effective_days',
                'start_time',
                'end_time',
                'station_ids'
            ]));

            $this->assertSame($params, $verifyData);
        } catch (Throwable $e) {
            $this->fail('抛出验证异常');
        }
    }

    /**
     * @testdox 测试将参数导入请求对象后，参数值是否不变。
     *
     * @return void
     */
    public function testUpdateActivityRequest()
    {
        $params = [
            'id' => 1,
            'name' => '活动名称',
            'grant_max_count' => 10000,
            'effective_days' => 30,
            'start_time' => '2024-09-25 08:00:00',
            'end_time' => '2024-09-30 08:00:00',
            'station_ids' => [1001]
        ];
        $request = new UpdateActivityRequest($params);

        $this->assertSame($params['id'], $request->id);
        $this->assertSame($params['name'], $request->name);
        $this->assertSame($params['grant_max_count'], $request->grant_max_count);
        $this->assertSame($params['effective_days'], $request->effective_days);
        $this->assertSame($params['start_time'], $request->start_time);
        $this->assertSame($params['end_time'], $request->end_time);
        $this->assertSame($params['station_ids'], $request->station_ids);
    }

    /**
     * @testdox 验证活动时间不通过的场景
     *
     * @return void
     * @throws ReflectionException
     */
    public function testVerifyActivityTimeNoPass()
    {
        $request = new UpdateActivityRequest([]);
        $logic = new UpdateActivity(new AdminLoginUser([]), $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyActivityTime');
        $method->setAccessible(true);

        try {
            // 执行方法
            $method->invokeArgs($logic, ['2024-10-25 08:00:00', '2024-09-30 08:00:00']);

            $this->fail('没有抛出异常');
        } catch (RuntimeException $e) {
            $this->assertSame(RuntimeException::CodeBusinessException, $e->getCode());
            $this->assertSame("活动结束时间必须大于活动开始时间", $e->getMessage());
        }
    }

    /**
     * @testdox 验证活动时间通过的场景
     *
     * @return void
     * @throws ReflectionException
     */
    public function testVerifyActivityTimePass()
    {
        $request = new UpdateActivityRequest([]);
        $logic = new UpdateActivity(new AdminLoginUser([]), $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyActivityTime');
        $method->setAccessible(true);

        try {
            // 执行方法
            $method->invokeArgs($logic, ['2024-09-25 08:00:00', '2024-09-30 08:00:00']);

            $this->assertTrue(true, '验证通过');
        } catch (RuntimeException $e) {
            $this->fail('抛出异常');
        }
    }


    public function testVerifyOperationPermission()
    {
        $request = new UpdateActivityRequest([]);
        $logic = new UpdateActivity(new AdminLoginUser([]), $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyOperationPermission');
        $method->setAccessible(true);

        try {
            // 执行方法
            $method->invokeArgs($logic, [3, 1]);

            $this->fail('没有抛出异常');
        } catch (RuntimeException $e) {
            $this->assertSame(RuntimeException::CodeBusinessException, $e->getCode());
            $this->assertSame("只有创建活动的用户才允许操作", $e->getMessage());
        }


        try {
            // 执行方法
            $method->invokeArgs($logic, [3, 3]);

            $this->assertTrue(true, '预期不会抛出异常');
        } catch (RuntimeException $e) {
            $this->fail('预期不会抛出异常,实际抛出了异常');
        }
    }

    public function testVerifyActivityIsCanUpdate()
    {
        $request = new UpdateActivityRequest([]);
        $logic = new UpdateActivity(new AdminLoginUser([]), $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyActivityIsCanUpdate');
        $method->setAccessible(true);

        try {
            // 执行方法
            $method->invokeArgs($logic, [CouponActivity::StatusReview]);

            $this->fail('预期是会抛出异常的，不过实际没有抛出。');
        } catch (RuntimeException $e) {
            $this->assertSame(RuntimeException::CodeBusinessException, $e->getCode());
            $this->assertSame("只有当活动状态处于创建中才允许更新。", $e->getMessage());
        }
    }


    /**
     * @testdox 测试验证场站ID集合合法性 - 验证不通过场景
     */
    public function testVerifyStationIdsLegitimacyNoPass()
    {
        $request = new UpdateActivityRequest([]);
        $logic = new UpdateActivity(new AdminLoginUser([]), $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyStationIdsLegitimacy');
        $method->setAccessible(true);


        // 创建存根来替换真实的数据库查询
        $stationsModel = $this->createStub(Stations::class);
        $stationsModel->method('getStationIds')->willReturn([1013, 1014]);
        bind(Stations::class, $stationsModel);


        try {
            // 执行方法
            $method->invokeArgs($logic, [100001, [1013, 1014, 1015]]);

            $this->fail('没有抛出异常');
        } catch (RuntimeException $e) {
            $this->assertSame(RuntimeException::CodeBusinessException, $e->getCode());
            $this->assertSame("场站ID集合中存在无效的ID：1015", $e->getMessage());
        }
    }

    /**
     * @testdox 测试验证场站ID集合合法性 - 验证通过场景
     */
    public function testVerifyStationIdsLegitimacyPass()
    {
        $request = new UpdateActivityRequest([]);
        $logic = new UpdateActivity(new AdminLoginUser([]), $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyStationIdsLegitimacy');
        $method->setAccessible(true);


        // 创建存根来替换真实的数据库查询
        $stationsModel = $this->createStub(Stations::class);
        $stationsModel->method('getStationIds')->willReturn([1013, 1014, 1015]);
        bind(Stations::class, $stationsModel);


        try {
            // 执行方法
            $method->invokeArgs($logic, [100001, [1013, 1014, 1015]]);

            $this->assertTrue(true, '验证通过');
        } catch (RuntimeException $e) {
            $this->fail('抛出异常');
        }
    }


    /**
     * @testdox 获取新添加的场站ID集合
     */
    public function testGetNewAddStationIds()
    {
        $request = new UpdateActivityRequest([]);
        $logic = new UpdateActivity(new AdminLoginUser([]), $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('getNewAddStationIds');
        $method->setAccessible(true);


        // 执行方法
        $old_station_ids = [1013, 1014];
        $new_station_ids = [1013, 1014, 1015];
        $new_add_station_ids = $method->invokeArgs($logic, [$old_station_ids, $new_station_ids]);
        $this->assertIsArray($new_add_station_ids);
        $this->assertSame([1015], $new_add_station_ids);


        $old_station_ids = [1013, 1016, 1017];
        $new_station_ids = [1013, 1014, 1015];
        $new_add_station_ids = $method->invokeArgs($logic, [$old_station_ids, $new_station_ids]);
        $this->assertIsArray($new_add_station_ids);
        $this->assertSame([1014, 1015], $new_add_station_ids);
    }


    /**
     * @testdox 添加新关联的场站
     */
    public function testBatchAddRelationStationId()
    {
        $request = new UpdateActivityRequest([]);
        $logic = new UpdateActivity(new AdminLoginUser([]), $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('batchAddRelationStationId');
        $method->setAccessible(true);

        $activity_id = 10;
        $new_add_station_ids = [101, 102, 302];

        // 执行方法
        Db::startTrans();
        $row = $method->invokeArgs($logic, [$activity_id, $new_add_station_ids]);
        Db::rollback();
        $sql = Db::getLastSql();

        $this->assertIsInt($row);
        $this->assertSame(count($new_add_station_ids), $row);


        $expected_sql = "INSERT INTO `coupon_activity_station` (`activity_id` , `station_id` , `create_time`) VALUES ";
        foreach ($new_add_station_ids as $index => $station_id) {
            if ($index > 0) {
                $expected_sql .= " , ";
            }
            $expected_sql .= sprintf("( %d,%d,'%s' )", $activity_id, $station_id, date('Y-m-d H:i:s'));
        }
        $this->assertSame($expected_sql, $sql);
    }

    /**
     * @testdox 获取不在关联的场站ID集合
     */
    public function testGetRemoveStationIds()
    {
        $request = new UpdateActivityRequest([]);
        $logic = new UpdateActivity(new AdminLoginUser([]), $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('getRemoveStationIds');
        $method->setAccessible(true);


        // 执行方法
        $old_station_ids = [1013, 1014];
        $new_station_ids = [1013, 1014, 1015];
        $new_add_station_ids = $method->invokeArgs($logic, [$old_station_ids, $new_station_ids]);
        $this->assertIsArray($new_add_station_ids);
        $this->assertSame([], $new_add_station_ids);

        $old_station_ids = [1013, 1017, 1016];
        $new_station_ids = [1013, 1014, 1015];
        $new_add_station_ids = $method->invokeArgs($logic, [$old_station_ids, $new_station_ids]);
        $this->assertIsArray($new_add_station_ids);
        $this->assertSame([1017, 1016], $new_add_station_ids);
    }

    /**
     * @testdox 删除不在关联的场站
     */
    public function testBatchRemoveRelationStationId()
    {
        $request = new UpdateActivityRequest([]);
        $logic = new UpdateActivity(new AdminLoginUser([]), $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('batchRemoveRelationStationId');
        $method->setAccessible(true);

        $activity_id = 10;
        $remove_station_ids = [101, 102, 302];

        // 执行方法
        Db::startTrans();
        $method->invokeArgs($logic, [$activity_id, $remove_station_ids]);
        Db::rollback();
        $sql = Db::getLastSql();


        $expected_sql = sprintf(
            "DELETE FROM `coupon_activity_station` WHERE  `activity_id` = %d  AND `station_id` IN (%s)",
            $activity_id, implode(",", $remove_station_ids)
        );
        $this->assertSame($expected_sql, $sql);
    }

    /**
     * @testdox 记录操作日志
     */
    public function testAddOperationLog()
    {
        $request = new UpdateActivityRequest([]);
        $logic = new UpdateActivity(new AdminLoginUser([]), $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('addOperationLog');
        $method->setAccessible(true);

        $params = [
            'corp_id' => 10001,
            'user_id' => 2,
            'op_interface' => '/admin/coupon_activity/update_activity',
            'op_content' => [
                'id' => 1,
                'name' => '活动名称',
                'grant_max_count' => 1002,
                'effective_days' => 30,
                'start_time' => date('Y-m-d H:i:s', strtotime('+1 day')),
                'end_time' => date('Y-m-d H:i:s', strtotime('+10 day')),
                'station_ids' => [1001, 1003, 1004]
            ]
        ];

        // 执行方法
        Db::startTrans();
        // int $corp_id, int $user_id, int $user_type, string $op_interface, string $op_content
        $method->invokeArgs($logic, [
            $params['corp_id'], $params['user_id'],
            $params['op_interface'], $params['op_content']
        ]);
        Db::rollback();
        $sql = Db::getLastSql();

        // 断言
        $expected_sql = sprintf(
            "INSERT INTO `coupon_oplog` SET `corp_id` = '%d' , `user_id` = '%d' , `user_type` = 0 , `op_interface` = '%s' , `op_content` = '%s' , `op_time` = '%s'",
            $params['corp_id'], $params['user_id'], $params['op_interface'], addslashes(json_encode($params['op_content'])), date('Y-m-d H:i:s')
        );

        $this->assertSame($expected_sql, $sql);
    }


}