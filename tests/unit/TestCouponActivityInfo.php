<?php

namespace tests\unit;

use app\common\cache\redis\entity\AdminLoginUser;
use app\common\lib\exception\RuntimeException;
use app\common\lib\VerifyData;
use app\common\logic\admin\coupon_activity\ActivityInfo;
use app\common\logic\admin\coupon_activity\ActivityList;
use app\common\logic\admin\coupon_activity\CreateActivity;
use app\common\logic\admin\entity\ActivityInfoRequest;
use app\common\logic\admin\entity\ActivityListRequest;
use app\common\logic\admin\entity\CreateActivityRequest;
use app\common\logic\admin\entity\DeleteActivityRequest;
use app\common\model\CouponActivity;
use app\common\model\CouponActivity as CouponActivityModel;
use app\common\model\CouponActivityStation;
use app\common\model\Stations;
use PHPUnit\Framework\TestCase;
use ReflectionClass;
use ReflectionException;
use Respect\Validation\Validator as v;
use Throwable;

/**
 * @testdox 优惠券活动详情
 */
class TestCouponActivityInfo extends TestCase
{
    /**
     * @testdox 验证参数 - (activity_id)活动ID - 不传递时会验证不通过。
     */
    public function testVerifyParamsActivityIdScene1()
    {
        $params = [];

        try {
            v::input($params, VerifyData::coupon_activity([
                'activity_id',
            ]));
            $this->fail();
        } catch (Throwable $e) {
            $this->assertSame('活动ID 不能是可选的', $e->getMessage());
        }
    }

    /**
     * @testdox 验证参数 - (activity_id)活动ID - 传递非数值时验证不通过。
     */
    public function testVerifyParamsActivityIdScene2()
    {
        $params = [
            'activity_id' => '1'
        ];

        try {
            v::input($params, VerifyData::coupon_activity([
                'activity_id',
            ]));
            $this->fail();
        } catch (Throwable $e) {
            $this->assertSame('活动ID 必须是integer类型', $e->getMessage());
        }
    }

    /**
     * @testdox 验证参数 - (activity_id)活动ID - 传递低于1的数值时会验证不通过。
     */
    public function testVerifyParamsActivityIdScene3()
    {
        $params = [
            'activity_id' => -1
        ];

        try {
            v::input($params, VerifyData::coupon_activity([
                'activity_id',
            ]));
            $this->fail();
        } catch (Throwable $e) {
            $this->assertSame('活动ID 必须大于或等于 1', $e->getMessage());
        }
    }

    /**
     * @testdox 验证参数 - (activity_id)活动ID - 传递符合预期的数值，验证通过。
     */
    public function testVerifyParamsActivityIdScene4()
    {
        $params = [
            'activity_id' => 1
        ];

        try {
            $verify_params = v::input($params, VerifyData::coupon_activity([
                'activity_id',
            ]));

            $this->assertSame($params, $verify_params);
        } catch (Throwable $e) {
            $this->fail('不应该会抛出异常的');
        }
    }

    /**
     * @testdox 创建活动详情的请求对象
     * @return void
     */
    public function testCreateActivityInfoRequest()
    {
        $params = [
            'activity_id' => 1
        ];
        $request = new ActivityInfoRequest($params);
        $this->assertSame($params['activity_id'], $request->activity_id);
        $this->assertSame($params, $request->toArray());
    }

    /**
     * @testdox 增加数据权限过滤条件 - 超级管理员身份 或 其他非运营商账号身份
     */
    public function testAddDataPermissionsFilterConditionsScene1()
    {
        $request = new ActivityInfoRequest([]);
        $logic = new ActivityInfo(new AdminLoginUser([]), $request);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('addDataPermissionsFilterConditions');
        $method->setAccessible(true);

        $result = $method->invokeArgs($logic, [new AdminLoginUser(['pid' => 0, 'corp_id' => 0]), []]);
        $this->assertIsArray($result);
        $this->assertSame([], $result);
    }

    /**
     * @testdox 增加数据权限过滤条件 - 运营商主账号身份 或 运营商子账号身份
     */
    public function testAddDataPermissionsFilterConditionsScene2()
    {
        $request = new ActivityInfoRequest([]);
        $logic = new ActivityInfo(new AdminLoginUser([]), $request);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('addDataPermissionsFilterConditions');
        $method->setAccessible(true);

        $result = $method->invokeArgs($logic, [new AdminLoginUser(['pid' => 0, 'corp_id' => 10001]), []]);
        $this->assertIsArray($result);
        $this->assertSame(['filter_corp_id' => 10001], $result);
    }

    /**
     * @testdox 整理过滤条件 - 没有传递任何过滤条件
     * @return void
     * @throws ReflectionException
     */
    public function testArrangeFiltersScene1()
    {
        $request = new ActivityInfoRequest([]);
        $logic = new ActivityInfo(new AdminLoginUser([]), $request);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('arrangeFilters');
        $method->setAccessible(true);

        // 预期的结果
        $expect_filters = [
            ['is_del', '=', CouponActivity::IsDelNot]
        ];
        // 传递的参数
        $params = [];

        $filters = $method->invokeArgs($logic, [$params]);
        $this->assertIsArray($filters);
        $this->assertSame($expect_filters, $filters);
    }

    /**
     * @testdox 整理过滤条件 - 只传递了运营商ID
     * @return void
     * @throws ReflectionException
     */
    public function testArrangeFiltersScene2()
    {
        $request = new ActivityInfoRequest([]);
        $logic = new ActivityInfo(new AdminLoginUser([]), $request);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('arrangeFilters');
        $method->setAccessible(true);

        // 预期的结果
        $expect_filters = [
            ['is_del', '=', CouponActivity::IsDelNot],
            ['corp_id', '=', 10001]
        ];
        // 传递的参数
        $params = [
            'filter_corp_id' => 10001
        ];

        $filters = $method->invokeArgs($logic, [$params]);
        $this->assertIsArray($filters);
        $this->assertSame($expect_filters, $filters);
    }


    /**
     * @testdox 整理过滤条件 - 只传递了活动ID
     * @return void
     * @throws ReflectionException
     */
    public function testArrangeFiltersScene3()
    {
        $request = new ActivityInfoRequest([]);
        $logic = new ActivityInfo(new AdminLoginUser([]), $request);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('arrangeFilters');
        $method->setAccessible(true);

        // 预期的结果
        $expect_filters = [
            ['is_del', '=', CouponActivity::IsDelNot],
            ['id', '=', 1]
        ];
        // 传递的参数
        $params = [
            'activity_id' => 1
        ];

        $filters = $method->invokeArgs($logic, [$params]);
        $this->assertIsArray($filters);
        $this->assertSame($expect_filters, $filters);
    }

    /**
     * @testdox 整理过滤条件 - 传递了所有的过滤参数
     * @return void
     * @throws ReflectionException
     */
    public function testArrangeFiltersScene4()
    {
        $request = new ActivityInfoRequest([]);
        $logic = new ActivityInfo(new AdminLoginUser([]), $request);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('arrangeFilters');
        $method->setAccessible(true);

        // 预期的结果
        $expect_filters = [
            ['is_del', '=', CouponActivity::IsDelNot],
            ['corp_id', '=', 10001],
            ['id', '=', 1],
        ];
        // 传递的参数
        $params = [
            'activity_id' => 1,
            'filter_corp_id' => 10001
        ];

        $filters = $method->invokeArgs($logic, [$params]);
        $this->assertIsArray($filters);
        $this->assertSame($expect_filters, $filters);
    }


    /**
     * @testdox 获取活动数据 - 没有查询到符合条件的数据
     * @return void
     * @throws ReflectionException
     */
    public function testGetActivityInfoScene1()
    {
        $request = new ActivityInfoRequest([]);
        $logic = new ActivityInfo(new AdminLoginUser([]), $request);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('getActivityInfo');
        $method->setAccessible(true);

        // 模拟场景：没有查询出活动信息
        $couponActivityModel = $this->createStub(CouponActivity::class);
        $couponActivityModel->method('getActivityInfo')->willReturn(null);
        bind(CouponActivity::class, $couponActivityModel);

        $this->expectException(RuntimeException::class);
        $this->expectExceptionCode(RuntimeException::CodeBusinessException);
        $this->expectExceptionMessage("无效活动ID");

        $method->invokeArgs($logic, [[]]);
    }

    /**
     * @testdox 获取活动数据 - 查询到了符合条件的数据
     * @return void
     * @throws ReflectionException
     */
    public function testGetActivityInfoScene2()
    {
        $request = new ActivityInfoRequest([]);
        $logic = new ActivityInfo(new AdminLoginUser([]), $request);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('getActivityInfo');
        $method->setAccessible(true);

        // 模拟场景：没有查询出活动信息
        $return = [
            'id' => 1,
            'name' => '活动名称',
            'corp_id' => 10001,
            'user_id' => 2,
            'status' => CouponActivity::StatusCreating,
            'grant_max_count' => 10000,
            'effective_days' => 30,
            'qcode_url' => '',
            'grant_num' => 0,
            'start_time' => '2024-09-26 08:00:00',
            'end_time' => '2024-09-30 08:00:00',
            'create_time' => '2024-09-26 08:00:00',
            'update_time' => null
        ];
        $couponActivityModel = $this->createStub(CouponActivity::class);
        $couponActivityModel->method('getActivityInfo')->willReturn($return);
        bind(CouponActivity::class, $couponActivityModel);

        $result = $method->invokeArgs($logic, [[]]);
        $this->assertSame($return, $result);
    }

    /**
     * @testdox 填充关联的场站ID集合 - 验证查询的SQL
     * @return void
     * @throws ReflectionException
     */
    public function testFillRelationStationIdsScene1()
    {
        $request = new ActivityInfoRequest([]);
        $logic = new ActivityInfo(new AdminLoginUser([]), $request);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('fillRelationStationIds');
        $method->setAccessible(true);

        $params = [
            'id' => 1,
        ];
        $expected_sql = sprintf(
            "SELECT `station_id` FROM `coupon_activity_station` WHERE  `activity_id` = %d",
            $params['id']
        );

        $method->invokeArgs($logic, [$params]);

        $sql = CouponActivityStation::getLastSql();

        $this->assertSame($expected_sql, $sql);
    }


    /**
     * @testdox 填充关联的场站ID集合 - 验证返回结果
     * @return void
     * @throws ReflectionException
     */
    public function testFillRelationStationIdsScene2()
    {
        $request = new ActivityInfoRequest([]);
        $logic = new ActivityInfo(new AdminLoginUser([]), $request);

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new \ReflectionClass($logic);
        $method = $reflectionClass->getMethod('fillRelationStationIds');
        $method->setAccessible(true);

        $couponActivityStationModel = $this->createStub(CouponActivityStation::class);
        $couponActivityStationModel->method('getActivityStationIds')->willReturn([1, 2, 3]);
        bind(CouponActivityStation::class, $couponActivityStationModel);

        $params = [
            'id' => 1,
            'name' => '活动名称',
            'corp_id' => 10001,
            'user_id' => 2,
            'status' => CouponActivity::StatusCreating,
            'grant_max_count' => 10000,
            'effective_days' => 30,
            'qcode_url' => '',
            'grant_num' => 0,
            'start_time' => '2024-09-26 08:00:00',
            'end_time' => '2024-09-30 08:00:00',
            'create_time' => '2024-09-26 08:00:00',
            'update_time' => null
        ];
        $expected_result = [
            'id' => 1,
            'name' => '活动名称',
            'corp_id' => 10001,
            'user_id' => 2,
            'status' => CouponActivity::StatusCreating,
            'grant_max_count' => 10000,
            'effective_days' => 30,
            'qcode_url' => '',
            'grant_num' => 0,
            'start_time' => '2024-09-26 08:00:00',
            'end_time' => '2024-09-30 08:00:00',
            'create_time' => '2024-09-26 08:00:00',
            'update_time' => null,
            'station_ids' => [1, 2, 3]
        ];

        $result = $method->invokeArgs($logic, [$params]);

        $this->assertSame($expected_result, $result);
    }
}