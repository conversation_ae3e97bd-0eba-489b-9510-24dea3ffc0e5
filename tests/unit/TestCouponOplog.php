<?php


use app\common\cache\redis\entity\AdminLoginUser;
use app\common\lib\exception\RuntimeException;
use app\common\lib\VerifyData;
use app\common\logic\admin\coupon_activity\ActivityInfo;
use app\common\logic\admin\coupon_collection\CouponCollectionInfo;
use app\common\logic\admin\coupon_collection\entity\CouponCollectionInfoRequest;
use app\common\logic\admin\entity\ActivityInfoRequest;
use app\common\model\CouponActivity;
use app\common\model\CouponActivityStation;
use app\common\model\CouponCollection;
use app\common\model\CouponOplog;
use PHPUnit\Framework\TestCase;
use Respect\Validation\Validator as v;
use think\facade\Db;

/**
 * @testdox 优惠券操作记录表
 */
class TestCouponOplog extends TestCase
{
    /**
     * @testdox 验证添加日志的SQL
     */
    public function testAddLogSql()
    {
        $params = [
            'corp_id' => 1001,
            'user_id' => 1,
            'user_type' => CouponOplog::UserTypeAdmin,
            'op_interface' => '/aaaa/aaaaaaa/aaaa',
            'op_content' => json_encode([
                'name' => 111,
                'sex' => 1
            ]),
        ];

        $CouponOplogModel = app(CouponOplog::class);
        Db::startTrans();
        $id = $CouponOplogModel->addLog(
            $params['corp_id'], $params['user_id'], $params['user_type'],
            $params['op_interface'], $params['op_content']
        );
        Db::rollback();
        $sql = Db::getLastSql();

        $this->assertIsInt($id);
        $expected_sql = sprintf(
            "INSERT INTO `coupon_oplog` SET `corp_id` = '%s' , `user_id` = '%s' , `user_type` = %d , `op_interface` = '%s' , `op_content` = '%s' , `op_time` = '%s'",
            $params['corp_id'], $params['user_id'], $params['user_type'],
            $params['op_interface'], addslashes($params['op_content']), date('Y-m-d H:i:s')
        );
        $this->assertSame($expected_sql, $sql);
    }


}