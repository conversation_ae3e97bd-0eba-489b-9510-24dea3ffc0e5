<?php

namespace tests\unit;

use app\common\cache\redis\entity\AdminLoginUser;
use app\common\lib\exception\RuntimeException;
use app\common\lib\VerifyData;
use app\common\logic\admin\coupon_activity\AddPattern;
use app\common\logic\admin\coupon_activity\UpdateActivity;
use app\common\logic\admin\coupon_activity\UpdatePattern;
use app\common\logic\admin\entity\AddPatternRequest;
use app\common\logic\admin\entity\UpdateActivityRequest;
use app\common\logic\admin\entity\UpdatePatternRequest;
use app\common\model\CouponActivity;
use PHPUnit\Framework\TestCase;
use ReflectionClass;
use ReflectionException;
use Respect\Validation\Validator as v;
use think\facade\Db;
use think\Request;
use Throwable;

/**
 * @testdox 更新现金券
 */
class TestUpdatePattern extends TestCase
{
    /**
     * @testdox 验证参数 - (pattern_id)现金券ID - 不传递时会验证不通过。
     */
    public function testVerifyParamsActivityIdScene1()
    {
        $params = [];

        try {
            v::input($params, VerifyData::coupon_activity([
                'pattern_id',
            ]));
            $this->fail();
        } catch (Throwable $e) {
            $this->assertSame('现金券ID 不能是可选的', $e->getMessage());
        }
    }

    /**
     * @testdox 验证参数 - (pattern_id)现金券ID - 传递非数值时验证不通过。
     */
    public function testVerifyParamsActivityIdScene2()
    {
        $params = [
            'pattern_id' => '1'
        ];

        try {
            v::input($params, VerifyData::coupon_activity([
                'pattern_id',
            ]));
            $this->fail();
        } catch (Throwable $e) {
            $this->assertSame('现金券ID 必须是integer类型', $e->getMessage());
        }
    }

    /**
     * @testdox 验证参数 - (pattern_id)现金券ID - 传递低于1的数值时会验证不通过。
     */
    public function testVerifyParamsActivityIdScene3()
    {
        $params = [
            'pattern_id' => -1
        ];

        try {
            v::input($params, VerifyData::coupon_activity([
                'pattern_id',
            ]));
            $this->fail();
        } catch (Throwable $e) {
            $this->assertSame('现金券ID 必须大于或等于 1', $e->getMessage());
        }
    }

    /**
     * @testdox 验证参数 - (pattern_id)现金券ID - 传递符合预期的数值，验证通过。
     */
    public function testVerifyParamsActivityIdScene4()
    {
        $params = [
            'pattern_id' => 1
        ];

        try {
            $verify_params = v::input($params, VerifyData::coupon_activity([
                'pattern_id',
            ]));

            $this->assertSame($params, $verify_params);
        } catch (Throwable $e) {
            $this->fail('不应该会抛出异常的');
        }
    }

    /**
     * @testdox 验证参数 - (pattern_name)现金券名称 - 不传递时，验证不通过。
     * @return void
     */
    public function testVerifyParamsPatternNameScene1()
    {
        // 参数要求：
        // pattern_name: 字符串类型 长度范围: 1~128


        $params = [];

        // 非字符串类型
        try {
            v::input($params, VerifyData::coupon_activity([
                'pattern_name',
            ]));

            $this->fail('预期会抛出异常，实际没有抛出验证异常');
        } catch (Throwable $e) {
            $this->assertSame('现金券名称 不能是可选的', $e->getMessage());
        }
    }

    /**
     * @testdox 验证参数 - (pattern_name)现金券名称 - 传递非字符串类型时，验证不通过。
     * @return void
     */
    public function testVerifyParamsPatternNameScene2()
    {
        $params = [
            'pattern_name' => 1
        ];

        // 非字符串类型
        try {
            v::input($params, VerifyData::coupon_activity([
                'pattern_name',
            ]));

            $this->fail('预期会抛出异常，实际没有抛出验证异常');
        } catch (Throwable $e) {
            $this->assertSame('现金券名称 必须是string类型', $e->getMessage());
        }
    }

    /**
     * @testdox 验证参数 - (pattern_name)现金券名称 - 当传递的名称超出最大长度时，验证不通过。
     * @return void
     */
    public function testVerifyParamsPatternNameScene3()
    {
        // 验证名称超出最大长度
        $params['pattern_name'] = str_repeat('测', 129);
        try {
            v::input($params, VerifyData::coupon_activity([
                'pattern_name',
            ]));
            $this->fail('预期会抛出异常，实际没有抛出验证异常');
        } catch (Throwable $e) {
            $this->assertSame('现金券名称 长度必须在 1 与 128 之间', $e->getMessage());
        }
    }

    /**
     * @testdox 验证参数 - (pattern_name)现金券名称 - 当传递的字段值符合预期时，验证才会通过。
     * @return void
     */
    public function testVerifyParamsPatternNameScene4()
    {

        $params['pattern_name'] = '满10元减5元';
        try {
            $verifyData = v::input($params, VerifyData::coupon_activity([
                'pattern_name',
            ]));
            $this->assertSame($params, $verifyData);
        } catch (Throwable $e) {
            $this->fail('预期不会抛出异常，实际抛出了。');
        }
    }

    /**
     * @testdox 验证参数 - (par_value)面值 - 当不传递时，验证不会通过。
     * @return void
     */
    public function testVerifyParamsParValueScene1()
    {
        $params = [];

        try {
            v::input($params, VerifyData::coupon_activity([
                'par_value',
            ]));

            $this->fail('没有抛出验证异常');
        } catch (Throwable $e) {
            $this->assertSame('面值 不能是可选的', $e->getMessage());
        }
    }

    /**
     * @testdox 验证参数 - (par_value)面值 - 当传递非数值类型时，验证不会通过。
     * @return void
     */
    public function testVerifyParamsParValueScene2()
    {
        $params = [
            'par_value' => '1'
        ];

        try {
            v::input($params, VerifyData::coupon_activity([
                'par_value',
            ]));

            $this->fail('没有抛出验证异常');
        } catch (Throwable $e) {
            $this->assertSame('面值 必须是integer类型', $e->getMessage());
        }
    }

    /**
     * @testdox 验证参数 - (par_value)面值 - 当传递的字段值低于1时，验证不会通过。
     * @return void
     */
    public function testVerifyParamsParValueScene3()
    {
        $params['par_value'] = -1;
        try {
            v::input($params, VerifyData::coupon_activity([
                'par_value',
            ]));
            $this->fail('没有抛出验证异常');
        } catch (Throwable $e) {
            $this->assertSame('面值 必须大于或等于 1', $e->getMessage());
        }
    }

    /**
     * @testdox 验证参数 - (par_value)面值 - 当传递的字段值符合预期时，验证才会通过。
     * @return void
     */
    public function testVerifyParamsParValueScene4()
    {
        $params['par_value'] = 1123;
        try {
            $verifyData = v::input($params, VerifyData::coupon_activity([
                'par_value',
            ]));
            $this->assertSame($params, $verifyData);
        } catch (Throwable $e) {
            $this->fail('预期不会抛出异常，实际抛出了。');
        }
    }


    /**
     * @testdox 验证参数 - (usage_amount)满减金额 - 当不传递时，验证不会通过。
     * @return void
     */
    public function testVerifyParamsUsageAmountScene1()
    {
        $params = [];

        $this->expectExceptionMessage('满减金额 不能是可选的');

        v::input($params, VerifyData::coupon_activity([
            'usage_amount',
        ]));
    }

    /**
     * @testdox 验证参数 - (usage_amount)满减金额 - 当传递非数值类型时，验证不会通过。
     * @return void
     */
    public function testVerifyParamsUsageAmountScene2()
    {
        $params = [
            'usage_amount' => '1',
        ];

        $this->expectExceptionMessage('满减金额 必须是integer类型');

        v::input($params, VerifyData::coupon_activity([
            'usage_amount',
        ]));
    }

    /**
     * @testdox 验证参数 - (usage_amount)满减金额 - 当不传递的数值低于1时，验证不会通过。
     * @return void
     */
    public function testVerifyParamsUsageAmountScene3()
    {
        // 验证名称超出最大长度
        $params['usage_amount'] = 0;

        $this->expectExceptionMessage('满减金额 必须大于或等于 1');

        v::input($params, VerifyData::coupon_activity([
            'usage_amount',
        ]));
    }

    /**
     * @testdox 验证参数 - (usage_amount)满减金额 - 当传递的字段值符合预期时，验证才会通过。
     * @return void
     */
    public function testVerifyParamsUsageAmountScene4()
    {
        $params['usage_amount'] = 1123;
        $verifyData = v::input($params, VerifyData::coupon_activity([
            'usage_amount',
        ]));
        $this->assertSame($params, $verifyData);
    }

    /**
     * @testdox 验证参数 - 全部参数
     * @return void
     */
    public function testVerifyParamsAll()
    {
        $params = [
            'activity_id' => 1,
            'pattern_name' => '满10元减5元',
            'par_value' => 50,
            'usage_amount' => 100,
        ];

        try {
            $verifyData = v::input($params, VerifyData::coupon_activity([
                'activity_id',
                'pattern_name',
                'par_value',
                'usage_amount',
            ]));
            $this->assertSame($params, $verifyData);
        } catch (Throwable $e) {
            $this->fail('预期不会抛出异常，实际抛出了。');
        }
    }

    /**
     * @testdox 创建更新现金券请求
     * @return void
     */
    public function testUpdatePatternRequest()
    {
        $params = [
            'pattern_id' => 1,
            'pattern_name' => '满10元减5元',
            'par_value' => 50,
            'usage_amount' => 100,
        ];
        $request = new UpdatePatternRequest($params);

        $this->assertSame($params['pattern_id'], $request->pattern_id);
        $this->assertSame($params['pattern_name'], $request->pattern_name);
        $this->assertSame($params['par_value'], $request->par_value);
        $this->assertSame($params['usage_amount'], $request->usage_amount);
    }

    /**
     * @testdox 验证操作权限 - 当操作员是超级管理员 或 非运营商账号时，预期会验证不通过。
     * @return void
     * @throws ReflectionException
     */
    public function testVerifyOperationPermissionScene1()
    {
        $request = new UpdatePatternRequest([]);
        $logic = new UpdatePattern(new AdminLoginUser([]), $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyOperationPermission');
        $method->setAccessible(true);

        $this->expectException(RuntimeException::class);
        $this->expectExceptionCode(RuntimeException::CodeBusinessException);
        $this->expectExceptionMessage('只有运营商主账号才能够更新现金券');

        $method->invokeArgs($logic, [new AdminLoginUser(['pid' => 0, 'corp_id' => 0])]);
    }

    /**
     * @testdox 验证操作权限 - 当操作员是运营商子账号时，预期会验证不通过。
     * @return void
     * @throws ReflectionException
     */
    public function testVerifyOperationPermissionScene2()
    {
        $request = new UpdatePatternRequest([]);
        $logic = new UpdatePattern(new AdminLoginUser([]), $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyOperationPermission');
        $method->setAccessible(true);

        $this->expectException(RuntimeException::class);
        $this->expectExceptionCode(RuntimeException::CodeBusinessException);
        $this->expectExceptionMessage('只有运营商主账号才能够更新现金券');

        // 执行方法
        $method->invokeArgs($logic, [new AdminLoginUser(['pid' => 2, 'corp_id' => 10001])]);
    }

    /**
     * @testdox 验证操作权限 - 当操作员是运营商主账号时，预期验证才会通过。
     * @return void
     * @throws ReflectionException
     */
    public function testVerifyOperationPermissionScene3()
    {
        $request = new UpdatePatternRequest([]);
        $logic = new UpdatePattern(new AdminLoginUser([]), $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyOperationPermission');
        $method->setAccessible(true);

        $method->invokeArgs($logic, [new AdminLoginUser(['pid' => 0, 'corp_id' => 10001])]);
        $this->assertTrue(true);
    }

    /**
     * @testdox 加载现金券模板数据
     * @return void
     * @throws ReflectionException
     */
    public function testLoadCouponPatternData()
    {
        $request = new UpdatePatternRequest([]);
        $logic = new UpdatePattern(new AdminLoginUser([]), $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('loadCouponPatternData');
        $method->setAccessible(true);

        $params = [
            'pattern_id' => 1
        ];

        Db::startTrans();
        $method->invokeArgs($logic, [$params['pattern_id']]);
        Db::rollback();
        $sql = Db::getLastSql();

        $expected_sql = sprintf(
            "SELECT `user_id`,`activity_id` FROM `coupon_pattern` WHERE  `id` = %d LIMIT 1",
            $params['pattern_id']
        );

        $this->assertSame($expected_sql, $sql);
    }

    /**
     * @testdox 验证参数的合法性 - 根据ID没有查询出现金券数据
     * @return void
     * @throws ReflectionException
     */
    public function testVerifyParamsLegitimacyScene1()
    {
        $request = new UpdatePatternRequest([]);
        $logic = new UpdatePattern(new AdminLoginUser([]), $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyParamsLegitimacy');
        $method->setAccessible(true);


        $this->expectExceptionMessage("无效现金券ID");
        $this->expectException(RuntimeException::class);
        $this->expectExceptionCode(RuntimeException::CodeBusinessException);


        $params = [
            'patternData' => null,
            'op_user' => new AdminLoginUser(['id' => 1])
        ];

        $method->invokeArgs($logic, [$params['patternData'], $params['op_user']]);
    }

    /**
     * @testdox 验证参数的合法性 - 查询到的现金券不是操作员创建的
     * @return void
     * @throws ReflectionException
     */
    public function testVerifyParamsLegitimacyScene2()
    {
        $request = new UpdatePatternRequest([]);
        $logic = new UpdatePattern(new AdminLoginUser([]), $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyParamsLegitimacy');
        $method->setAccessible(true);


        $this->expectExceptionMessage("无效现金券ID");
        $this->expectException(RuntimeException::class);
        $this->expectExceptionCode(RuntimeException::CodeBusinessException);


        $params = [
            'patternData' => ['user_id' => 2],
            'op_user' => new AdminLoginUser(['id' => 1])
        ];

        $method->invokeArgs($logic, [$params['patternData'], $params['op_user']]);
    }

    /**
     * @testdox 验证参数的合法性 - 查询到的现金券属于操作员创建的
     * @return void
     * @throws ReflectionException
     */
    public function testVerifyParamsLegitimacyScene3()
    {
        $request = new UpdatePatternRequest([]);
        $logic = new UpdatePattern(new AdminLoginUser([]), $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyParamsLegitimacy');
        $method->setAccessible(true);


        $params = [
            'patternData' => ['user_id' => 1],
            'op_user' => new AdminLoginUser(['id' => 1])
        ];

        $method->invokeArgs($logic, [$params['patternData'], $params['op_user']]);

        $this->assertTrue(true);
    }

    /**
     * @testdox 加载优惠券活动数据
     * @return void
     * @throws ReflectionException
     */
    public function testLoadActivityData()
    {
        $request = new UpdatePatternRequest([]);
        $logic = new UpdatePattern(new AdminLoginUser([]), $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('loadActivityData');
        $method->setAccessible(true);

        $params = [
            'activity_id' => 1,
        ];

        $method->invokeArgs($logic, [$params['activity_id']]);
        $sql = Db::getLastSql();

        $expected_sql = sprintf(
            "SELECT `status`,`is_del` FROM `coupon_activity` WHERE  `id` = %d LIMIT 1",
            $params['activity_id']
        );

        $this->assertSame($expected_sql, $sql);
    }

    /**
     * @testdox 验证活动是否能操作 - 不能够操作的场景：活动已被删除
     * @return void
     * @throws ReflectionException
     */
    public function testVerifyActivityIsCanOperationScene1()
    {
        $request = new UpdatePatternRequest([]);
        $logic = new UpdatePattern(new AdminLoginUser([]), $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyActivityIsCanOperation');
        $method->setAccessible(true);

        $params = [
            'activity_data' => [
                'status' => CouponActivity::StatusCreating,
                'is_del' => CouponActivity::IsDelYes
            ],
        ];


        // 断言异常
        $this->expectExceptionMessage('关联的活动已被删除，无法更新现金券!');
        $this->expectException(RuntimeException::class);
        $this->expectExceptionCode(RuntimeException::CodeBusinessException);

        // 执行方法
        $method->invokeArgs($logic, [$params['activity_data']]);
    }

    /**
     * @testdox 验证活动是否能操作 - 不能够操作的场景：活动状态不是处于"创建中"
     * @return void
     * @throws ReflectionException
     */
    public function testVerifyActivityIsCanOperationScene2()
    {
        $request = new UpdatePatternRequest([]);
        $logic = new UpdatePattern(new AdminLoginUser([]), $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyActivityIsCanOperation');
        $method->setAccessible(true);

        $params = [
            'activity_data' => [
                'status' => CouponActivity::StatusStop,
                'is_del' => CouponActivity::IsDelNot
            ],
        ];


        // 断言异常
        $this->expectExceptionMessage('只有当关联的活动状态处于"创建中"时才允许更新现金券!');
        $this->expectException(RuntimeException::class);
        $this->expectExceptionCode(RuntimeException::CodeBusinessException);

        // 执行方法
        $method->invokeArgs($logic, [$params['activity_data']]);
    }

    /**
     * @testdox 验证活动是否能操作 - 能够操作的场景：活动未被删除，状态也刚好处于"创建中"
     * @return void
     * @throws ReflectionException
     */
    public function testVerifyActivityIsCanOperationScene3()
    {
        $request = new UpdatePatternRequest([]);
        $logic = new UpdatePattern(new AdminLoginUser([]), $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyActivityIsCanOperation');
        $method->setAccessible(true);

        $params = [
            'activity_data' => [
                'status' => CouponActivity::StatusCreating,
                'is_del' => CouponActivity::IsDelNot
            ],
        ];

        // 执行方法
        $method->invokeArgs($logic, [$params['activity_data']]);

        $this->assertTrue(true);
    }

    public function testUpdateCouponPattern()
    {
        $request = new UpdatePatternRequest([]);
        $logic = new UpdatePattern(new AdminLoginUser([]), $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('updateCouponPattern');
        $method->setAccessible(true);

        $params = [
            'pattern_id' => 1,
            'pattern_name' => '现金券新名称',
            'par_value' => 100,
            'usage_amount' => 200
        ];

        // 执行方法
        Db::startTrans();
        $method->invokeArgs($logic, [$params['pattern_id'], $params['pattern_name'], $params['par_value'], $params['usage_amount']]);
        Db::rollback();
        $sql = Db::getLastSql();

        $expected_sql = sprintf(
            "UPDATE `coupon_pattern`  SET `name` = '%s' , `par_value` = %d , `usage_amount` = %d  WHERE  `id` = %d",
            $params['pattern_name'], $params['par_value'], $params['usage_amount'], $params['pattern_id']
        );

        $this->assertSame($expected_sql, $sql);
    }
}