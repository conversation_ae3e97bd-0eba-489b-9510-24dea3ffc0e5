<?php

namespace tests\unit;

use app\common\cache\redis\entity\AdminLoginUser;
use app\common\lib\exception\RuntimeException;
use app\common\lib\VerifyData;
use app\common\logic\admin\coupon_activity\DeletePattern;
use app\common\logic\admin\entity\DeletePatternRequest;
use app\common\model\CouponActivity;
use PHPUnit\Framework\TestCase;
use ReflectionClass;
use ReflectionException;
use Respect\Validation\Validator as v;
use think\facade\Db;
use think\Request;


class TestDeletePattern extends TestCase
{
    /**
     * @testdox 验证参数 - (pattern_id)现金券ID - 不传递时会验证不通过。
     */
    public function testVerifyParamsActivityIdScene1()
    {
        $params = [];

        $this->expectExceptionMessage('现金券ID 不能是可选的');

        v::input($params, VerifyData::coupon_activity([
            'pattern_id',
        ]));
    }

    /**
     * @testdox 验证参数 - (pattern_id)现金券ID - 传递非数值时验证不通过。
     */
    public function testVerifyParamsActivityIdScene2()
    {
        $params = [
            'pattern_id' => '1'
        ];

        $this->expectExceptionMessage('现金券ID 必须是integer类型');

        v::input($params, VerifyData::coupon_activity([
            'pattern_id',
        ]));
    }

    /**
     * @testdox 验证参数 - (pattern_id)现金券ID - 传递低于1的数值时会验证不通过。
     */
    public function testVerifyParamsActivityIdScene3()
    {
        $params = [
            'pattern_id' => -1
        ];

        $this->expectExceptionMessage('现金券ID 必须大于或等于 1');

        v::input($params, VerifyData::coupon_activity([
            'pattern_id',
        ]));
    }

    /**
     * @testdox 验证参数 - (pattern_id)现金券ID - 传递符合预期的数值，验证通过。
     */
    public function testVerifyParamsActivityIdScene4()
    {
        $params = [
            'pattern_id' => 1
        ];

        $verify_params = v::input($params, VerifyData::coupon_activity([
            'pattern_id',
        ]));

        $this->assertSame($params, $verify_params);
    }

    /**
     * @testdox 创建删除现金券请求对象
     * @return void
     */
    public function testCreateDeletePatternRequest()
    {
        $params = [
            'pattern_id' => 1
        ];
        $request = new DeletePatternRequest($params);
        $this->assertSame($params['pattern_id'], $request->pattern_id);
        $this->assertSame($params, $request->toArray());
    }

    /**
     * @testdox 验证操作权限 - 以超级管理员的身份操作，会验证不通过。
     */
    public function testVerifyOperationPermissionScene1()
    {
        $request = new DeletePatternRequest([]);
        $logic = new DeletePattern(new AdminLoginUser([]), $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyOperationPermission');
        $method->setAccessible(true);

        // 断言执行方法后会抛出异常
        $this->expectException(RuntimeException::class);
        $this->expectExceptionCode(RuntimeException::CodeBusinessException);
        $this->expectExceptionMessage("只允许运营商主账号操作");

        // 执行方法
        $method->invokeArgs($logic, [new AdminLoginUser(['pid' => 0, 'corp_id' => 0])]);
    }

    /**
     * @testdox 验证操作权限 - 以运营商主账号的身份操作，验证会通过。
     */
    public function testVerifyOperationPermissionScene2()
    {
        $request = new DeletePatternRequest([]);
        $logic = new DeletePattern(new AdminLoginUser([]), $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyOperationPermission');
        $method->setAccessible(true);

        // 执行方法
        $result = $method->invokeArgs($logic, [new AdminLoginUser(['pid' => 0, 'corp_id' => 10001])]);
        $this->assertNull($result);
    }

    /**
     * @testdox 验证操作权限 - 以运营商子账号的身份操作，验证会不通过。
     */
    public function testVerifyOperationPermissionScene3()
    {
        $request = new DeletePatternRequest([]);
        $logic = new DeletePattern(new AdminLoginUser([]), $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyOperationPermission');
        $method->setAccessible(true);

        // 断言执行方法后会抛出异常
        $this->expectException(RuntimeException::class);
        $this->expectExceptionCode(RuntimeException::CodeBusinessException);
        $this->expectExceptionMessage("只允许运营商主账号操作");

        // 执行方法
        $method->invokeArgs($logic, [new AdminLoginUser(['pid' => 2, 'corp_id' => 10001])]);
    }


    /**
     * @testdox 验证数据权限 - 当没有查询到活动数据时，会验证不通过。
     */
    public function testVerifyDataPermissionsScene1()
    {
        $request = new DeletePatternRequest([]);
        $logic = new DeletePattern(new AdminLoginUser([]), $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyDataPermissions');
        $method->setAccessible(true);

        // 断言执行方法后会抛出异常
        $this->expectException(RuntimeException::class);
        $this->expectExceptionCode(RuntimeException::CodeBusinessException);
        $this->expectExceptionMessage("无效的现金券ID");

        // 执行方法
        $method->invokeArgs($logic, [new AdminLoginUser([]), null]);
    }

    /**
     * @testdox 验证数据权限 - 当活动的创建人与操作删除的人不是同一账号时，会验证不通过。
     */
    public function testVerifyDataPermissionsScene2()
    {
        $request = new DeletePatternRequest([]);
        $logic = new DeletePattern(new AdminLoginUser([]), $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyDataPermissions');
        $method->setAccessible(true);

        // 断言执行方法后会抛出异常
        $this->expectException(RuntimeException::class);
        $this->expectExceptionCode(RuntimeException::CodeBusinessException);
        $this->expectExceptionMessage("无效的现金券ID");

        // 执行方法
        $method->invokeArgs($logic, [new AdminLoginUser(['id' => 5]), ['is_del' => CouponActivity::IsDelNot, 'user_id' => 2]]);
    }

    /**
     * @testdox 验证数据权限 - 当关联的活动存在，并且创建的人与操作删除的人是同一个时，验证才会通过。
     */
    public function testVerifyDataPermissionsScene4()
    {
        $request = new DeletePatternRequest([]);
        $logic = new DeletePattern(new AdminLoginUser([]), $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyDataPermissions');
        $method->setAccessible(true);

        // 执行方法
        $result = $method->invokeArgs($logic, [new AdminLoginUser(['id' => 2]), ['is_del' => CouponActivity::IsDelNot, 'user_id' => 2]]);

        $this->assertNull($result);
    }

    /**
     * @testdox 验证是否能够删除 - 当活动状态处于"创建中"时，允许删除现金券。
     * @return void
     * @throws ReflectionException
     */
    public function testVerifyIsCanOperationScene1()
    {
        $request = new DeletePatternRequest([]);
        $logic = new DeletePattern(new AdminLoginUser([]), $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyIsCanOperation');
        $method->setAccessible(true);

        // 执行方法
        $result = $method->invokeArgs($logic, [['status' => CouponActivity::StatusCreating]]);

        $this->assertNull($result);
    }

    /**
     * @testdox 验证是否能够删除 - 当活动状态处于"审核中"时，不允许删除现金券。
     * @return void
     * @throws ReflectionException
     */
    public function testVerifyIsCanOperationScene2()
    {
        $request = new DeletePatternRequest([]);
        $logic = new DeletePattern(new AdminLoginUser([]), $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyIsCanOperation');
        $method->setAccessible(true);

        $this->expectException(RuntimeException::class);
        $this->expectExceptionCode(RuntimeException::CodeBusinessException);
        $this->expectExceptionMessage('现金券只有在活动状态处于"创建中"时，才允许删除。');

        // 执行方法
        $method->invokeArgs($logic, [['status' => CouponActivity::StatusReview]]);
    }

    /**
     * @testdox 验证是否能够删除 - 当活动状态处于"发放中"时，不允许删除现金券。
     * @return void
     * @throws ReflectionException
     */
    public function testVerifyIsCanOperationScene3()
    {
        $request = new DeletePatternRequest([]);
        $logic = new DeletePattern(new AdminLoginUser([]), $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyIsCanOperation');
        $method->setAccessible(true);

        $this->expectException(RuntimeException::class);
        $this->expectExceptionCode(RuntimeException::CodeBusinessException);
        $this->expectExceptionMessage('现金券只有在活动状态处于"创建中"时，才允许删除。');

        // 执行方法
        $method->invokeArgs($logic, [['status' => CouponActivity::StatusGrant]]);
    }

    /**
     * @testdox 验证是否能够删除 - 当活动状态处于"已结束"时，不允许删除现金券。
     * @return void
     * @throws ReflectionException
     */
    public function testVerifyIsCanOperationScene4()
    {
        $request = new DeletePatternRequest([]);
        $logic = new DeletePattern(new AdminLoginUser([]), $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyIsCanOperation');
        $method->setAccessible(true);

        $this->expectException(RuntimeException::class);
        $this->expectExceptionCode(RuntimeException::CodeBusinessException);
        $this->expectExceptionMessage('现金券只有在活动状态处于"创建中"时，才允许删除。');

        // 执行方法
        $method->invokeArgs($logic, [['status' => CouponActivity::StatusEnd]]);
    }

    /**
     * @testdox 验证是否能够删除 - 当活动状态处于"已停用"时，不允许删除现金券。
     * @return void
     * @throws ReflectionException
     */
    public function testVerifyIsCanOperationScene5()
    {
        $request = new DeletePatternRequest([]);
        $logic = new DeletePattern(new AdminLoginUser([]), $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyIsCanOperation');
        $method->setAccessible(true);

        $this->expectException(RuntimeException::class);
        $this->expectExceptionCode(RuntimeException::CodeBusinessException);
        $this->expectExceptionMessage('现金券只有在活动状态处于"创建中"时，才允许删除。');

        // 执行方法
        $method->invokeArgs($logic, [['status' => CouponActivity::StatusStop]]);
    }

    /**
     * @testdox 验证是否能够删除 - 当活动状态处于"停用审核中"时，不允许删除。
     * @return void
     * @throws ReflectionException
     */
    public function testVerifyIsCanOperationScene6()
    {
        $request = new DeletePatternRequest([]);
        $logic = new DeletePattern(new AdminLoginUser([]), $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('verifyIsCanOperation');
        $method->setAccessible(true);

        $this->expectException(RuntimeException::class);
        $this->expectExceptionCode(RuntimeException::CodeBusinessException);
        $this->expectExceptionMessage('现金券只有在活动状态处于"创建中"时，才允许删除。');

        // 执行方法
        $method->invokeArgs($logic, [['status' => CouponActivity::StatusReviewStop]]);
    }

    /**
     * @testdox 记录操作日志
     */
    public function testAddOperationLog()
    {
        $request = new DeletePatternRequest([]);
        $logic = new DeletePattern(new AdminLoginUser([]), $request, new Request());

        // 通过反射API获取对象protocol方法的权限
        $reflectionClass = new ReflectionClass($logic);
        $method = $reflectionClass->getMethod('addOperationLog');
        $method->setAccessible(true);

        $params = [
            'corp_id' => 10001,
            'user_id' => 2,
            'op_interface' => '/admin/coupon_activity/delete_pattern',
            'op_content' => [
                'pattern_id' => 1,
            ]
        ];

        // 执行方法
        Db::startTrans();
        $method->invokeArgs($logic, [
            $params['corp_id'], $params['user_id'],
            $params['op_interface'], $params['op_content']
        ]);
        Db::rollback();
        $sql = Db::getLastSql();

        // 断言
        $expected_sql = sprintf(
            "INSERT INTO `coupon_oplog` SET `corp_id` = '%d' , `user_id` = '%d' , `user_type` = 0 , `op_interface` = '%s' , `op_content` = '%s' , `op_time` = '%s'",
            $params['corp_id'], $params['user_id'], $params['op_interface'], addslashes(json_encode($params['op_content'])), date('Y-m-d H:i:s')
        );

        $this->assertSame($expected_sql, $sql);
    }


}