<?php

namespace tests\admin_api;

use app\admin\controller\CouponActivity;
use app\common\cache\redis\entity\AdminLoginUser;
use app\common\lib\QrCode;
use app\common\model\AdminUsers;
use app\common\logic\admin\coupon_activity\ActivityList;
use app\common\model\CouponActivity as CouponActivityModel;

class TestCouponActivity extends BaseApi
{
    protected array $create_activity_params = [
        'name' => '0925',
        'grant_max_count' => 10000,
        'effective_days' => 30,
        'start_time' => '2024-09-25 08:00:00',
        'end_time' => '2024-09-30 08:00:00',
        'station_ids' => [1013, 1014],
    ];

    protected array $activity_list_params = [
        'page' => 1,
        'limit' => 10,
        'sort_field' => ActivityList::SortFieldsID,
        'sort_type' => ActivityList::SortTypeDesc
    ];

    protected array $update_activity_params = [
        'id' => 0,
        'name' => '0925',
        'grant_max_count' => 10000,
        'effective_days' => 30,
        'start_time' => '2024-09-25 08:00:00',
        'end_time' => '2024-09-30 08:00:00',
        'station_ids' => [1013, 1015],
    ];

    protected array $add_pattern_params = [
        'activity_id' => 0,
        'pattern_name' => '服务费满10元减5元',
        'par_value' => 50,
        'usage_amount' => 100,
    ];

    public function test_create_activity()
    {
        $adminLoginUser = new AdminLoginUser((new AdminUsers())->getUserData(15));
        $app = $this->init_post_request($this->create_activity_params, $adminLoginUser);

        $response = (new CouponActivity($app))->create_activity();
        $this->assertSame(200, $response->getCode());
        $this->assertIsArray($response->getData());
        $responseBody = $response->getData();
        $this->assertSame(200, $responseBody['code']);
        // new_id 大于 0
        $this->assertGreaterThan(0, $responseBody['data']['new_id']);
        $this->update_activity_params['id'] = $responseBody['data']['new_id'];
        return $responseBody['data']['new_id'];
    }

    /**
     * @depends test_create_activity
     */
    public function test_sort_list_info()
    {
        $adminLoginUser = new AdminLoginUser((new AdminUsers())->getUserData(15));
        $app = $this->init_post_request([], $adminLoginUser);

        $response = (new CouponActivity($app))->sort_list_info();
        $this->assertSame(200, $response->getCode());
        $this->assertIsArray($response->getData());
        $responseBody = $response->getData();
        $this->assertSame("成功", $responseBody['msg']);
        $this->assertSame(200, $responseBody['code']);
        $this->assertIsArray($responseBody['data']);
        $this->assertArrayHasKey("order_name", $responseBody['data']);
        $this->assertArrayHasKey("order_type", $responseBody['data']);
        $this->assertArrayHasKey("sort_field_options", $responseBody['data']);
        foreach ($responseBody['data']['sort_field_options'] as $option) {
            $this->assertIsArray($option);
            $this->assertArrayHasKey("value", $option);
            $this->assertIsInt($option['value']);
            $this->assertArrayHasKey("label", $option);
            $this->assertIsString($option['label']);
        }
        $this->assertArrayHasKey("sort_type_options", $responseBody['data']);
        foreach ($responseBody['data']['sort_type_options'] as $option) {
            $this->assertIsArray($option);
            $this->assertArrayHasKey("value", $option);
            $this->assertIsInt($option['value']);
            $this->assertArrayHasKey("label", $option);
            $this->assertIsString($option['label']);
        }

        return $responseBody['data'];
    }

    /**
     * @testdox 新建任务后查看列表数据是否增加一条数据
     * @depends test_sort_list_info
     */
    public function test_list(array $sort_list_info)
    {
        $params = [
            'page' => 1,
            'limit' => 10,
            'sort_field' => $sort_list_info['order_name'],
            'sort_type' => $sort_list_info['order_type']
        ];
        $adminLoginUser = new AdminLoginUser((new AdminUsers())->getUserData(15));
        $app = $this->init_post_request($params, $adminLoginUser);

        $response = (new CouponActivity($app))->list();
        $this->assertSame(200, $response->getCode());
        $this->assertIsArray($response->getData());
        $responseBody = $response->getData();
        $this->assertSame("成功", $responseBody['msg']);
        $this->assertSame(200, $responseBody['code']);
        $list = $responseBody['data'];
        $this->assertSame(1, $list['total']);
        $this->assertSame($params['limit'], $list['per_page']);
        $this->assertSame($params['page'], $list['current_page']);
        $this->assertSame(1, $list['last_page']);
        $this->assertIsArray($list['data']);
        foreach ($list['data'] as $value) {
            $this->assertIsArray($value);
            $this->assertArrayHasKey('id', $value);
            $this->assertIsInt($value['id']);
            $this->assertArrayHasKey('name', $value);
            $this->assertIsString($value['name']);
            $this->assertArrayHasKey('status', $value);
            $this->assertIsInt($value['status']);
            $this->assertArrayHasKey('grant_max_count', $value);
            $this->assertIsInt($value['grant_max_count']);
            $this->assertArrayHasKey('grant_num', $value);
            $this->assertIsInt($value['grant_num']);
            $this->assertArrayHasKey('qcode_url', $value);
            $this->assertIsString($value['qcode_url']);
            $this->assertArrayHasKey('start_time', $value);
            $this->assertIsString($value['start_time']);
            $this->assertArrayHasKey('end_time', $value);
            $this->assertIsString($value['end_time']);
            $this->assertArrayHasKey('create_time', $value);
            $this->assertIsString($value['create_time']);
            $this->assertArrayHasKey('update_time', $value);
            $this->assertNull($value['update_time']);
        }

        return $list['data'];
    }

    /**
     * @depends test_list
     */
    public function test_info(array $list_data)
    {
        $this->assertArrayHasKey('id', $list_data[0]);
        $this->assertIsInt($list_data[0]['id']);
        $this->assertGreaterThan(0, $list_data[0]['id']);

        $params = [
            'activity_id' => $list_data[0]['id']
        ];
        $adminLoginUser = new AdminLoginUser((new AdminUsers())->getUserData(15));
        $app = $this->init_post_request($params, $adminLoginUser);

        $response = (new CouponActivity($app))->info();
        $this->assertSame(200, $response->getCode());
        $this->assertIsArray($response->getData());
        $responseBody = $response->getData();
        $this->assertSame("成功", $responseBody['msg']);
        $this->assertSame(200, $responseBody['code']);
        $this->assertIsArray($responseBody['data']);
        $this->assertArrayHasKey("id", $responseBody['data']);
        $this->assertSame($list_data[0]['id'], $responseBody['data']['id']);
        $this->assertArrayHasKey("name", $responseBody['data']);
        $this->assertSame($this->create_activity_params['name'], $responseBody['data']["name"]);
        $this->assertArrayHasKey("status", $responseBody['data']);
        $this->assertSame(CouponActivityModel::StatusCreating, $responseBody['data']["status"]);
        $this->assertArrayHasKey("grant_max_count", $responseBody['data']);
        $this->assertSame($this->create_activity_params['grant_max_count'], $responseBody['data']['grant_max_count']);
        $this->assertArrayHasKey("grant_num", $responseBody['data']);
        $this->assertSame(0, $responseBody['data']['grant_num']);
        $this->assertArrayHasKey("effective_days", $responseBody['data']);
        $this->assertSame($this->create_activity_params['effective_days'], $responseBody['data']['effective_days']);
        $this->assertArrayHasKey("qcode_url", $responseBody['data']);
        $this->assertSame("", $responseBody['data']['qcode_url']);
        $this->assertArrayHasKey("start_time", $responseBody['data']);
        $this->assertSame($this->create_activity_params['start_time'], $responseBody['data']['start_time']);
        $this->assertArrayHasKey("end_time", $responseBody['data']);
        $this->assertSame($this->create_activity_params['end_time'], $responseBody['data']['end_time']);
        $this->assertArrayHasKey("create_time", $responseBody['data']);
        $this->assertArrayHasKey("update_time", $responseBody['data']);
        $this->assertSame(null, $responseBody['data']['update_time']);

        return $responseBody['data'];
    }

    /**
     * @depends test_info
     */
    public function test_update_activity(array $activity_data)
    {
        $this->assertArrayHasKey('id', $activity_data);
        $this->assertIsInt($activity_data['id']);
        $this->assertGreaterThan(0, $activity_data['id']);

        $this->update_activity_params['id'] = $activity_data['id'];
        $adminLoginUser = new AdminLoginUser((new AdminUsers())->getUserData(15));
        $app = $this->init_post_request($this->update_activity_params, $adminLoginUser);

        $response = (new CouponActivity($app))->update_activity();
        $this->assertSame(200, $response->getCode());
        $this->assertIsArray($response->getData());
        $responseBody = $response->getData();
        $this->assertSame(200, $responseBody['code']);

        return $activity_data;
    }


    /**
     * @testdox 添加现金券
     * @depends test_update_activity
     */
    public function test_add_pattern(array $activity_data)
    {
        $this->assertArrayHasKey('id', $activity_data);
        $this->assertIsInt($activity_data['id']);
        $this->assertGreaterThan(0, $activity_data['id']);

        $this->add_pattern_params['activity_id'] = $activity_data['id'];
        $adminLoginUser = new AdminLoginUser((new AdminUsers())->getUserData(15));
        $app = $this->init_post_request($this->add_pattern_params, $adminLoginUser);

        $response = (new CouponActivity($app))->add_pattern();
        $this->assertSame(200, $response->getCode());
        $this->assertIsArray($response->getData());
        $responseBody = $response->getData();
        $this->assertSame('成功', $responseBody['msg']);
        $this->assertSame(200, $responseBody['code']);
        $this->assertIsArray($responseBody['data']);
        $this->assertArrayHasKey('new_id', $responseBody['data']);
        $this->assertGreaterThan(0, $responseBody['data']['new_id']);

        return [
            'activity_data' => $activity_data,
            'new_pattern_id' => $responseBody['data']['new_id']
        ];
    }

    /**
     * @testdox 删除现金券
     * @depends test_add_pattern
     */
    public function test_delete_pattern(array $params)
    {
        $adminLoginUser = new AdminLoginUser((new AdminUsers())->getUserData(15));
        $app = $this->init_post_request([
            'pattern_id' => $params['new_pattern_id']
        ], $adminLoginUser);

        $response = (new CouponActivity($app))->delete_pattern();
        $this->assertSame(200, $response->getCode());
        $this->assertIsArray($response->getData());
        $responseBody = $response->getData();
        var_export($responseBody);
        $this->assertSame('成功', $responseBody['msg']);
        $this->assertSame(200, $responseBody['code']);


        return [
            'activity_data' => $params['activity_data'],
        ];
    }

    /**
     * @testdox 删除现金券后再添加现金券
     * @depends test_delete_pattern
     */
    public function test_delete_after_add_pattern(array $params)
    {
        $this->add_pattern_params['activity_id'] = $params['activity_data']['id'];
        $adminLoginUser = new AdminLoginUser((new AdminUsers())->getUserData(15));
        $app = $this->init_post_request($this->add_pattern_params, $adminLoginUser);

        $response = (new CouponActivity($app))->add_pattern();
        $this->assertSame(200, $response->getCode());
        $this->assertIsArray($response->getData());
        $responseBody = $response->getData();
        $this->assertSame('成功', $responseBody['msg']);
        $this->assertSame(200, $responseBody['code']);
        $this->assertIsArray($responseBody['data']);
        $this->assertArrayHasKey('new_id', $responseBody['data']);
        $this->assertGreaterThan(0, $responseBody['data']['new_id']);

        return [
            'activity_data' => $params['activity_data'],
            'new_pattern_id' => $responseBody['data']['new_id']
        ];
    }

    /**
     * @testdox 查询现金券详情
     * @depends test_delete_after_add_pattern
     */
    public function test_pattern_info(array $params)
    {
        $this->assertArrayHasKey('new_pattern_id', $params);
        $new_pattern_id = $params['new_pattern_id'];
        $adminLoginUser = new AdminLoginUser((new AdminUsers())->getUserData(15));
        $app = $this->init_post_request([
            'pattern_id' => $new_pattern_id
        ], $adminLoginUser);

        $response = (new CouponActivity($app))->pattern_info();
        $this->assertSame(200, $response->getCode());
        $this->assertIsArray($response->getData());
        $responseBody = $response->getData();
        $this->assertSame("成功", $responseBody['msg']);
        $this->assertSame(200, $responseBody['code']);
        $this->assertIsArray($responseBody['data']);
        $this->assertArrayHasKey('id', $responseBody['data']);
        $this->assertSame($new_pattern_id, $responseBody['data']['id']);
        $this->assertArrayHasKey('name', $responseBody['data']);
        $this->assertIsString($responseBody['data']['name']);
        $this->assertArrayHasKey('par_value', $responseBody['data']);
        $this->assertIsInt($responseBody['data']['par_value']);
        $this->assertArrayHasKey('usage_amount', $responseBody['data']);
        $this->assertIsInt($responseBody['data']['usage_amount']);
        $this->assertArrayHasKey('create_time', $responseBody['data']);
        $this->assertIsString($responseBody['data']['create_time']);


        return [
            'activity_data' => $params['activity_data'],
            'pattern_data' => $responseBody['data']
        ];
    }

    /**
     * @testdox 更新现金券
     * @depends test_pattern_info
     */
    public function test_update_pattern(array $params)
    {
        $this->assertArrayHasKey('pattern_data', $params);
        $pattern_data = $params['pattern_data'];

        $update_params = [
            'pattern_id' => $pattern_data['id'],
            'pattern_name' => $pattern_data['name'] . '-更新',
            'par_value' => $pattern_data['par_value'],
            'usage_amount' => $pattern_data['usage_amount'],
        ];

        $adminLoginUser = new AdminLoginUser((new AdminUsers())->getUserData(15));
        $app = $this->init_post_request($update_params, $adminLoginUser);

        $response = (new CouponActivity($app))->update_pattern();

        return [
            'new_pattern_info' => $update_params,
            'activity_data' => $params['activity_data']
        ];
    }

    /**
     * @testdox 更新之后查询现金券详情
     * @depends test_update_pattern
     */
    public function test_update_after_pattern_info(array $params)
    {
        $this->assertArrayHasKey('new_pattern_info', $params);
        $new_pattern_info = $params['new_pattern_info'];

        $adminLoginUser = new AdminLoginUser((new AdminUsers())->getUserData(15));
        $app = $this->init_post_request([
            'pattern_id' => $new_pattern_info['pattern_id']
        ], $adminLoginUser);

        $response = (new CouponActivity($app))->pattern_info();

        $this->assertSame(200, $response->getCode());
        $this->assertIsArray($response->getData());
        $responseBody = $response->getData();
        $this->assertSame("成功", $responseBody['msg']);
        $this->assertSame(200, $responseBody['code']);
        $this->assertIsArray($responseBody['data']);
        $this->assertSame($new_pattern_info['pattern_id'], $responseBody['data']['id']);
        $this->assertSame($new_pattern_info['pattern_name'], $responseBody['data']['name']);
        $this->assertSame($new_pattern_info['par_value'], $responseBody['data']['par_value']);
        $this->assertSame($new_pattern_info['usage_amount'], $responseBody['data']['usage_amount']);

        return [
            'activity_data' => $params['activity_data'],
            'pattern_data' => $responseBody['data']
        ];
    }

    /**
     * @testdox 查询现金券列表的排序信息
     * @depends test_update_after_pattern_info
     */
    public function test_pattern_sort_list_info(array $params)
    {
        $this->assertArrayHasKey('activity_data', $params);
        $activity_data = $params['activity_data'];

        $adminLoginUser = new AdminLoginUser((new AdminUsers())->getUserData(15));
        $app = $this->init_post_request([], $adminLoginUser);

        $response = (new CouponActivity($app))->pattern_sort_list_info();
        $this->assertSame(200, $response->getCode());
        $this->assertIsArray($response->getData());
        $responseBody = $response->getData();
        $this->assertSame("成功", $responseBody['msg']);
        $this->assertSame(200, $responseBody['code']);
        $this->assertIsArray($responseBody['data']);
        $this->assertArrayHasKey("order_name", $responseBody['data']);
        $this->assertArrayHasKey("order_type", $responseBody['data']);
        $this->assertArrayHasKey("sort_field_options", $responseBody['data']);
        foreach ($responseBody['data']['sort_field_options'] as $option) {
            $this->assertIsArray($option);
            $this->assertArrayHasKey("value", $option);
            $this->assertIsInt($option['value']);
            $this->assertArrayHasKey("label", $option);
            $this->assertIsString($option['label']);
        }
        $this->assertArrayHasKey("sort_type_options", $responseBody['data']);
        foreach ($responseBody['data']['sort_type_options'] as $option) {
            $this->assertIsArray($option);
            $this->assertArrayHasKey("value", $option);
            $this->assertIsInt($option['value']);
            $this->assertArrayHasKey("label", $option);
            $this->assertIsString($option['label']);
        }

        return [
            'activity_data' => $activity_data,
            'sort_options' => $responseBody['data']
        ];
    }


    /**
     * @testdox 查询现金券列表
     * @depends test_pattern_sort_list_info
     */
    public function test_pattern_list(array $params)
    {
        $this->assertArrayHasKey('activity_data', $params);
        $this->assertArrayHasKey('sort_options', $params);
        $activity_data = $params['activity_data'];
        $sort_options = $params['sort_options'];
        $this->assertArrayHasKey('id', $activity_data);
        $this->assertIsInt($activity_data['id']);
        $this->assertGreaterThan(0, $activity_data['id']);


        $prams = [
            'activity_id' => $activity_data['id'],
            'page' => 1,
            'limit' => 10,
            'sort_field' => $sort_options['order_name'],
            'sort_type' => $sort_options['order_type']
        ];
        $adminLoginUser = new AdminLoginUser((new AdminUsers())->getUserData(15));
        $app = $this->init_post_request($prams, $adminLoginUser);

        $response = (new CouponActivity($app))->pattern_list();
        $this->assertSame(200, $response->getCode());
        $this->assertIsArray($response->getData());
        $responseBody = $response->getData();
        $this->assertSame("成功", $responseBody['msg']);
        $this->assertSame(200, $responseBody['code']);
        $this->assertIsArray($responseBody['data']);
        $listData = $responseBody['data'];
        $this->assertSame(1, $listData['total']);
        $this->assertSame($prams['limit'], $listData['per_page']);
        $this->assertSame($prams['page'], $listData['current_page']);
        $this->assertSame(1, $listData['last_page']);
        $this->assertIsArray($listData['data']);
        $this->assertSame(1, count($listData['data']));

        foreach ($listData['data'] as $value) {
            $this->assertArrayHasKey('id', $value);
            $this->assertIsInt($value['id']);
            $this->assertArrayHasKey('name', $value);
            $this->assertIsString($value['name']);
            $this->assertArrayHasKey('par_value', $value);
            $this->assertIsInt($value['par_value']);
            $this->assertArrayHasKey('usage_amount', $value);
            $this->assertIsInt($value['usage_amount']);
            $this->assertArrayHasKey('create_time', $value);
            $this->assertIsString($value['create_time']);
        }

        return [
            'activity_data' => $activity_data,
        ];
    }

    /**
     * @testdox 提交审核
     * @depends test_pattern_list
     */
    public function test_submit_review(array $params)
    {

        $adminLoginUser = new AdminLoginUser((new AdminUsers())->getUserData(15));
        $app = $this->init_post_request([
            'id' => $params['activity_data']['id']
        ], $adminLoginUser);

        $response = (new CouponActivity($app))->submit_review();

        var_export($response);

        return $params;
    }

    /**
     * @testdox 提交审核
     * @depends test_pattern_list
     */
    public function test__review(array $params)
    {

        $adminLoginUser = new AdminLoginUser((new AdminUsers())->getUserData(15));
        $app = $this->init_post_request([
            'id' => $params['activity_data']['id']
        ], $adminLoginUser);

        //QrCode

        $response = (new CouponActivity($app))->submit_review();
    }


    /**
     * @testdox 删除活动
     * @depends test_pattern_list
     */
    public function test_delete(array $activity_data)
    {
        $this->assertArrayHasKey('id', $activity_data);
        $this->assertIsInt($activity_data['id']);
        $this->assertGreaterThan(0, $activity_data['id']);

        $params = [
            'activity_id' => $activity_data['id']
        ];
        $adminLoginUser = new AdminLoginUser((new AdminUsers())->getUserData(15));
        $app = $this->init_post_request($params, $adminLoginUser);

        $response = (new CouponActivity($app))->delete_activity();
        $this->assertSame(200, $response->getCode());
        $this->assertIsArray($response->getData());
        $responseBody = $response->getData();
        $this->assertSame(200, $responseBody['code']);
    }


    /**
     * @testdox 测试删除后，查询列表的查询信息
     * @depends test_delete
     */
    public function test_delete_after_sort_list_info()
    {
        $adminLoginUser = new AdminLoginUser((new AdminUsers())->getUserData(15));
        $app = $this->init_post_request([], $adminLoginUser);

        $response = (new CouponActivity($app))->sort_list_info();
        $this->assertSame(200, $response->getCode());
        $this->assertIsArray($response->getData());
        $responseBody = $response->getData();
        $this->assertSame("成功", $responseBody['msg']);
        $this->assertSame(200, $responseBody['code']);
        $this->assertIsArray($responseBody['data']);
        $this->assertArrayHasKey("order_name", $responseBody['data']);
        $this->assertArrayHasKey("order_type", $responseBody['data']);
        $this->assertArrayHasKey("sort_field_options", $responseBody['data']);
        foreach ($responseBody['data']['sort_field_options'] as $option) {
            $this->assertIsArray($option);
            $this->assertArrayHasKey("value", $option);
            $this->assertIsInt($option['value']);
            $this->assertArrayHasKey("label", $option);
            $this->assertIsString($option['label']);
        }
        $this->assertArrayHasKey("sort_type_options", $responseBody['data']);
        foreach ($responseBody['data']['sort_type_options'] as $option) {
            $this->assertIsArray($option);
            $this->assertArrayHasKey("value", $option);
            $this->assertIsInt($option['value']);
            $this->assertArrayHasKey("label", $option);
            $this->assertIsString($option['label']);
        }

        return $responseBody['data'];
    }

    /**
     * @testdox 测试删除查询列表是否没有数据了
     * @depends test_delete_after_sort_list_info
     */
    public function test_delete_after_list(array $sort_list_info)
    {
        $params = [
            'page' => 1,
            'limit' => 10,
            'sort_field' => $sort_list_info['order_name'],
            'sort_type' => $sort_list_info['order_type']
        ];
        $adminLoginUser = new AdminLoginUser((new AdminUsers())->getUserData(15));
        $app = $this->init_post_request($params, $adminLoginUser);

        $response = (new CouponActivity($app))->list();
        $this->assertSame(200, $response->getCode());
        $this->assertIsArray($response->getData());
        $responseBody = $response->getData();
        $this->assertSame("成功", $responseBody['msg']);
        $this->assertSame(200, $responseBody['code']);
        $list = $responseBody['data'];
        $this->assertSame(0, $list['total']);
        $this->assertSame($params['limit'], $list['per_page']);
        $this->assertSame($params['page'], $list['current_page']);
        $this->assertSame(0, $list['last_page']);
        $this->assertIsArray($list['data']);
        $this->assertSame([], $list['data']);
    }
}