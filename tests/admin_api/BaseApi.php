<?php

namespace tests\admin_api;

use app\common\cache\redis\entity\AdminLoginUser;
use PHPUnit\Framework\TestCase;
use think\App;
use think\Request;

class BaseApi extends TestCase
{
    protected function init_post_request(array $params, AdminLoginUser $adminLoginUser): App
    {
        // 初始化应用
        $app = new App();
        $app->initialize();

        // 初始化请求对象并设置请求参数
        $request = new Request();
        $request->setMethod('POST');
        $request->withPost($params);
        $request->adminLoginUser = $adminLoginUser;
        $postParams = $request->post();
        $this->assertIsArray($postParams);
        $this->assertSame($params, $postParams);

        // 将请求对象绑定到应用中
        $app->bind(Request::class, $request);
        $this->assertInstanceOf(Request::class, $app->request);
        $this->assertIsArray($app->request->post());
        $this->assertSame($params, $app->request->post());

        return $app;
    }


    protected function assertListFields(array $list_data, array $fields_type): void
    {
        $fields_keys = array_keys($fields_type);
        foreach ($list_data as $row) {
            $this->assertSame($fields_keys, array_keys($row));
            foreach ($fields_type as $field => $type) {

                $this->assertTrue(array_key_exists($field, $row));
                $value = $row[$field];
                switch ($type) {
                    case "string":
                        $this->assertIsString($value);
                        break;
                    case "int":
                        $this->assertIsInt($value);
                        break;
                    case "float":
                        $this->assertIsFloat($value);
                        break;
                }
            }
        }
    }
}