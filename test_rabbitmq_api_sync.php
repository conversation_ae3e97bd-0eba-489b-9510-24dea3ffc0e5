<?php
/**
 * RabbitMQ API 同步测试脚本
 * 
 * 用于测试 API 同步队列的 RabbitMQ 实现
 * 运行方式：php test_rabbitmq_api_sync.php
 */

require_once __DIR__ . '/vendor/autoload.php';

use app\common\lib\rabbitmq\Publisher;
use think\App;

// 初始化 ThinkPHP 应用
$app = new App();
$app->initialize();

echo "开始测试 RabbitMQ API 同步队列...\n";

try {
    // 测试发布消息
    $testData = [
        'url' => '/test/api',
        'method' => 'POST',
        'data' => [
            'test_id' => 123,
            'test_name' => 'RabbitMQ 测试'
        ],
        'headers' => [
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer test-token'
        ],
        'key' => 'test_cache_key_' . time(),
        'timestamp' => time()
    ];

    echo "发布测试消息...\n";
    echo "消息内容: " . json_encode($testData, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n";

    $success = Publisher::quickPublish([
        'exchange' => 'api_sync',
        'type' => 'direct',
        'queues' => ['api_sync_queue'],
        'routing_key' => 'api.sync',
        'message' => $testData
    ]);

    if ($success) {
        echo "✅ 消息发布成功！\n";
        echo "请启动消费者来处理消息：php think api:sync-consumer\n";
    } else {
        echo "❌ 消息发布失败！\n";
    }

} catch (Exception $e) {
    echo "❌ 测试失败: " . $e->getMessage() . "\n";
    echo "错误文件: " . $e->getFile() . "\n";
    echo "错误行号: " . $e->getLine() . "\n";
}

echo "测试完成。\n";
