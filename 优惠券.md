```mysql
CREATE TABLE `coupon_activity`
(
    `id`               int(11) UNSIGNED AUTO_INCREMENT                               NOT NULL COMMENT '活动id',
    `name`             varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '活动名称',
    `corp_id`          bigint UNSIGNED                                               NOT NULL COMMENT '活动主办的运营商id',
    `user_id`          int(11) UNSIGNED                                              NOT NULL COMMENT '活动创建人的id',
    `status`           tinyint(1) UNSIGNED                                           NOT NULL DEFAULT 1 COMMENT '活动状态：1-创建中；2-审核中；3-发放中；4-已结束；5-已停用；',
    `grant_rule`       int(11) UNSIGNED                                              NOT NULL COMMENT '发放用户等级规则，对应发放规则表的规则id',
    `grant_max_count`  int(6) UNSIGNED                                               NOT NULL COMMENT '活动中优惠券包发放上限',
    `grant_user_count` tinyint(2) UNSIGNED                                           NOT NULL COMMENT '活动中用户领取次数限定',
    `effective_days`   int(4) UNSIGNED                                               NOT NULL COMMENT '领取后，有效天数',
    `qcode_url`        varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '活动二维码信息；用于生成二维码。',
    `grant_num`        int(6) UNSIGNED                                               NOT NULL DEFAULT 0 COMMENT '活动券已领取份数',
    `is_del`           tinyint(1) UNSIGNED                                           NOT NULL DEFAULT 0 COMMENT '活动是否删除；0-未删除；1-已删除；',
    `start_time`       timestamp(0)                                                  NOT NULL COMMENT '活动开始时间',
    `end_time`         timestamp(0)                                                  NOT NULL COMMENT '活动结束时间',
    `create_time`      timestamp(0)                                                  NOT NULL DEFAULT current_timestamp() COMMENT '活动创建时间',
    `update_time`      timestamp(0)                                                  NULL     DEFAULT NULL COMMENT '活动更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = Dynamic
    COMMENT ='活动表';

CREATE TABLE `coupon_activity_station`
(
    `id`          int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '场站-活动关联表id',
    `station_id`  int(11) UNSIGNED NOT NULL COMMENT '场站id',
    `activity_id` int(11) UNSIGNED NOT NULL COMMENT '活动id',
    `create_time` timestamp(0)     NOT NULL DEFAULT current_timestamp() COMMENT '创建时间',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `as_activity_key` (`activity_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = Dynamic
    COMMENT ='活动与场站的关系表';

CREATE TABLE `coupon_collection`
(
    `id`          int(11)      NOT NULL AUTO_INCREMENT COMMENT '现金券领取表id',
    `pattern_id`  int(11)      NOT NULL COMMENT '现金券模板id',
    `activity_id` int(11)      NOT NULL COMMENT '活动id',
    `user_id`     bigint       NOT NULL COMMENT '领取用户id',
    `status`      tinyint(1)   NOT NULL COMMENT '现金券状态；0-未使用；1-已使用；',
    `create_time` timestamp(0) NOT NULL DEFAULT current_timestamp() COMMENT '领取时间',
    `expire_time` timestamp(0) NOT NULL COMMENT '过期时间',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `pattern_key` (`pattern_id`) USING BTREE,
    INDEX `activity_key` (`activity_id`) USING BTREE,
    INDEX `user_id` (`user_id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = Dynamic COMMENT ="现金券领取表";

CREATE TABLE `coupon_collection_order`
(
    `order_id`      varchar(32)  NOT NULL COMMENT '充电订单ID',
    `collection_id` int(11)      NOT NULL COMMENT '现金券领取表ID',
    `create_time`   timestamp(0) NOT NULL DEFAULT current_timestamp() COMMENT '创建时间',
    UNIQUE KEY (`order_id`, `collection_id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = Dynamic COMMENT ="用户现金券与充电订单的关系";



ALTER TABLE `coupon_collection`
    ADD COLUMN `use_time` timestamp NULL DEFAULT NULL COMMENT '使用时间';

CREATE TABLE `coupon_oplog`
(
    `id`           bigint UNSIGNED                                                NOT NULL AUTO_INCREMENT COMMENT 'coupon操作记录表id',
    `corp_id`      bigint UNSIGNED                                                NOT NULL COMMENT '运营商id',
    `user_id`      bigint UNSIGNED                                                NOT NULL COMMENT '操作人员id',
    `user_type`    tinyint(1) UNSIGNED                                            NOT NULL COMMENT '操作人员类型；0-后台用户；1-小程序用户',
    `op_interface` VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '操作接口',
    `op_content`   VARCHAR(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作内容',
    `op_time`      timestamp(0)                                                   NOT NULL DEFAULT CURRENT_TIMESTAMP() COMMENT '操作时间',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `corp_id` (`corp_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = Dynamic COMMENT ="优惠券操作记录表";

CREATE TABLE `coupon_pattern`
(
    `id`           int(11) UNSIGNED AUTO_INCREMENT                               NOT NULL COMMENT '现金券模板表id',
    `name`         varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '模板名称',
    `activity_id`  int(11) UNSIGNED                                              NOT NULL COMMENT '活动id',
    `par_value`    int(4) UNSIGNED                                               NOT NULL COMMENT '面值，精确到小数点后1位',
    `usage_amount` int(4) UNSIGNED                                               NOT NULL COMMENT '满减金额。精确到小数点后1位',
    `create_time`  timestamp(0)                                                  NOT NULL DEFAULT current_timestamp() COMMENT '模板创建时间',
    `user_id`      int(11) UNSIGNED                                              NOT NULL COMMENT '模板创建人id',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `activity_id` (`activity_id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci
  ROW_FORMAT = Dynamic COMMENT ="现金券模板表";
```

## 增加定时脚本

- 运营平台 - 优惠券活动到期后自动结束
    - 运行命令：cd /www/wwwroot/charge-admin-git-new/ && php think coupon-activity-expire-to-end-status
    - 运行频率：每小时第0分钟运行一次

## 更新完代码后，需要重启的进程有：
- transfer-service-read-queue-consumer