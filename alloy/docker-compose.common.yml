services:
  alloy: &alloy
    extends:
      file: ../docker-compose/common.yml
      service: common
    image: ${DOCKER_BASE_IMAGE_PREFIX}alloy:${ALLOY_VERSION}-${CI_COMMIT_REF_NAME}
    container_name: ${DOCKER_CONTAINER_PREFIX}${CI_SOURCE_NAME}-alloy
    ports:
      - $ALLOY_PORT:$ALLOY_PORT
    environment:
      - DOCKER_HOSTNAME=$DOCKER_HOSTNAME
      - DOCKER_HOST_IPV4=$DOCKER_HOST_IPV4
    mem_limit: 300M
    command: run --server.http.listen-addr=0.0.0.0:$ALLOY_PORT --storage.path=/var/lib/alloy/data /etc/alloy/config.alloy
    healthcheck:
      test: [ "CMD-SHELL", "nc -z 127.0.0.1 $ALLOY_PORT || exit 1" ]
  alloy-build:
    <<: *alloy
    build:
      context: .
      args:
       - ALLOY_SELF_BASE_IMAGE=${DOCKER_BASE_IMAGE_PREFIX}alloy:${ALLOY_VERSION}-${CI_COMMIT_REF_NAME}
    image: ${DOCKER_IMAGE_PREFIX}${CI_SOURCE_NAME}/alloy:${ALLOY_VERSION}-${CI_COMMIT_REF_NAME}
