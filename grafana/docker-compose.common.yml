services:
  grafana: &grafana
    extends:
      file: ../docker-compose/common.yml
      service: common
    image: ${DOCKER_BASE_IMAGE_PREFIX}grafana:${GRAFANA_VERSION}-${CI_COMMIT_REF_NAME}
    container_name: ${DOCKER_CONTAINER_PREFIX}${CI_SOURCE_NAME}-grafana
    mem_limit: 300M
    healthcheck:
      test: [ "CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:3000/api/health || exit 1" ]
  grafana-build:
    build:
      context: .
      args:
        - GRAFANA_SELF_BASE_IMAGE=${DOCKER_BASE_IMAGE_PREFIX}grafana:${GRAFANA_VERSION}-${CI_COMMIT_REF_NAME}
    image: ${DOCKER_IMAGE_PREFIX}${CI_SOURCE_NAME}/grafana:${GRAFANA_VERSION}-${CI_COMMIT_REF_NAME}
    <<: *grafana
