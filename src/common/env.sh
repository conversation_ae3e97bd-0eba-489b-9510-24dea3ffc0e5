#!/bin/sh
#
# Docker运行控制脚本 -- 环境变量
#
# <AUTHOR> <EMAIL>
# Version: 1.0 (2025-04-01 18:16:50)

# 如果没有.env.lock文件，强制使用.env.example
if [ ! -f .env.lock ]; then
    rm -f .env
    cp .env.example .env
fi

# 使用 . 代替 source
if [ -f "./.env" ]; then
    . "./.env"
fi

export CI_COMMIT_REF_NAME=${CI_COMMIT_REF_NAME:-$env}
export DOCKER_HOSTNAME=${DOCKER_HOSTNAME:-$(hostname)}
# DOCKER_HOST_IPV4 获取方式可能在某些最小化环境或无网络环境下失败，保持原样但需注意
export DOCKER_HOST_IPV4=${DOCKER_HOST_IPV4:-$(curl -s4 ifconfig.me)}
# nproc 可能不存在，可以考虑使用 getconf _NPROCESSORS_ONLN 或其他兼容方式，暂时保留
export DOCKER_BUILD_CORES=${DOCKER_BUILD_CORES:-$(nproc)}
export DOCKER_IMAGE_PREFIX=${DOCKER_IMAGE_PREFIX:-''}
export DOCKER_CONTAINER_PREFIX=${DOCKER_CONTAINER_PREFIX:-''}
export DOCKER_BASE_IMAGE_PREFIX=${DOCKER_BASE_IMAGE_PREFIX:-'resc/'}
export DOCKER_BASE_IMAGE_LIST=${DOCKER_BASE_IMAGE_LIST:-''}

# 屏蔽编译基础镜像的警告
export NGINX_VERSION=${NGINX_VERSION:-'1.26'}
export PHP_VERSION=${PHP_VERSION:-'8.1'}
export PYTHON_VERSION=${PYTHON_VERSION:-'3.10.12'}
export GRAFANA_VERSION=${GRAFANA_VERSION:-'11.5.2'}
export LOKI_VERSION=${LOKI_VERSION:-'3.4.2'}
export ALLOY_VERSION=${ALLOY_VERSION:-'v1.7.1'}
export PROMETHEUS_VERSION=${PROMETHEUS_VERSION:-'v2.53.4'}
