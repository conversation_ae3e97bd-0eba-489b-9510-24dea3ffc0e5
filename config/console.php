<?php
// +----------------------------------------------------------------------
// | 控制台配置
// +----------------------------------------------------------------------
use app\command\ActiveAlarmSummary;
use app\command\ApiSyncCommand;
use app\command\EnterpriseWechatMessageQueueConsumer;
use app\command\FullSiteDailyStatistics;
use app\command\OrderPayCommand;

return [
    // 指令定义
    'commands' => [
        'test'                                  => '\app\command\test',
        'test2'                                 => '\app\command\test2',
        'receive_msg'                           => '\app\command\receive_msg',
        'SendHandleTimer'                       => '\app\command\SendHandleTimer',
        'TimeoutHandleJob'                      => '\app\command\TimeoutHandleJob',
        'StartChargingJob'                      => '\app\command\StartChargingJob',
        'StartChargingTimer'                    => '\app\command\StartChargingTimer',
        'DailyStatistics'                       => '\app\command\DailyStatistics',
        'message-push-queue-consumer'           => '\app\command\MessagePushQueueConsumer',
        'shots-status-queue-consumer'           => '\app\command\ShotsStatusQueueConsumer',
        'admin-socket-queue-consumer'           => '\app\command\AdminSocketQueueConsumer',
        'ranking-list-queue-consumer'           => '\app\command\RankingListQueueConsumer',
        'alarm-monitoring-queue-consumer'       => '\app\command\AlarmMonitoringQueueConsumer',
        'print-charging-orders'                 => '\app\command\PrintChargingOrders',
        'user-balance-change-queue-consumer'    => '\app\command\UserBalanceChangeQueueConsumer',
        'detection-shots-status'                => '\app\command\DetectionShotsStatus',
        'transfer-service-client-read'          => '\app\command\TransferServiceClientRead',
        'transfer-service-read-queue-consumer'  => '\app\command\TransferServiceReadQueueConsumer',
        'transfer-service-write-queue-consumer' => '\app\command\TransferServiceWriteQueueConsumer',
        'delete-expire-empr'                    => '\app\command\DeleteExpireElectricityMeterPowerRecord',
        'StatisticsRefundOrderCount'            => '\app\command\StatisticsRefundOrderCount',
        'StatisticsClearingOrder'               => '\app\command\StatisticsClearingOrder',
        'OrderPay'                              => OrderPayCommand::class,
        'api-sync'                              => ApiSyncCommand::class,
        EnterpriseWechatMessageQueueConsumer::COMMAND => EnterpriseWechatMessageQueueConsumer::class,
        ActiveAlarmSummary::COMMAND             => ActiveAlarmSummary::class,
        FullSiteDailyStatistics::COMMAND        => FullSiteDailyStatistics::class
    ],
];
